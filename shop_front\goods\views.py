from django.shortcuts import render, get_object_or_404, redirect
from django.core.paginator import Paginator
from django.contrib.auth.decorators import user_passes_test
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from .models import Product, Category, ProductImage
from account.models import Favorite
import os
import json
from django.db.models import Count

def product_list(request):
    """商品列表视图 - 显示所有上架商品，包括后台新添加的"""
    # 显示所有上架的商品，确保分类也是激活状态
    products = Product.objects.filter(
        is_active=True,
        category__is_active=True
    ).select_related('category').annotate(
        favorite_count=Count('favorited_by')
    ).order_by('-id')

    # 分类筛选
    category_id = request.GET.get('category')
    if category_id:
        try:
            category = Category.objects.get(id=category_id, is_active=True)
            products = products.filter(category=category)
        except Category.DoesNotExist:
            pass

    # 搜索功能
    search = request.GET.get('search', '').strip()
    if search:
        products = products.filter(name__icontains=search)

    # 排序功能
    sort = request.GET.get('sort', '-id')
    valid_sorts = ['-id', 'id', '-price', 'price', '-sales', 'sales', '-updated_time', '-favorite_count', 'favorite_count']
    if sort in valid_sorts:
        products = products.order_by(sort)
    else:
        products = products.order_by('-id')

    # 统计信息
    total_count = products.count()

    # 分页
    paginator = Paginator(products, 12)  # 每页显示12个商品
    page = request.GET.get('page')
    products_page = paginator.get_page(page)

    # 获取用户收藏的商品ID列表
    favorite_products = []
    if request.user.is_authenticated:
        favorite_products = list(Favorite.objects.filter(
            user=request.user, 
            product__in=[p.id for p in products_page]
        ).values_list('product_id', flat=True))

    # 获取所有激活的分类
    categories = Category.objects.filter(is_active=True).order_by('name')

    # 获取当前分类信息
    current_category = None
    if category_id:
        try:
            current_category = Category.objects.get(id=category_id, is_active=True)
        except Category.DoesNotExist:
            pass

    # 获取商品统计信息
    stats = {
        'total_products': total_count,
        'hot_products': Product.objects.filter(is_active=True, is_hot=True, category__is_active=True).count(),
        'new_products': Product.objects.filter(is_active=True, is_new=True, category__is_active=True).count(),
    }

    return render(request, 'goods/product_list.html', {
        'products': products_page,
        'categories': categories,
        'current_category': current_category,
        'search': search,
        'sort': sort,
        'stats': stats,
        'total_count': total_count,
        'favorite_products': favorite_products,  # 添加收藏商品ID列表
    })

def index(request):
    """首页视图"""
    # 获取热门商品（只显示上架的）
    hot_products = Product.objects.filter(is_hot=True, is_active=True)[:6]

    # 获取新品（只显示上架的）
    new_products = Product.objects.filter(is_new=True, is_active=True).order_by('-created_time')[:8]

    # 获取所有分类（只显示激活的）
    categories = Category.objects.filter(parent=None, is_active=True)

    return render(request, 'goods/product_list.html', {
        'hot_products': hot_products,
        'new_products': new_products,
        'categories': categories
    })

def product_detail(request, pk):
    """商品详情视图"""
    from django.db.models import Avg, Count
    from django.core.paginator import Paginator
    from reviews.models import Review

    product = get_object_or_404(Product, pk=pk)
    # 获取商品的所有图片
    product_images = product.images.all() if hasattr(product, 'images') else []
    # 获取所有分类（用于编辑表单）
    categories = Category.objects.all()
    
    # 判断用户是否已收藏该商品
    is_favorite = False
    if request.user.is_authenticated:
        is_favorite = Favorite.objects.filter(user=request.user, product=product).exists()

    # 获取商品收藏数量
    favorite_count = Favorite.objects.filter(product=product).count()

    # 获取商品评价
    reviews = Review.objects.filter(product=product).select_related('user').prefetch_related('review_images')

    # 评价分页
    paginator = Paginator(reviews, 5)  # 每页显示5条评价
    page = request.GET.get('page')
    product_reviews = paginator.get_page(page)

    # 计算评价统计
    reviews_stats = reviews.aggregate(
        average_score=Avg('score'),
        total_count=Count('id')
    )

    # 如果没有评价，设置默认值
    if reviews_stats['average_score'] is None:
        reviews_stats['average_score'] = 0
    if reviews_stats['total_count'] is None:
        reviews_stats['total_count'] = 0

    # 获取相关商品（同分类的其他上架商品）
    related_products = Product.objects.filter(
        category=product.category,
        is_active=True,
        category__is_active=True
    ).exclude(id=product.id).order_by('-sales', '-created_time')[:4]

    # 如果同分类商品不足，从其他分类补充
    if len(related_products) < 4:
        additional_products = Product.objects.filter(
            is_active=True,
            category__is_active=True
        ).exclude(
            id=product.id
        ).exclude(
            id__in=[p.id for p in related_products]
        ).order_by('-sales', '-created_time')[:4-len(related_products)]
        related_products = list(related_products) + list(additional_products)

    return render(request, 'goods/product_detail.html', {
        'product': product,
        'product_images': product_images,
        'categories': categories,
        'product_reviews': product_reviews,
        'reviews_stats': reviews_stats,
        'related_products': related_products,
        'is_favorite': is_favorite,  # 添加是否已收藏的标记
        'favorite_count': favorite_count,  # 添加收藏数量
    })

def category_products(request, category_id):
    """分类商品列表"""
    category = get_object_or_404(Category, id=category_id, is_active=True)

    # 如果是父分类，显示其所有子分类的商品
    if category.children.exists():
        # 只获取子分类的ID，不包含父分类本身
        child_category_ids = list(category.children.filter(is_active=True).values_list('id', flat=True))
        products = Product.objects.filter(
            category_id__in=child_category_ids,
            is_active=True
        ).select_related('category').distinct().order_by('-created_time', 'id')
    else:
        # 如果是子分类，只显示该分类的商品
        products = Product.objects.filter(
            category=category,
            is_active=True
        ).select_related('category').order_by('-created_time', 'id')

    # 获取所有分类
    categories = Category.objects.filter(parent=None, is_active=True).prefetch_related('children')

    # 分页
    paginator = Paginator(products, 12)
    page = request.GET.get('page')
    products_page = paginator.get_page(page)
    
    # 获取用户收藏的商品ID列表
    favorite_products = []
    if request.user.is_authenticated:
        favorite_products = list(Favorite.objects.filter(
            user=request.user, 
            product__in=[p.id for p in products_page]
        ).values_list('product_id', flat=True))

    return render(request, 'goods/product_list.html', {
        'category': category,
        'products': products_page,
        'categories': categories,
        'favorite_products': favorite_products,  # 添加收藏商品ID列表
    })

def hot_products(request):
    """热门商品"""
    products = Product.objects.filter(is_hot=True, is_active=True)

    # 分页
    paginator = Paginator(products, 12)
    page = request.GET.get('page')
    products_page = paginator.get_page(page)
    
    # 获取用户收藏的商品ID列表
    favorite_products = []
    if request.user.is_authenticated:
        favorite_products = list(Favorite.objects.filter(
            user=request.user, 
            product__in=[p.id for p in products_page]
        ).values_list('product_id', flat=True))

    return render(request, 'goods/product_list.html', {
        'products': products_page,
        'title': '热门商品',
        'favorite_products': favorite_products,  # 添加收藏商品ID列表
    })

def new_products(request):
    """新品上市"""
    products = Product.objects.filter(is_new=True, is_active=True).order_by('-created_time')

    # 分页
    paginator = Paginator(products, 12)
    page = request.GET.get('page')
    products_page = paginator.get_page(page)
    
    # 获取用户收藏的商品ID列表
    favorite_products = []
    if request.user.is_authenticated:
        favorite_products = list(Favorite.objects.filter(
            user=request.user, 
            product__in=[p.id for p in products_page]
        ).values_list('product_id', flat=True))

    return render(request, 'goods/product_list.html', {
        'products': products_page,
        'title': '新品上市',
        'favorite_products': favorite_products,  # 添加收藏商品ID列表
    })

@user_passes_test(lambda u: u.is_staff)
def add_product(request):
    """添加商品视图"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            name = request.POST.get('name')
            price = request.POST.get('price')
            stock = request.POST.get('stock', 0)  # 添加库存字段
            description = request.POST.get('description')
            category_id = request.POST.get('category')
            is_hot = request.POST.get('is_hot') == 'on'
            is_new = request.POST.get('is_new') == 'on'
            
            # 创建商品
            category = get_object_or_404(Category, id=category_id)
            product = Product.objects.create(
                name=name,
                price=price,
                stock=int(stock) if stock else 0,  # 添加库存字段
                description=description,
                category=category,
                is_hot=is_hot,
                is_new=is_new
            )
            
            # 处理主图
            if 'main_image' in request.FILES:
                product.main_image = request.FILES['main_image']
                product.save()
            
            # 处理商品图片
            if 'product_images' in request.FILES:
                images = request.FILES.getlist('product_images')
                for i, image in enumerate(images):
                    ProductImage.objects.create(
                        product=product,
                        image=image,
                        alt_text=f'{product.name} - 图片{i+1}',
                        sort_order=i
                    )
            
            messages.success(request, '商品添加成功！')
            return redirect('goods:detail', pk=product.id)
        except Exception as e:
            messages.error(request, f'添加商品失败：{str(e)}')
    
    # GET请求，显示添加商品表单
    categories = Category.objects.all()
    return render(request, 'goods/add_product.html', {
        'categories': categories
    })

@user_passes_test(lambda u: u.is_staff)
def edit_product(request, product_id):
    """编辑商品视图"""
    product = get_object_or_404(Product, id=product_id)
    
    if request.method == 'POST':
        try:
            # 更新商品信息
            product.name = request.POST.get('name')
            product.price = request.POST.get('price')
            stock = request.POST.get('stock')
            if stock:
                product.stock = int(stock)
            product.description = request.POST.get('description')
            category_id = request.POST.get('category')
            if category_id:
                product.category = get_object_or_404(Category, id=category_id)
            product.is_hot = request.POST.get('is_hot') == 'on'
            product.is_new = request.POST.get('is_new') == 'on'
            
            # 处理主图
            if 'main_image' in request.FILES:
                product.main_image = request.FILES['main_image']
            
            product.save()
            
            # 处理商品图片
            if 'product_images' in request.FILES:
                images = request.FILES.getlist('product_images')
                for i, image in enumerate(images):
                    ProductImage.objects.create(
                        product=product,
                        image=image,
                        alt_text=f'{product.name} - 图片{i+1}',
                        sort_order=i
                    )
            
            messages.success(request, '商品更新成功！')
            return redirect('goods:detail', pk=product.id)
        except Exception as e:
            messages.error(request, f'更新商品失败：{str(e)}')
    
    # GET请求，显示编辑商品表单
    categories = Category.objects.all()
    return render(request, 'goods/edit_product.html', {
        'product': product,
        'categories': categories
    })

@user_passes_test(lambda u: u.is_staff)
def get_product(request, product_id):
    """获取商品信息的API"""
    product = get_object_or_404(Product, id=product_id)
    # 获取商品的所有图片
    images = [{'id': img.id, 'url': img.image.url} for img in product.images.all()]
    
    data = {
        'id': product.id,
        'name': product.name,
        'price': str(product.price),
        'description': product.description,
        'main_image': product.main_image.url if product.main_image else None,
        'is_hot': product.is_hot,
        'is_new': product.is_new,
        'category_id': product.category.id,
        'images': images,
    }
    return JsonResponse(data)

@require_http_methods(["POST"])
@user_passes_test(lambda u: u.is_staff)
def update_product(request, product_id):
    """更新商品信息的API - 支持实时前台更新"""
    product = get_object_or_404(Product, id=product_id)

    try:
        # 记录修改前的状态
        old_data = {
            'name': product.name,
            'price': product.price,
            'description': product.description,
            'is_hot': product.is_hot,
            'is_new': product.is_new,
            'is_active': product.is_active,
            'category_id': product.category.id if product.category else None,
        }

        # 更新基本信息
        if 'name' in request.POST:
            product.name = request.POST.get('name', product.name).strip()

        if 'price' in request.POST:
            try:
                product.price = float(request.POST.get('price', product.price))
            except (ValueError, TypeError):
                pass

        if 'description' in request.POST:
            product.description = request.POST.get('description', product.description).strip()

        if 'stock' in request.POST:
            try:
                product.stock = int(request.POST.get('stock', product.stock))
            except (ValueError, TypeError):
                pass

        # 处理布尔字段
        product.is_hot = request.POST.get('is_hot') in ['on', 'true', 'True', '1']
        product.is_new = request.POST.get('is_new') in ['on', 'true', 'True', '1']

        # 处理状态切换
        if 'is_active' in request.POST:
            is_active = request.POST.get('is_active')
            product.is_active = is_active in ['true', 'True', '1', 'on']

        # 更新分类
        if 'category' in request.POST:
            category_id = request.POST.get('category')
            if category_id:
                try:
                    category = Category.objects.get(id=category_id)
                    product.category = category
                except Category.DoesNotExist:
                    pass

        # 处理主图
        if 'main_image' in request.FILES:
            product.main_image = request.FILES['main_image']

        # 保存商品（这会触发updated_time自动更新）
        product.save()

        # 处理额外图片
        if 'product_images' in request.FILES:
            images = request.FILES.getlist('product_images')
            for i, image in enumerate(images):
                ProductImage.objects.create(
                    product=product,
                    image=image,
                    alt_text=f'{product.name} - 图片{i+1}',
                    sort_order=i
                )

        # 记录修改后的状态
        new_data = {
            'name': product.name,
            'price': str(product.price),
            'description': product.description,
            'is_hot': product.is_hot,
            'is_new': product.is_new,
            'is_active': product.is_active,
            'category_id': product.category.id if product.category else None,
            'category_name': product.category.name if product.category else None,
            'updated_time': product.updated_time.strftime('%Y-%m-%d %H:%M:%S'),
        }

        # 检查哪些字段发生了变化
        changed_fields = []
        for key in old_data:
            if str(old_data[key]) != str(new_data.get(key, '')):
                changed_fields.append(key)

        # 发送成功响应，包含更新信息
        response_data = {
            'success': True,
            'message': '商品信息更新成功',
            'product_id': product.id,
            'changed_fields': changed_fields,
            'updated_data': new_data,
            'frontend_visible': product.is_active and product.category.is_active if product.category else False
        }

        return JsonResponse(response_data)

    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'error': str(e),
            'message': f'更新商品失败: {str(e)}'
        })

@require_http_methods(["POST"])
@user_passes_test(lambda u: u.is_staff)
def delete_product(request, product_id):
    """删除商品的API"""
    try:
        product = get_object_or_404(Product, id=product_id)
        product_name = product.name

        # 删除商品（会级联删除相关的图片和订单项）
        product.delete()

        messages.success(request, f'商品 "{product_name}" 删除成功！')
        return JsonResponse({'success': True, 'message': f'商品 "{product_name}" 删除成功！'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@require_http_methods(["POST"])
@user_passes_test(lambda u: u.is_staff)
def delete_product_image(request, image_id):
    """删除商品图片的API"""
    try:
        image = get_object_or_404(ProductImage, id=image_id)
        image_name = image.image.name

        # 删除图片文件
        if image.image:
            image.image.delete()

        # 删除数据库记录
        image.delete()

        return JsonResponse({'success': True, 'message': '图片删除成功！'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@require_http_methods(["GET"])
def check_product_updates(request):
    """检查商品更新状态API - 用于前台实时检测"""
    try:
        from .signals import get_last_update_time, is_product_recently_updated
        from django.utils import timezone

        # 获取请求参数
        last_check = request.GET.get('last_check', 0)
        product_ids = request.GET.get('product_ids', '').split(',')

        try:
            last_check = float(last_check)
        except (ValueError, TypeError):
            last_check = 0

        # 检查全局更新时间
        last_update = get_last_update_time()
        has_updates = last_update > last_check

        # 检查特定商品更新
        updated_products = []
        if product_ids and product_ids != ['']:
            for product_id in product_ids:
                try:
                    product_id = int(product_id.strip())
                    if is_product_recently_updated(product_id, last_check):
                        # 获取更新后的商品信息
                        try:
                            product = Product.objects.get(id=product_id)
                            updated_products.append({
                                'id': product.id,
                                'name': product.name,
                                'price': str(product.price),
                                'is_active': product.is_active,
                                'is_hot': product.is_hot,
                                'is_new': product.is_new,
                                'updated_time': product.updated_time.strftime('%Y-%m-%d %H:%M:%S'),
                            })
                        except Product.DoesNotExist:
                            # 商品已被删除
                            updated_products.append({
                                'id': product_id,
                                'deleted': True
                            })
                except (ValueError, TypeError):
                    continue

        return JsonResponse({
            'success': True,
            'has_updates': has_updates,
            'last_update': last_update,
            'updated_products': updated_products,
            'check_time': timezone.now().timestamp()
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

@require_http_methods(["GET"])
def get_latest_products(request):
    """获取最新商品列表API - 用于前台实时刷新"""
    try:
        from django.utils import timezone

        # 获取参数
        limit = int(request.GET.get('limit', 10))
        category_id = request.GET.get('category')
        only_active = request.GET.get('only_active', 'true').lower() == 'true'

        # 构建查询
        products = Product.objects.select_related('category')

        if only_active:
            products = products.filter(is_active=True, category__is_active=True)

        if category_id:
            try:
                products = products.filter(category_id=int(category_id))
            except (ValueError, TypeError):
                pass

        # 按更新时间排序
        products = products.order_by('-updated_time', '-created_time')[:limit]

        # 序列化商品数据
        products_data = []
        for product in products:
            products_data.append({
                'id': product.id,
                'name': product.name,
                'price': str(product.price),
                'description': product.description[:100] + '...' if len(product.description) > 100 else product.description,
                'category': {
                    'id': product.category.id,
                    'name': product.category.name
                } if product.category else None,
                'main_image_url': product.main_image.url if product.main_image else None,
                'is_hot': product.is_hot,
                'is_new': product.is_new,
                'is_active': product.is_active,
                'stock': product.stock,
                'sales': product.sales,
                'created_time': product.created_time.strftime('%Y-%m-%d %H:%M:%S'),
                'updated_time': product.updated_time.strftime('%Y-%m-%d %H:%M:%S'),
            })

        return JsonResponse({
            'success': True,
            'products': products_data,
            'total_count': len(products_data),
            'timestamp': timezone.now().strftime('%Y-%m-%d %H:%M:%S')
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def search_products(request):
    """商品搜索功能"""
    query = request.GET.get('q', '').strip()
    category_id = request.GET.get('category')
    sort = request.GET.get('sort', '-id')

    # 基础查询：只显示上架的商品
    products = Product.objects.filter(
        is_active=True,
        category__is_active=True
    ).select_related('category').annotate(
        favorite_count=Count('favorited_by')
    )

    # 搜索过滤
    if query:
        from django.db.models import Q
        products = products.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query) |
            Q(category__name__icontains=query)
        )

    # 分类过滤
    if category_id:
        try:
            category = Category.objects.get(id=category_id, is_active=True)
            products = products.filter(category=category)
        except Category.DoesNotExist:
            pass

    # 排序
    valid_sorts = ['-id', 'id', '-price', 'price', '-sales', 'sales', '-updated_time', '-favorite_count', 'favorite_count']
    if sort in valid_sorts:
        products = products.order_by(sort)
    else:
        products = products.order_by('-id')  # 默认按id倒序排列

    # 统计
    total_count = products.count()

    # 分页
    paginator = Paginator(products, 12)
    page = request.GET.get('page')
    products_page = paginator.get_page(page)

    # 获取用户收藏的商品ID列表
    favorite_products = []
    if request.user.is_authenticated:
        favorite_products = list(Favorite.objects.filter(
            user=request.user,
            product__in=[p.id for p in products_page]
        ).values_list('product_id', flat=True))

    # 获取所有激活的分类
    categories = Category.objects.filter(is_active=True).order_by('name')

    # 获取当前分类信息
    current_category = None
    if category_id:
        try:
            current_category = Category.objects.get(id=category_id, is_active=True)
        except Category.DoesNotExist:
            pass

    return render(request, 'goods/search_results.html', {
        'products': products_page,
        'categories': categories,
        'current_category': current_category,
        'query': query,
        'sort': sort,
        'total_count': total_count,
        'favorite_products': favorite_products,
        'search_performed': bool(query),
    })
