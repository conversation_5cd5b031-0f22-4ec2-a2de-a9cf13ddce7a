{% extends 'base.html' %}
{% load static %}

{% block title %}订单详情 - 尚品汇{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">订单详情</h5>
                </div>
                <div class="card-body">
                    {% if order %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6>订单信息</h6>
                            <p><strong>订单号：</strong>{{ order.order_number }}</p>
                            <p><strong>订单状态：</strong>{{ order.status_display }}</p>
                            <p><strong>创建时间：</strong>{{ order.created_time|date:"Y-m-d H:i:s" }}</p>
                            <p><strong>订单总额：</strong>¥{{ order.total_amount }}</p>
                            <p><strong>实付金额：</strong>¥{{ order.pay_amount }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>收货信息</h6>
                            <p><strong>收货人：</strong>{{ order.receiver|default:"未填写" }}</p>
                            <p><strong>联系电话：</strong>{{ order.receiver_mobile|default:"未填写" }}</p>
                            <p><strong>收货地址：</strong>{{ order.shipping_address|default:"未填写" }}</p>
                            {% if order.remark %}
                            <p><strong>订单备注：</strong>{{ order.remark }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-12">
                            <h6>商品清单</h6>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>商品</th>
                                            <th>单价</th>
                                            <th>数量</th>
                                            <th>小计</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in order.items.all %}
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <img src="{{ item.product.main_image.url }}" alt="{{ item.product.name }}" style="width: 60px; height: 60px; object-fit: cover;" class="me-3">
                                                    <div>
                                                        <h6 class="mb-0">{{ item.product.name }}</h6>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>¥{{ item.price }}</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>¥{{ item.total_amount }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'users:orders' %}" class="btn btn-secondary">返回订单列表</a>
                                <div>
                                    {% if order.status == 'pending' %}
                                    <a href="{% url 'order:pay' order.order_number %}" class="btn btn-success me-2">立即支付</a>
                                    <a href="{% url 'order:cancel' order.order_number %}" class="btn btn-danger" onclick="return confirm('确定要取消这个订单吗？')">取消订单</a>
                                    {% elif order.status == 'shipped' %}
                                    <button class="btn btn-info btn-confirm" data-id="{{ order.order_number }}">确认收货</button>
                                    {% elif order.status == 'received' %}
                                    {% for item in order.items.all %}
                                    <a href="{% url 'reviews:add' item.product.id %}" class="btn btn-warning me-2">评价商品</a>
                                    {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <p class="text-muted">订单不存在</p>
                        <a href="{% url 'goods:index' %}" class="btn btn-primary">返回首页</a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 确认收货
    const confirmBtn = document.querySelector('.btn-confirm');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            if (confirm('确认已收到商品？')) {
                const orderId = this.dataset.id;
                
                fetch(`/api/orders/${orderId}/receive/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 显示确认成功消息
                        alert('确认收货成功！即将跳转到商品评价页面');
                        // 跳转到评价页面
                        if (data.review_url) {
                            window.location.href = data.review_url;
                        } else {
                            location.reload();
                        }
                    } else {
                        alert(data.message || '操作失败，请重试');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('操作失败，请重试');
                });
            }
        });
    }

    // 获取CSRF Token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}
