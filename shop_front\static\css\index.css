/* 基础设置 */
.container {
    width: 1190px;
    margin: 0 auto;
}
/* #region顶部导航条start */
.topbar {
    height: 30px;
    background-color: #ECECEC;
}
.welcome {
    height: 30px;
    line-height: 30px;
    font-size: 0;
    color: #666666;
}
.welcome span,
.welcome a {
    font-size: 12px;
}
.welcome .hello {
    margin-right: 28px;
}
.welcome .login {
    padding-right: 10px;
    border-right: 1px solid #666666;
}
.welcome .register {
    padding-left: 10px;
}
.topbar-nav .list {
    height: 30px;
    line-height: 30px;
}
.topbar-nav .list li {
    float: left;
}
.topbar-nav .list li a {
    padding: 0 15px;
    border-right: 1px solid #666666;
}
.topbar-nav .list li:first-child a {
    padding-left: 0;
}
.topbar-nav .list li:last-child a {
    padding-right: 0;
    border: 0;
}

/* #endregion顶部导航条end */

/*#region头部start  */
.header {
    height: 120px;
}
.header .search form {
    margin-top: 42px;
    font-size: 0;
}
.header .search input {
    width: 508px;
    height: 34px;
    border: 1px solid #DD302D;
}
.header .search button {
    width: 80px;
    height: 36px;
    background-color: #DD302D;
    vertical-align: top;
    background-image: url('../images/assets/serch_icon.png');
    background-repeat: no-repeat;
    background-position: 28px 6px;
}
/*#endregion头部end  */

/* #region主导航区start */
.main-nav {
    height: 48px;
    border-bottom: 1px solid #DD302D;
}
.all-types {
    height: 48px;
    line-height: 48px;
    width: 190px;
    background-color: #DD302D;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
}
.main-nav-list {
    height: 48px;
    line-height: 48px;
}
.main-nav-list li {
    float: left;
    margin: 0 10px;
    font-size: 16px;
    color: #333333;
}
/* #endregion主导航区end */

/* #region主要内容区start */
.main-content {
    margin-top: 30px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(0,0,0,0.1);
    position: relative;
}

.main-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.content-wrapper {
    display: flex;
    gap: 0;
    min-height: 450px;
    position: relative;
    z-index: 1;
}

.sidebar {
    width: 190px;
    flex-shrink: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.sidebar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 1px;
    height: 100%;
    background: linear-gradient(to bottom, transparent, rgba(255,255,255,0.3), transparent);
}

.slide-nav {
    width: 100%;
    height: 450px;
    background: transparent;
    padding: 15px 0;
}

.slide-nav > li {
    height: 38px;
    line-height: 38px;
    padding: 0 15px;
    font-size: 13px;
    margin-bottom: 2px;
    transition: all 0.3s ease;
    position: relative;
    border-radius: 0 20px 20px 0;
    margin-right: 15px;
}

.slide-nav > li:hover {
    background: rgba(255,255,255,0.2);
    transform: translateX(10px);
}

.slide-nav > li:hover > a {
    color: #FFFFFF;
}

.slide-nav > li > a {
    color: rgba(255,255,255,0.9);
    text-decoration: none;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.slide-nav > li:hover .second-menu {
    display: block;
}

.second-menu {
    display: none;
    position: absolute;
    left: 180px;
    top: 0;
    width: 600px;
    min-height: 450px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
    padding: 25px;
    z-index: 1000;
}

.second-menu dl {
    margin-bottom: 25px;
}

.second-menu dt {
    float: left;
    width: 80px;
    font-weight: bold;
    margin-right: 15px;
    color: #DD302D;
    font-size: 14px;
}

.second-menu dd {
    float: left;
    margin-right: 15px;
}

.second-menu dd a {
    padding: 5px 10px;
    border-radius: 15px;
    transition: all 0.3s ease;
    font-size: 13px;
}

.second-menu dd a:hover {
    background: #DD302D;
    color: white;
}
.main-area {
    width: 710px; /* 固定宽度 1190 - 200 - 280 = 710 */
    display: flex;
    gap: 0;
    background: white;
}

.banner-section {
    flex: 1;
    position: relative;
    background: white;
    min-width: 0; /* 允许flex项目收缩 */
}

.banner {
    width: 100%;
    height: 450px;
    position: relative;
    overflow: hidden;
    border-radius: 0;
}

.banner-box {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.banner-list {
    width: 100%;
    height: 100%;
    position: relative;
}

.banner-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
}

.banner-item.active {
    opacity: 1;
}

.banner-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.banner-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.banner-dots span {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.banner-dots span.active {
    background: #DD302D;
    transform: scale(1.2);
}

.banner-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    z-index: 10;
}

.banner:hover .banner-arrow {
    opacity: 1;
}

.banner-arrow:hover {
    background: rgba(221, 48, 45, 0.8);
    transform: translateY(-50%) scale(1.1);
}

.banner-arrow.prev {
    left: 20px;
}

.banner-arrow.next {
    right: 20px;
}
.right-sidebar {
    width: 280px;
    flex-shrink: 0;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    border-left: 1px solid rgba(255,255,255,0.3);
    padding: 20px 15px;
}

.slide-other {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.message {
    background: rgba(255,255,255,0.9);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    flex: 1;
    max-height: 200px;
    overflow-y: auto;
}

.message .title {
    height: auto;
    line-height: 1.5;
    padding: 0 0 15px 0;
    border-bottom: 2px solid #DD302D;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message .title span {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
}

.message .title a {
    font-size: 12px;
    color: #DD302D;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 15px;
    background: rgba(221, 48, 45, 0.1);
    transition: all 0.3s ease;
}

.message .title a:hover {
    background: #DD302D;
    color: white;
}

.message-list {
    padding: 0;
}

.message-list li {
    height: auto;
    line-height: 1.5;
    padding: 8px 0;
    font-size: 13px;
    color: #666;
    border-bottom: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.message-list li:hover {
    color: #DD302D;
    transform: translateX(5px);
}

.message-list li:last-child {
    border-bottom: none;
}

.other-nav {
    background: rgba(255,255,255,0.9);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    flex: 1;
    max-height: 200px;
}

.other-nav-list {
    margin: 0;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.other-nav-list li {
    width: auto;
    height: auto;
    float: none;
    margin: 0;
    text-align: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 10px;
    background: rgba(255,255,255,0.8);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.other-nav-list li:hover {
    background: white;
    border-color: #DD302D;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(221, 48, 45, 0.2);
}

.other-nav-list li a {
    text-decoration: none;
    color: #2c3e50;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.other-nav-list .picture {
    width: 30px;
    height: 30px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #DD302D;
}

.other-nav-list span {
    font-size: 11px;
    font-weight: 600;
}
.other-nav-list .picture {
    width: 48px;
    height: 48px;
    background-image: url('../images/assets/精灵图-侧边功能.png');
}
.other-nav-list:nth-child(1) li:nth-child(1) .picture {
    background-position: 0 0;
}
.other-nav-list:nth-child(1) li:nth-child(2) .picture {
    background-position: -48px 0;
}
.other-nav-list:nth-child(1) li:nth-child(3) .picture {
    background-position: -96px 0;
}
.other-nav-list:nth-child(1) li:nth-child(4) .picture {
    background-position: -144px 0;
}

.other-nav-list:nth-child(2) li:nth-child(1) .picture {
    background-position: 0 -48px;
}
.other-nav-list:nth-child(2) li:nth-child(2) .picture {
    background-position: -48px -48px;
}
.other-nav-list:nth-child(2) li:nth-child(3) .picture {
    background-position: -96px -48px;
}
.other-nav-list:nth-child(2) li:nth-child(4) .picture {
    background-position: -144px -48px;
}

.other-nav-list:nth-child(3) li:nth-child(1) .picture {
    background-position: 0 -96px;
}
.other-nav-list:nth-child(3) li:nth-child(2) .picture {
    background-position: -48px -96px;
}
.other-nav-list:nth-child(3) li:nth-child(3) .picture {
    background-position: -96px -96px;
}
.other-nav-list:nth-child(3) li:nth-child(4) .picture {
    background-position: -144px -96px;
}
/* #endregion主要内容区end */

/* #region秒杀start */
.seckill {
    margin-top: 50px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 50px rgba(238, 90, 36, 0.3);
    position: relative;
}

.seckill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="flash" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 15,8 10,8 12,18 7,12 10,12 8,2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23flash)"/></svg>');
    animation: flash-move 10s linear infinite;
}

@keyframes flash-move {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.seckill-header {
    padding: 25px 30px;
    background: rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
}

.seckill-title {
    font-size: 28px;
    font-weight: 700;
    color: white;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.seckill-title::before {
    content: '⚡';
    font-size: 32px;
    animation: pulse 1.5s infinite;
}

.seckill-timer {
    color: white;
    font-size: 16px;
}

.seckill-countdown {
    background: rgba(255,255,255,0.2);
    padding: 10px 15px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.seckill-countdown strong {
    background: white;
    color: #ff6b6b;
    padding: 5px 8px;
    border-radius: 5px;
    margin: 0 2px;
    font-weight: 700;
    min-width: 25px;
    display: inline-block;
    text-align: center;
}

.seckill-content {
    padding: 30px;
    display: flex;
    gap: 20px;
    overflow-x: auto;
    position: relative;
    z-index: 2;
}

.seckill-item {
    flex: 0 0 200px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    position: relative;
}

.seckill-item::before {
    content: '限时抢购';
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ff6b6b;
    color: white;
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;
    z-index: 3;
}

.seckill-item:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.seckill-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.seckill-item:hover img {
    transform: scale(1.1);
}

.seckill-info {
    padding: 15px;
}

.seckill-name {
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.seckill-price {
    font-size: 16px;
    color: #ff6b6b;
    font-weight: 700;
    margin-bottom: 5px;
}

.seckill-price span {
    font-size: 20px;
}

.original-price {
    font-size: 12px;
    color: #999;
}

/* #endregion秒杀end */

/* #region楼层start */
.floor {
    margin-top: 80px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    overflow: hidden;
    position: relative;
}

.floor:nth-child(odd) {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.floor:nth-child(even) {
    background: linear-gradient(135deg, #fff5f5 0%, #ffe8e8 100%);
}

.floor::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #DD302D, #ff6b6b, #DD302D);
}

.floor-nav {
    height: 60px;
    line-height: 60px;
    padding: 0 30px;
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(10px);
    position: relative;
}

.floor-name {
    font-size: 28px;
    font-weight: 700;
    color: #2c3e50;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    position: relative;
}

.floor-name::after {
    content: '';
    position: absolute;
    bottom: 15px;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #DD302D, #ff6b6b);
    border-radius: 2px;
}

.floor-nav-list {
    margin-top: 10px;
}

.floor-nav-list li {
    float: left;
    margin-right: 20px;
}

.floor-nav-list li a {
    padding: 8px 16px;
    font-size: 14px;
    color: #6c757d;
    text-decoration: none;
    border-radius: 20px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.7);
}

.floor-nav-list li a:hover {
    background: #DD302D;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(221, 48, 45, 0.3);
}
.floor-info {
    margin-top: 20px;
    padding: 30px;
    width: 100%;
    overflow: hidden;
}

.info-item {
    border-radius: 15px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.item1 {
    flex: 0 0 190px;
    height: 392px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.item1::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 100%);
}

.item1-list {
    padding: 20px 16px;
    position: relative;
    z-index: 2;
}

.item1-list li {
    width: 78px;
    height: 40px;
    border: none;
    float: left;
    text-align: center;
    line-height: 40px;
    margin: 0 4px 8px 0;
    background: rgba(255,255,255,0.9);
    border-radius: 20px;
    transition: all 0.3s ease;
    font-size: 12px;
    font-weight: 600;
    color: #2c3e50;
    cursor: pointer;
}

.item1-list li:hover {
    background: white;
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}
.item2 {
    width: 340px;
    height: 392px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.item2::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(221, 48, 45, 0.05) 0%, transparent 100%);
    z-index: 1;
}

.item2 a {
    position: relative;
    z-index: 2;
    display: block;
    height: 100%;
}

.item3, .item4, .item5 {
    width: 206px;
    height: 392px;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.item5 {
    margin-right: 0 !important;
}

.item3-top, .item5-top {
    width: 206px;
    height: 196px;
    border-bottom: 2px solid #f8f9fa;
    position: relative;
    overflow: hidden;
}

.item3-top::after, .item5-top::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background: linear-gradient(90deg, #DD302D, #ff6b6b);
}

/* 商品项悬停效果 */
.item3-top:hover, .item5-top:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

.item3-top a, .item5-top a {
    display: block;
    height: 100%;
    position: relative;
    text-decoration: none;
}

.item3-top a::before, .item5-top a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(221, 48, 45, 0);
    transition: background 0.3s ease;
    z-index: 1;
}

.item3-top a:hover::before, .item5-top a:hover::before {
    background: rgba(221, 48, 45, 0.1);
}

/* 确保楼层容器宽度足够 */
.floor-info {
    width: 1190px !important; /* 适配container宽度 */
    min-width: 1190px;
    clear: both;
    display: flex;
    align-items: stretch;
    gap: 10px;
}
.item5-top {
    width: 206px;
    height: 196px;
    border-bottom: 1px solid #E4E4E4;
}

/* 楼层商品图片样式 */
.floor-info .item2 {
    flex: 0 0 340px;
    position: relative;
}

.floor-info .item2 img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.floor-info .item3,
.floor-info .item4,
.floor-info .item5 {
    flex: 0 0 206px;
    display: flex;
    flex-direction: column;
}

.floor-info .item3-top,
.floor-info .item5-top {
    flex: 1;
    min-height: 196px;
    max-height: 196px;
    overflow: hidden;
    position: relative;
    display: flex;
    flex-direction: column;
}

.floor-info .item3-top img,
.floor-info .item5-top img {
    width: 100%;
    height: 140px;
    object-fit: cover;
    display: block;
    flex-shrink: 0;
    max-width: 100%;
    border-radius: 8px 8px 0 0;
}

/* 特别针对时尚服装版块的图片控制 */
.floor:nth-child(even) .item3-top img,
.floor:nth-child(even) .item5-top img {
    object-fit: contain;
    background: #f8f9fa;
    padding: 5px;
}

.floor:nth-child(even) .item2 img {
    object-fit: contain;
    background: #f8f9fa;
    padding: 10px;
}

/* 确保商品容器不溢出 */
.item1, .item2, .item3, .item4, .item5 {
    overflow: hidden;
    box-sizing: border-box;
    max-width: 100%;
}

.item3-top, .item5-top {
    overflow: hidden;
    position: relative;
    box-sizing: border-box;
    max-width: 100%;
    max-height: 196px;
}

/* 强制图片容器约束 */
.floor-info img {
    max-width: 100% !important;
    object-fit: cover !important;
}

/* 重新定义具体的图片尺寸 */
.floor-info .item2 img {
    width: 100% !important;
    height: 392px !important;
}

.floor-info .item3-top img,
.floor-info .item5-top img {
    width: 100% !important;
    height: 140px !important;
}

/* 防止图片变形 */
.floor-info a {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    text-decoration: none;
}

/* 商品信息容器 */
.floor-info .item-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 8px;
    background: rgba(255,255,255,0.95);
}

/* 商品信息样式 */
.item-name {
    padding: 8px;
    font-size: 12px;
    color: #2c3e50;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 600;
    line-height: 1.3;
    flex-shrink: 0;
}

.item-price {
    padding: 4px 8px 8px;
    font-size: 14px;
    color: #DD302D;
    font-weight: 700;
    text-align: center;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    flex-shrink: 0;
}

/* 楼层商品链接样式 */
.floor-info .item3-top a,
.floor-info .item5-top a {
    height: 100%;
    display: flex;
    flex-direction: column;
    text-decoration: none;
    transition: all 0.3s ease;
}

.floor-info .item3-top a:hover,
.floor-info .item5-top a:hover {
    transform: scale(1.02);
}

.floor-info .item3-top a:hover .item-name,
.floor-info .item5-top a:hover .item-name {
    color: #DD302D;
}

/* 大图商品信息样式 */
.promo-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 30px 20px 20px;
    text-align: center;
}

.promo-info h4 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.promo-info p {
    font-size: 14px;
    margin-bottom: 10px;
    opacity: 0.9;
}

.promo-price {
    font-size: 20px;
    font-weight: 700;
    color: #ff6b6b;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

/* 滚动动画 */
.scroll-animate {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-animate.animate {
    opacity: 1;
    transform: translateY(0);
}

/* 楼层交替动画 */
.floor:nth-child(odd) .info-item {
    animation: slideInLeft 0.8s ease-out forwards;
}

.floor:nth-child(even) .info-item {
    animation: slideInRight 0.8s ease-out forwards;
}

@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 楼层商品项延迟动画 */
.info-item:nth-child(1) { animation-delay: 0.1s; }
.info-item:nth-child(2) { animation-delay: 0.2s; }
.info-item:nth-child(3) { animation-delay: 0.3s; }
.info-item:nth-child(4) { animation-delay: 0.4s; }
.info-item:nth-child(5) { animation-delay: 0.5s; }

/* 特殊效果 */
.floor:nth-child(3n+1) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.floor:nth-child(3n+2) {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
}

.floor:nth-child(3n+3) {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.floor:nth-child(3n+1) .floor-name,
.floor:nth-child(3n+2) .floor-name,
.floor:nth-child(3n+3) .floor-name {
    color: white;
}

.floor:nth-child(3n+1) .floor-nav-list li a,
.floor:nth-child(3n+2) .floor-nav-list li a,
.floor:nth-child(3n+3) .floor-nav-list li a {
    color: rgba(255,255,255,0.8);
    background: rgba(255,255,255,0.2);
}

.floor:nth-child(3n+1) .floor-nav-list li a:hover,
.floor:nth-child(3n+2) .floor-nav-list li a:hover,
.floor:nth-child(3n+3) .floor-nav-list li a:hover {
    background: rgba(255,255,255,0.9);
    color: #2c3e50;
}

/* #endregion楼层end */

/* #region热门商品和新品上市美化 */
.hot-products, .new-products {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    padding: 60px 0;
    margin-top: 50px;
    position: relative;
    overflow: hidden;
}

.hot-products::before, .new-products::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(221, 48, 45, 0.05) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
    z-index: 1;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.section-title {
    text-align: center;
    font-size: 36px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 50px;
    position: relative;
    z-index: 2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #DD302D, #ff6b6b);
    border-radius: 2px;
}

.product-list {
    position: relative;
    z-index: 2;
}

.card {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: white;
    position: relative;
}

.card:hover {
    transform: translateY(-15px) scale(1.05);
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
}

.product-image-container {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.card-img-top {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.6s ease;
}

.card:hover .card-img-top {
    transform: scale(1.1);
}

.hot-tag, .new-tag {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    color: white;
    z-index: 3;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.hot-tag {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    animation: pulse 2s infinite;
}

.new-tag {
    background: linear-gradient(45deg, #00d2d3, #54a0ff);
    animation: bounce 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.product-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover .product-hover-overlay {
    opacity: 1;
}

.product-buttons {
    display: flex;
    gap: 15px;
}

.preview-button, .add-cart-button {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255,255,255,0.9);
    color: #DD302D;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 18px;
}

.preview-button:hover, .add-cart-button:hover {
    background: #DD302D;
    color: white;
    transform: scale(1.2);
}

.card-body {
    padding: 25px 20px;
    text-align: center;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-text {
    font-size: 20px;
    font-weight: 700;
    color: #DD302D;
    margin-bottom: 20px;
}

.btn-danger {
    background: linear-gradient(45deg, #DD302D, #ff6b6b);
    border: none;
    border-radius: 25px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: linear-gradient(45deg, #c92a2a, #DD302D);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(221, 48, 45, 0.4);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    transform: scale(1.1);
}

/* 交替布局样式 */
.hot-products {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.new-products {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.hot-products .section-title {
    color: white;
}

.new-products .section-title {
    color: white;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .floor-info {
        width: 100% !important;
        min-width: auto;
        gap: 8px;
    }

    .item1 {
        flex: 0 0 160px;
    }

    .item2 {
        flex: 0 0 280px;
    }

    .item3, .item4, .item5 {
        flex: 0 0 180px;
    }
}

@media (max-width: 992px) {
    .floor-info {
        flex-wrap: wrap;
        justify-content: center;
    }

    .item1 {
        flex: 0 0 100%;
        height: 200px;
        margin-bottom: 20px;
    }

    .item2 {
        flex: 0 0 48%;
        margin-bottom: 20px;
    }

    .item3, .item4, .item5 {
        flex: 0 0 30%;
        min-width: 180px;
        margin-bottom: 20px;
    }

    .floor-name {
        font-size: 24px;
    }

    .floor-nav {
        padding: 0 20px;
    }
}

@media (max-width: 768px) {
    .section-title {
        font-size: 28px;
    }

    .card-body {
        padding: 15px;
    }

    .product-buttons {
        gap: 10px;
    }

    .preview-button, .add-cart-button {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .floor {
        margin-top: 40px;
    }

    .floor-info {
        padding: 20px 15px;
        flex-direction: column;
        gap: 15px;
    }

    .item1, .item2, .item3, .item4, .item5 {
        flex: 0 0 auto;
        width: 100%;
        height: auto;
        min-height: 250px;
    }

    .item3-top, .item5-top {
        height: auto;
        min-height: 200px;
        max-height: none;
        border-bottom: none;
        margin-bottom: 10px;
    }

    .floor-info .item3-top img,
    .floor-info .item5-top img {
        height: 180px;
    }

    .floor-nav-list li {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .floor-nav-list li a {
        padding: 6px 12px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 24px;
    }

    .floor-name {
        font-size: 20px;
    }

    .hot-products, .new-products {
        padding: 40px 0;
    }

    .floor-info {
        padding: 15px 10px;
    }

    .item1-list li {
        width: calc(50% - 6px);
        margin-right: 6px;
    }
}

/* 加载动画 */
.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #DD302D;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: inline-block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 提示消息样式 */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 25px;
    color: white;
    font-weight: 600;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.toast-message.show {
    opacity: 1;
    transform: translateX(0);
}

/* #endregion热门商品和新品上市美化 */

/* #region页脚start */
.footer {
    margin-top: 48px;
    height: 440px;
    background-color: #EAEAEA;
    padding-top: 28px;
}
.top-links {
    height: 198px;
    border-bottom: 1px solid #E1E1E1;
}
.serve-list {
    width: 190px;
    float: left;
}
.serve-list li {
    height: 22px;
    line-height: 22px;
}
.serve-list li:first-child {
    height: 34px;
    line-height: 34px;
    font-size: 14px;
}
.serve-list:last-child {
    width: 242px;
}
.bottom-links {
    margin-top: 20px;
    text-align: center;
}
.cooperation-list {
    display: inline-block;
}
.cooperation-list li {
    float: left;
}
.cooperation-list li a {
    padding: 0 26px;
    border-right: 1px solid #E1E1E1;
}
.cooperation-list li:last-child a {
    border: 0;
    padding-right: 0;
}
.bottom-links-aside {
    margin-top: 10px;
} 