#!/usr/bin/env python
"""
测试商品图片alt_text字段修复
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop/shop_front')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from goods.models import Product, ProductImage
from django.core.files.uploadedfile import SimpleUploadedFile

def test_product_image_creation():
    """测试商品图片创建"""
    print("🔍 测试商品图片创建...")
    
    # 获取一个商品
    try:
        product = Product.objects.first()
        if not product:
            print("❌ 没有找到商品，请先创建商品")
            return
        
        print(f"📦 使用商品: {product.name}")
        
        # 创建一个测试图片
        test_image_content = b'fake image content'
        test_image = SimpleUploadedFile(
            name='test_image.jpg',
            content=test_image_content,
            content_type='image/jpeg'
        )
        
        # 测试创建商品图片（不提供alt_text）
        try:
            product_image = ProductImage.objects.create(
                product=product,
                image=test_image,
                sort_order=1
            )
            print(f"✅ 商品图片创建成功")
            print(f"   ID: {product_image.id}")
            print(f"   Alt Text: '{product_image.alt_text}'")
            print(f"   Sort Order: {product_image.sort_order}")
            
            # 清理测试数据
            product_image.delete()
            print("🧹 测试数据已清理")
            
        except Exception as e:
            print(f"❌ 商品图片创建失败: {e}")
            
        # 测试创建商品图片（提供alt_text）
        try:
            product_image = ProductImage.objects.create(
                product=product,
                image=test_image,
                alt_text="测试图片描述",
                sort_order=2
            )
            print(f"✅ 带alt_text的商品图片创建成功")
            print(f"   ID: {product_image.id}")
            print(f"   Alt Text: '{product_image.alt_text}'")
            print(f"   Sort Order: {product_image.sort_order}")
            
            # 清理测试数据
            product_image.delete()
            print("🧹 测试数据已清理")
            
        except Exception as e:
            print(f"❌ 带alt_text的商品图片创建失败: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == '__main__':
    test_product_image_creation()
