#!/usr/bin/env python
"""
测试统一登录页面功能
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('shop_front')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.test import Client
from users.models import User

def test_unified_login():
    """测试统一登录页面功能"""
    print("🔐 MARS BUY 统一登录页面测试")
    print("=" * 50)
    
    # 设置测试用户
    setup_test_users()
    
    print("\n🧪 测试场景:")
    print("   1. 普通用户选择普通用户登录 ✅")
    print("   2. 管理员选择管理员登录 ✅") 
    print("   3. 普通用户选择管理员登录 ❌")
    print("   4. 管理员选择普通用户登录 ❌")
    
    print("\n📋 测试结果:")
    
    # 测试1: 普通用户选择普通用户登录
    print("\n1️⃣ 普通用户选择普通用户登录")
    result1 = test_normal_user_normal_login()
    print(f"   结果: {'✅ 通过' if result1 else '❌ 失败'}")
    
    # 测试2: 管理员选择管理员登录
    print("\n2️⃣ 管理员选择管理员登录")
    result2 = test_admin_user_admin_login()
    print(f"   结果: {'✅ 通过' if result2 else '❌ 失败'}")
    
    # 测试3: 普通用户选择管理员登录
    print("\n3️⃣ 普通用户选择管理员登录（应该被拒绝）")
    result3 = test_normal_user_admin_login()
    print(f"   结果: {'✅ 正确拒绝' if result3 else '❌ 错误通过'}")
    
    # 测试4: 管理员选择普通用户登录
    print("\n4️⃣ 管理员选择普通用户登录（应该被拒绝）")
    result4 = test_admin_user_normal_login()
    print(f"   结果: {'✅ 正确拒绝' if result4 else '❌ 错误通过'}")
    
    print("\n" + "=" * 50)
    print("📊 测试总结:")
    total_tests = 4
    passed_tests = sum([result1, result2, result3, result4])
    print(f"   总测试数: {total_tests}")
    print(f"   通过数: {passed_tests}")
    print(f"   成功率: {passed_tests/total_tests*100:.1f}%")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！统一登录功能正常工作")
    else:
        print(f"\n⚠️ {total_tests - passed_tests} 个测试失败，请检查相关功能")
    
    print("\n🔗 访问地址:")
    print("   登录页面: http://127.0.0.1:8002/users/login/")
    print("   首页: http://127.0.0.1:8002/")

def setup_test_users():
    """设置测试用户"""
    print("🔧 设置测试用户...")
    
    # 创建或获取管理员用户
    admin_user, created = User.objects.get_or_create(
        username='admin123',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True,
            'is_active': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print("   ✅ 创建管理员用户: admin123")
    else:
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.save()
        print("   ✅ 管理员用户已存在: admin123")
    
    # 创建或获取普通用户
    normal_user, created = User.objects.get_or_create(
        username='user123',
        defaults={
            'email': '<EMAIL>',
            'is_staff': False,
            'is_superuser': False,
            'is_active': True
        }
    )
    if created:
        normal_user.set_password('user123')
        normal_user.save()
        print("   ✅ 创建普通用户: user123")
    else:
        normal_user.is_staff = False
        normal_user.is_superuser = False
        normal_user.save()
        print("   ✅ 普通用户已存在: user123")

def test_normal_user_normal_login():
    """测试普通用户选择普通用户登录"""
    try:
        client = Client()
        login_data = {
            'username': 'user123',
            'password': 'user123',
            'user_type': 'normal'
        }
        
        response = client.post('/users/login/', login_data, follow=True)
        
        # 检查是否成功登录并重定向到首页
        if response.status_code == 200 and 'home' in str(response.redirect_chain):
            return True
        return False
        
    except Exception as e:
        print(f"   异常: {str(e)}")
        return False

def test_admin_user_admin_login():
    """测试管理员选择管理员登录"""
    try:
        client = Client()
        login_data = {
            'username': 'admin123',
            'password': 'admin123',
            'user_type': 'admin'
        }
        
        response = client.post('/users/login/', login_data, follow=False)
        
        # 检查是否重定向到后台管理系统
        if response.status_code == 302 and '8003' in response.url:
            return True
        return False
        
    except Exception as e:
        print(f"   异常: {str(e)}")
        return False

def test_normal_user_admin_login():
    """测试普通用户选择管理员登录（应该被拒绝）"""
    try:
        client = Client()
        login_data = {
            'username': 'user123',
            'password': 'user123',
            'user_type': 'admin'
        }
        
        response = client.post('/users/login/', login_data)
        
        # 检查是否被拒绝（返回登录页面且有错误消息）
        if response.status_code == 200 and '没有管理员权限' in str(response.content):
            return True
        return False
        
    except Exception as e:
        print(f"   异常: {str(e)}")
        return False

def test_admin_user_normal_login():
    """测试管理员选择普通用户登录（应该被拒绝）"""
    try:
        client = Client()
        login_data = {
            'username': 'admin123',
            'password': 'admin123',
            'user_type': 'normal'
        }
        
        response = client.post('/users/login/', login_data)
        
        # 检查是否被拒绝（返回登录页面且有错误消息）
        if response.status_code == 200 and '请选择"管理员"登录类型' in str(response.content):
            return True
        return False
        
    except Exception as e:
        print(f"   异常: {str(e)}")
        return False

if __name__ == '__main__':
    test_unified_login()
