# Generated by Django 4.2.22 on 2025-06-08 11:28

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('goods', '0003_alter_product_main_image_alter_productimage_image_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('account', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='favorite',
            options={'ordering': ['-created_at'], 'verbose_name': '收藏', 'verbose_name_plural': '收藏'},
        ),
        migrations.RenameField(
            model_name='favorite',
            old_name='created_time',
            new_name='created_at',
        ),
        migrations.AlterField(
            model_name='favorite',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='goods.product', verbose_name='商品'),
        ),
        migrations.AlterField(
            model_name='favorite',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddIndex(
            model_name='favorite',
            index=models.Index(fields=['user', 'created_at'], name='account_fav_user_id_95f1b5_idx'),
        ),
        migrations.AddIndex(
            model_name='favorite',
            index=models.Index(fields=['product'], name='account_fav_product_e6cc67_idx'),
        ),
    ]
