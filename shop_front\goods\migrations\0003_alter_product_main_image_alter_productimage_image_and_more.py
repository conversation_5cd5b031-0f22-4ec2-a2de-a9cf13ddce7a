# Generated by Django 4.2.22 on 2025-06-08 11:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('goods', '0002_alter_category_icon_alter_product_main_image_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='product',
            name='main_image',
            field=models.ImageField(default='products/default.png', upload_to='products/', verbose_name='主图'),
        ),
        migrations.AlterField(
            model_name='productimage',
            name='image',
            field=models.ImageField(upload_to='products/', verbose_name='图片'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'is_active'], name='goods_produ_categor_8bd6ac_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name', 'price'], name='goods_produ_name_a8de4f_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_hot', 'is_new', 'is_active'], name='goods_produ_is_hot_7767f1_idx'),
        ),
        migrations.AddIndex(
            model_name='productimage',
            index=models.Index(fields=['product', 'sort_order'], name='goods_produ_product_6385ef_idx'),
        ),
    ]
