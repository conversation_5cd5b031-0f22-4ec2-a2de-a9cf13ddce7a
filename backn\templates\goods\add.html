{% extends 'base.html' %}

{% block title %}添加商品 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}添加商品{% endblock %}

{% block page_actions %}
<a href="{% url 'goods:list' %}" class="btn btn-outline-secondary">
    <i class="fas fa-arrow-left me-1"></i>返回列表
</a>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>商品信息
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">商品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category" class="form-label">商品分类 <span class="text-danger">*</span></label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">请选择分类</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">商品描述 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="4" required></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="price" class="form-label">商品价格 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           step="0.01" min="0" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="stock" class="form-label">库存数量</label>
                                <input type="number" class="form-control" id="stock" name="stock" 
                                       min="0" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="main_image" class="form-label">商品主图</label>
                        <input type="file" class="form-control" id="main_image" name="main_image" 
                               accept="image/*">
                        <div class="form-text">支持 JPG、PNG、GIF 格式，建议尺寸 800x800 像素</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                <label class="form-check-label" for="is_active">
                                    立即上架
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_hot" name="is_hot">
                                <label class="form-check-label" for="is_hot">
                                    设为热门
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_new" name="is_new" checked>
                                <label class="form-check-label" for="is_new">
                                    标记为新品
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'goods:list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存商品
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>添加提示
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>填写建议</h6>
                    <ul class="mb-0 small">
                        <li>商品名称要简洁明了，突出特色</li>
                        <li>详细描述商品的功能和特点</li>
                        <li>价格设置要合理，考虑市场竞争</li>
                        <li>上传高质量的商品图片</li>
                        <li>合理设置库存数量</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>注意事项</h6>
                    <ul class="mb-0 small">
                        <li>带 <span class="text-danger">*</span> 的字段为必填项</li>
                        <li>商品图片大小不超过 5MB</li>
                        <li>商品添加后可以随时编辑</li>
                        <li>下架的商品不会在前台显示</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>快速统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary mb-1">{{ total_products|default:0 }}</h5>
                            <small class="text-muted">总商品数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-1">{{ active_products|default:0 }}</h5>
                        <small class="text-muted">已上架</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 图片预览
    $('#main_image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // 如果已有预览图，则更新，否则创建新的
                let preview = $('#image-preview');
                if (preview.length === 0) {
                    $('#main_image').after(`
                        <div id="image-preview" class="mt-2">
                            <img src="" alt="预览" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                        </div>
                    `);
                    preview = $('#image-preview');
                }
                preview.find('img').attr('src', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 表单验证
    $('form').submit(function(e) {
        const name = $('#name').val().trim();
        const price = $('#price').val();
        const category = $('#category').val();
        const description = $('#description').val().trim();
        
        if (!name) {
            alert('请输入商品名称');
            $('#name').focus();
            e.preventDefault();
            return false;
        }
        
        if (!category) {
            alert('请选择商品分类');
            $('#category').focus();
            e.preventDefault();
            return false;
        }
        
        if (!description) {
            alert('请输入商品描述');
            $('#description').focus();
            e.preventDefault();
            return false;
        }
        
        if (!price || parseFloat(price) <= 0) {
            alert('请输入有效的商品价格');
            $('#price').focus();
            e.preventDefault();
            return false;
        }
        
        return true;
    });
});
</script>
{% endblock %}
