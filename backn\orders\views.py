from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from django.core.paginator import Paginator
from django.utils import timezone
from datetime import timedelta
import csv

from .models import Order, OrderItem
from admin_panel.models import AdminLog
from common.utils import get_client_ip, is_admin

@login_required
@user_passes_test(is_admin)
def order_list(request):
    """订单列表"""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    date_filter = request.GET.get('date', '')

    orders = Order.objects.select_related('user').all()

    # 搜索过滤
    if search_query:
        orders = orders.filter(
            Q(order_no__icontains=search_query) |
            Q(user__username__icontains=search_query) |
            Q(receiver_name__icontains=search_query) |
            Q(receiver_phone__icontains=search_query)
        )

    # 状态过滤
    if status_filter:
        orders = orders.filter(status=status_filter)

    # 日期过滤
    if date_filter:
        today = timezone.now().date()
        if date_filter == 'today':
            orders = orders.filter(created_time__date=today)
        elif date_filter == 'week':
            week_ago = today - timezone.timedelta(days=7)
            orders = orders.filter(created_time__date__gte=week_ago)
        elif date_filter == 'month':
            month_ago = today - timezone.timedelta(days=30)
            orders = orders.filter(created_time__date__gte=month_ago)

    orders = orders.order_by('-created_time')

    # 分页
    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 状态选择
    status_choices = Order.STATUS_CHOICES

    context = {
        'page_obj': page_obj,
        'status_choices': status_choices,
        'search_query': search_query,
        'status_filter': status_filter,
        'date_filter': date_filter,
    }

    return render(request, 'orders/list.html', context)

@login_required
@user_passes_test(is_admin)
def order_detail(request, order_id):
    """订单详情"""
    order = get_object_or_404(Order, id=order_id)
    order_items = OrderItem.objects.filter(order=order).select_related('product')

    context = {
        'order': order,
        'order_items': order_items,
    }

    return render(request, 'orders/detail.html', context)

@login_required
@user_passes_test(is_admin)
def update_order_status(request, order_id):
    """更新订单状态"""
    if request.method == 'POST':
        order = get_object_or_404(Order, id=order_id)
        new_status = request.POST.get('status')

        if new_status in dict(Order.STATUS_CHOICES):
            old_status = order.get_status_display()
            order.status = new_status

            # 更新时间字段
            if new_status == 'paid' and not order.paid_time:
                order.paid_time = timezone.now()
            elif new_status == 'shipped' and not order.shipped_time:
                order.shipped_time = timezone.now()
            elif new_status == 'delivered' and not order.delivered_time:
                order.delivered_time = timezone.now()

            order.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=request.user,
                action='更新订单状态',
                content_type='Order',
                object_id=str(order.id),
                object_repr=str(order),
                change_message=f'订单状态从 {old_status} 更新为 {order.get_status_display()}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, f'订单 {order.order_no} 状态已更新为 {order.get_status_display()}')
            return JsonResponse({'success': True, 'status': order.get_status_display()})
        else:
            return JsonResponse({'success': False, 'message': '无效的状态'})

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def order_search(request):
    """订单搜索API"""
    query = request.GET.get('q', '')
    if query:
        orders = Order.objects.filter(
            Q(order_no__icontains=query) |
            Q(user__username__icontains=query) |
            Q(receiver_name__icontains=query)
        ).select_related('user')[:10]

        results = []
        for order in orders:
            results.append({
                'id': order.id,
                'order_no': order.order_no,
                'user': order.user.username,
                'status': order.get_status_display(),
                'total_amount': str(order.total_amount),
                'created_time': order.created_time.strftime('%Y-%m-%d %H:%M'),
            })

        return JsonResponse({'results': results})

    return JsonResponse({'results': []})

@login_required
@user_passes_test(is_admin)
def export_orders(request):
    """导出订单"""
    # 获取过滤参数
    status_filter = request.GET.get('status', '')
    date_filter = request.GET.get('date', '')

    orders = Order.objects.select_related('user').all()

    # 应用过滤条件
    if status_filter:
        orders = orders.filter(status=status_filter)

    if date_filter:
        today = timezone.now().date()
        if date_filter == 'today':
            orders = orders.filter(created_time__date=today)
        elif date_filter == 'week':
            week_ago = today - timedelta(days=7)
            orders = orders.filter(created_time__date__gte=week_ago)
        elif date_filter == 'month':
            month_ago = today - timedelta(days=30)
            orders = orders.filter(created_time__date__gte=month_ago)

    # 创建CSV响应
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="orders.csv"'
    response.write('\ufeff')  # BOM for Excel

    writer = csv.writer(response)
    writer.writerow(['订单号', '用户', '状态', '订单总额', '实付金额', '收货人', '收货电话', '创建时间'])

    for order in orders:
        writer.writerow([
            order.order_no,
            order.user.username,
            order.get_status_display(),
            order.total_amount,
            order.final_amount,
            order.receiver_name,
            order.receiver_phone,
            order.created_time.strftime('%Y-%m-%d %H:%M:%S'),
        ])

    return response
