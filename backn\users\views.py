from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Count, Sum
from django.core.paginator import Paginator

from .models import User
from orders.models import Order
from admin_panel.models import AdminLog
from common.utils import get_client_ip, is_admin

@login_required
@user_passes_test(is_admin)
def user_list(request):
    """用户列表"""
    search_query = request.GET.get('search', '')
    status_filter = request.GET.get('status', '')
    vip_filter = request.GET.get('vip', '')

    # 分别查询管理员和普通用户
    admin_users = User.objects.filter(Q(is_staff=True) | Q(is_superuser=True))
    regular_users = User.objects.filter(is_staff=False, is_superuser=False)

    # 搜索过滤
    if search_query:
        admin_users = admin_users.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(phone__icontains=search_query)
        )
        regular_users = regular_users.filter(
            Q(username__icontains=search_query) |
            Q(email__icontains=search_query) |
            Q(phone__icontains=search_query)
        )

    # 状态过滤
    if status_filter == 'active':
        admin_users = admin_users.filter(is_active=True)
        regular_users = regular_users.filter(is_active=True)
    elif status_filter == 'inactive':
        admin_users = admin_users.filter(is_active=False)
        regular_users = regular_users.filter(is_active=False)

    # VIP过滤
    if vip_filter == 'vip':
        admin_users = admin_users.filter(is_vip=True)
        regular_users = regular_users.filter(is_vip=True)
    elif vip_filter == 'normal':
        admin_users = admin_users.filter(is_vip=False)
        regular_users = regular_users.filter(is_vip=False)

    # 添加订单统计
    admin_users = admin_users.annotate(
        order_count=Count('orders'),
        paid_order_count=Count('orders', filter=Q(orders__status='paid'))
    ).order_by('-date_joined')
    
    regular_users = regular_users.annotate(
        order_count=Count('orders'),
        paid_order_count=Count('orders', filter=Q(orders__status='paid'))
    ).order_by('-date_joined')

    # 分页
    admin_paginator = Paginator(admin_users, 10)
    regular_paginator = Paginator(regular_users, 10)
    
    admin_page_number = request.GET.get('admin_page')
    regular_page_number = request.GET.get('regular_page')
    
    admin_page_obj = admin_paginator.get_page(admin_page_number)
    regular_page_obj = regular_paginator.get_page(regular_page_number)

    context = {
        'admin_page_obj': admin_page_obj,
        'regular_page_obj': regular_page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'vip_filter': vip_filter,
        'total_users': admin_users.count() + regular_users.count(),
        'current_user': request.user,
    }

    return render(request, 'users/list.html', context)

@login_required
@user_passes_test(is_admin)
def user_detail(request, user_id):
    """用户详情"""
    user = get_object_or_404(User, id=user_id)

    # 获取用户订单
    orders = Order.objects.filter(user=user).order_by('-created_time')[:10]

    # 统计数据
    total_orders = Order.objects.filter(user=user).count()
    paid_orders = Order.objects.filter(user=user, status='paid').count()
    total_spent = Order.objects.filter(user=user, status='paid').aggregate(
        total=Sum('final_amount'))['total'] or 0

    context = {
        'user': user,
        'orders': orders,
        'total_orders': total_orders,
        'paid_orders': paid_orders,
        'total_spent': total_spent,
    }

    return render(request, 'users/detail.html', context)

@login_required
@user_passes_test(is_admin)
def toggle_user_status(request, user_id):
    """切换用户状态"""
    if request.method == 'POST':
        user = get_object_or_404(User, id=user_id)
        user.is_active = not user.is_active
        user.save()

        # 记录操作日志
        action = '激活用户' if user.is_active else '禁用用户'
        AdminLog.objects.create(
            admin=request.user,
            action=action,
            content_type='User',
            object_id=str(user.id),
            object_repr=str(user),
            change_message=f'{action}: {user.username}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        status = '激活' if user.is_active else '禁用'
        messages.success(request, f'用户 {user.username} 已{status}')

        return JsonResponse({'success': True, 'status': user.is_active})

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def user_search(request):
    """用户搜索API"""
    query = request.GET.get('q', '')
    if query:
        users = User.objects.filter(
            Q(username__icontains=query) |
            Q(email__icontains=query) |
            Q(phone__icontains=query)
        )[:10]

        results = []
        for user in users:
            results.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'phone': user.phone,
                'is_active': user.is_active,
                'is_vip': user.is_vip,
            })

        return JsonResponse({'results': results})

    return JsonResponse({'results': []})
