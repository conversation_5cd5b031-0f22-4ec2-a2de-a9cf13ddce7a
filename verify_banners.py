#!/usr/bin/env python3
"""
验证轮播图配置和文件状态
"""

import os
import sys
import django
from pathlib import Path

# 设置Django环境
sys.path.append('D:/python/shop/shop_front')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from home.models import Banner

def check_banner_files():
    """检查轮播图文件是否存在"""
    print("🖼️ 检查轮播图文件...")
    print("=" * 60)
    
    banner_dir = Path('D:/python/shop/media/banners')
    expected_files = [
        '轮播图 (1).png',
        '轮播图 (2).png', 
        '轮播图 (3).png',
        '轮播图.png'
    ]
    
    for filename in expected_files:
        file_path = banner_dir / filename
        if file_path.exists():
            file_size = file_path.stat().st_size
            print(f"✅ {filename} - 存在 ({file_size:,} 字节)")
        else:
            print(f"❌ {filename} - 不存在")
    
    print()

def check_banner_database():
    """检查轮播图数据库记录"""
    print("🗄️ 检查轮播图数据库记录...")
    print("=" * 60)
    
    try:
        banners = Banner.objects.all().order_by('sort_order')
        
        if banners.exists():
            print(f"📊 数据库中共有 {banners.count()} 条轮播图记录:")
            print()
            
            for i, banner in enumerate(banners, 1):
                status = "🟢 启用" if banner.is_active else "🔴 禁用"
                print(f"  {i}. 标题: {banner.title}")
                print(f"     图片: {banner.image}")
                print(f"     链接: {banner.url}")
                print(f"     排序: {banner.sort_order}")
                print(f"     状态: {status}")
                print(f"     创建时间: {banner.created_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 检查图片文件是否存在
                image_path = Path('D:/python/shop/media') / str(banner.image)
                if image_path.exists():
                    print(f"     文件状态: ✅ 存在")
                else:
                    print(f"     文件状态: ❌ 不存在")
                print()
        else:
            print("⚠️ 数据库中没有轮播图记录")
            print("💡 建议运行: python manage.py create_banners")
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
    
    print()

def check_template_usage():
    """检查模板中的轮播图使用"""
    print("📄 检查模板中的轮播图使用...")
    print("=" * 60)
    
    template_path = Path('D:/python/shop/shop_front/home/<USER>/home/<USER>')
    
    if template_path.exists():
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 检查关键代码
        checks = [
            ('轮播图容器', 'banner-box'),
            ('轮播图列表', 'banner-list'),
            ('轮播图项目', 'banner-item'),
            ('指示点', 'banner-dots'),
            ('左右箭头', 'banner-arrow'),
            ('JavaScript控制', 'showBanner'),
            ('自动轮播', 'startTimer'),
            ('默认图片1', '/media/banners/轮播图 (1).png'),
            ('默认图片2', '/media/banners/轮播图 (2).png'),
            ('默认图片3', '/media/banners/轮播图 (3).png'),
            ('默认图片4', '/media/banners/轮播图.png'),
        ]
        
        for name, keyword in checks:
            if keyword in content:
                print(f"✅ {name}: 已配置")
            else:
                print(f"❌ {name}: 未找到")
    else:
        print("❌ 首页模板文件不存在")
    
    print()

def check_media_settings():
    """检查媒体文件配置"""
    print("⚙️ 检查媒体文件配置...")
    print("=" * 60)
    
    try:
        from django.conf import settings
        
        print(f"MEDIA_URL: {settings.MEDIA_URL}")
        print(f"MEDIA_ROOT: {settings.MEDIA_ROOT}")
        
        media_root = Path(settings.MEDIA_ROOT)
        if media_root.exists():
            print(f"✅ MEDIA_ROOT 目录存在: {media_root}")
        else:
            print(f"❌ MEDIA_ROOT 目录不存在: {media_root}")
            
        banners_dir = media_root / 'banners'
        if banners_dir.exists():
            print(f"✅ banners 目录存在: {banners_dir}")
        else:
            print(f"❌ banners 目录不存在: {banners_dir}")
            
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
    
    print()

def generate_summary():
    """生成总结报告"""
    print("📋 轮播图状态总结")
    print("=" * 60)
    
    # 文件检查
    banner_dir = Path('D:/python/shop/media/banners')
    files_exist = all((banner_dir / f).exists() for f in [
        '轮播图 (1).png', '轮播图 (2).png', '轮播图 (3).png', '轮播图.png'
    ])
    
    # 数据库检查
    db_records = Banner.objects.filter(is_active=True).count()
    
    # 模板检查
    template_path = Path('D:/python/shop/shop_front/home/<USER>/home/<USER>')
    template_exists = template_path.exists()
    
    print(f"📁 轮播图文件: {'✅ 完整' if files_exist else '❌ 缺失'}")
    print(f"🗄️ 数据库记录: {'✅ 正常' if db_records > 0 else '❌ 无记录'} ({db_records} 条)")
    print(f"📄 模板文件: {'✅ 存在' if template_exists else '❌ 缺失'}")
    
    if files_exist and db_records > 0 and template_exists:
        print("\n🎉 轮播图配置完整，应该可以正常显示！")
        print("\n🔗 测试建议:")
        print("1. 启动前台服务器: python manage.py runserver 127.0.0.1:8001")
        print("2. 访问首页: http://127.0.0.1:8001/")
        print("3. 检查轮播图是否正常显示和切换")
    else:
        print("\n⚠️ 轮播图配置不完整，需要修复以下问题:")
        if not files_exist:
            print("- 检查 media/banners/ 目录下的图片文件")
        if db_records == 0:
            print("- 运行 python manage.py create_banners 创建数据库记录")
        if not template_exists:
            print("- 检查首页模板文件是否存在")

def main():
    """主函数"""
    print("🔍 MARS BUY 轮播图验证工具")
    print("=" * 80)
    print()
    
    try:
        check_banner_files()
        check_banner_database()
        check_template_usage()
        check_media_settings()
        generate_summary()
        
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
