from django import forms
from .models import Review, ReviewImage

class ReviewForm(forms.ModelForm):
    """商品评价表单"""
    class Meta:
        model = Review
        fields = ['content', 'score', 'is_anonymous']
        widgets = {
            'content': forms.Textarea(attrs={'rows': 4, 'placeholder': '请输入您的评价内容'}),
            'score': forms.RadioSelect(),
        }

class ReviewImageForm(forms.ModelForm):
    """评价图片表单"""
    class Meta:
        model = ReviewImage
        fields = ['image'] 