from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from .models import SystemConfig, SharedCategory


@admin.register(SystemConfig)
class SystemConfigAdmin(admin.ModelAdmin):
    list_display = ('key', 'value_short', 'description', 'is_active', 'updated_at')
    list_filter = ('is_active', 'created_at', 'updated_at')
    search_fields = ('key', 'value', 'description')
    ordering = ('key',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('key', 'value', 'description', 'is_active')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def value_short(self, obj):
        """显示截断的值"""
        if len(obj.value) > 50:
            return f"{obj.value[:50]}..."
        return obj.value
    value_short.short_description = _('配置值')


@admin.register(SharedCategory)
class SharedCategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'parent', 'level', 'is_active', 'sort_order')
    list_filter = ('is_active', 'level')
    search_fields = ('name', 'code')
    ordering = ('sort_order', 'code')
    readonly_fields = ('created_at', 'updated_at')
    list_editable = ('sort_order', 'is_active')
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'parent', 'level', 'is_active', 'sort_order')
        }),
        (_('时间信息'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    ) 