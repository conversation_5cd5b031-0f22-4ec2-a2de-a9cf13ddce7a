#!/usr/bin/env python3
"""
测试首页分类链接功能
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.urls import reverse
from django.test import Client
from django.contrib.auth import get_user_model

User = get_user_model()

def test_category_links():
    """测试分类链接功能"""
    print("🧪 测试首页分类链接功能")
    print("=" * 60)
    
    # 1. 测试URL配置
    print("\n1. 检查URL配置...")
    try:
        home_url = reverse('home:index')
        goods_list_url = reverse('goods:list')
        category_url = reverse('goods:category', kwargs={'category_id': 2})
        
        print(f"   ✅ 首页URL: {home_url}")
        print(f"   ✅ 商品列表URL: {goods_list_url}")
        print(f"   ✅ 分类页面URL: {category_url}")
        
    except Exception as e:
        print(f"   ❌ URL配置错误: {e}")
        return False
    
    # 2. 测试页面访问
    print("\n2. 测试页面访问...")
    client = Client()
    
    # 测试首页
    response = client.get(home_url)
    print(f"   首页状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("   ✅ 首页访问成功")
        
        # 检查页面内容
        content = response.content.decode('utf-8')
        
        # 检查分类链接
        if 'category-title-link' in content:
            print("   ✅ 分类按钮链接已添加")
        else:
            print("   ❌ 分类按钮链接未找到")
            
        if 'goods:list' in content:
            print("   ✅ 分类链接URL配置正确")
        else:
            print("   ❌ 分类链接URL配置错误")
            
        # 检查导航栏
        if 'top-navbar' in content:
            print("   ✅ 顶部导航栏已加载")
        else:
            print("   ❌ 顶部导航栏未找到")
            
        # 检查分类下拉菜单
        if 'category-nav-dropdown' in content:
            print("   ✅ 分类下拉菜单已加载")
        else:
            print("   ❌ 分类下拉菜单未找到")
            
    else:
        print(f"   ❌ 首页访问失败: {response.status_code}")
    
    # 3. 测试商品列表页面
    print("\n3. 测试商品列表页面...")
    response = client.get(goods_list_url)
    print(f"   商品列表页面状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("   ✅ 商品列表页面访问成功")
    else:
        print(f"   ❌ 商品列表页面访问失败: {response.status_code}")
    
    # 4. 测试分类页面
    print("\n4. 测试分类页面...")
    response = client.get(category_url)
    print(f"   分类页面状态码: {response.status_code}")
    
    if response.status_code == 200:
        print("   ✅ 分类页面访问成功")
    else:
        print(f"   ❌ 分类页面访问失败: {response.status_code}")
    
    return True

def test_navigation_links():
    """测试导航栏所有链接"""
    print("\n🔗 测试导航栏链接...")
    
    client = Client()
    
    # 测试导航栏中的各个链接
    nav_links = [
        ('首页', reverse('home:index')),
        ('商品列表', reverse('goods:list')),
        ('数码分类', reverse('goods:category', kwargs={'category_id': 2})),
        ('家居分类', reverse('goods:category', kwargs={'category_id': 26})),
        ('食品分类', reverse('goods:category', kwargs={'category_id': 39})),
    ]
    
    for name, url in nav_links:
        try:
            response = client.get(url)
            if response.status_code == 200:
                print(f"   ✅ {name}: {url}")
            else:
                print(f"   ❌ {name}: {url} (状态码: {response.status_code})")
        except Exception as e:
            print(f"   ❌ {name}: {url} (错误: {e})")

def main():
    """主函数"""
    print("🔗 首页分类链接功能测试")
    print("=" * 60)
    
    # 测试分类链接
    test_category_links()
    
    # 测试导航链接
    test_navigation_links()
    
    print("\n✨ 测试完成！")
    print("\n📋 功能说明:")
    print("1. 首页红色导航栏中的'分类'按钮现在可以点击")
    print("2. 点击'分类'按钮会跳转到商品列表页面")
    print("3. 鼠标悬停在'分类'按钮上会显示分类下拉菜单")
    print("4. 下拉菜单中的各个分类也都有对应的链接")
    
    print("\n🌐 访问链接:")
    print("   首页: http://127.0.0.1:8001/")
    print("   商品列表: http://127.0.0.1:8001/goods/")
    print("   数码分类: http://127.0.0.1:8001/goods/category/2/")
    print("   家居分类: http://127.0.0.1:8001/goods/category/26/")
    print("   食品分类: http://127.0.0.1:8001/goods/category/39/")

if __name__ == '__main__':
    main()
