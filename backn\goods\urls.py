from django.urls import path
from . import views

app_name = 'goods'

urlpatterns = [
    # 商品管理
    path('', views.product_list, name='list'),
    path('list/', views.product_list, name='list'),
    path('add/', views.product_add, name='add'),
    path('edit/<int:product_id>/', views.product_edit, name='edit'),
    path('delete/<int:product_id>/', views.product_delete, name='delete'),
    path('detail/<int:product_id>/', views.product_detail, name='detail'),
    path('toggle-status/<int:product_id>/', views.toggle_product_status, name='toggle_status'),
    path('batch-action/', views.batch_action, name='batch_action'),
    
    # 分类管理
    path('categories/', views.category_list, name='category_list'),
    path('categories/add/', views.category_add, name='category_add'),
    path('categories/edit/<int:category_id>/', views.category_edit, name='category_edit'),
    path('categories/delete/<int:category_id>/', views.category_delete, name='category_delete'),
    path('categories/batch-delete/', views.category_batch_delete, name='category_batch_delete'),
    path('categories/batch-action/', views.category_batch_action, name='category_batch_action'),
    path('categories/toggle-status/<int:category_id>/', views.toggle_category_status, name='toggle_category_status'),
]
