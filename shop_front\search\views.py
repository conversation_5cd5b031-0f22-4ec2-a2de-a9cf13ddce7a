from django.shortcuts import render
from django.http import JsonResponse
from django.views import View
from elasticsearch_dsl import Q
from .documents import ProductDocument

# Create your views here.

class ProductSearchView(View):
    def get(self, request):
        query = request.GET.get('q', '')
        category = request.GET.get('category')
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')
        
        search = ProductDocument.search()
        if query:
            search = search.query(
                Q('multi_match',
                  query=query,
                  fields=['name^3', 'description'])
            )
            
        if category:
            search = search.filter('term', category=category)
            
        if min_price or max_price:
            price_filter = {}
            if min_price:
                price_filter['gte'] = float(min_price)
            if max_price:
                price_filter['lte'] = float(max_price)
            search = search.filter('range', price=price_filter)
            
        response = search.execute()
        
        return render(request, 'search/results.html', {
            'query': query,
            'results': response,
            'total': response.hits.total.value
        })

def search(request):
    """商品搜索"""
    q = request.GET.get('q', '')
    page = int(request.GET.get('page', 1))
    per_page = int(request.GET.get('per_page', 10))
    
    if not q:
        return render(request, 'search/search.html', {
            'products': [],
            'q': q
        })
    
    # 构建搜索查询
    s = ProductDocument.search()
    s = s.query(Q('multi_match', query=q, fields=['name', 'description']))
    s = s.highlight('name', 'description')
    
    # 分页
    start = (page - 1) * per_page
    s = s[start:start + per_page]
    
    # 执行搜索
    response = s.execute()
    
    return render(request, 'search/search.html', {
        'products': response,
        'q': q
    })

def search_suggest(request):
    """搜索建议"""
    q = request.GET.get('q', '')
    
    if not q:
        return JsonResponse({'suggestions': []})
    
    # 构建搜索建议查询
    s = ProductDocument.search()
    s = s.suggest('name_suggest', q, completion={
        'field': 'name_suggest',
        'size': 10
    })
    
    # 执行查询
    response = s.execute()
    
    # 提取建议结果
    suggestions = []
    if hasattr(response, 'suggest'):
        for item in response.suggest.name_suggest[0].options:
            suggestions.append(item.text)
    
    return JsonResponse({'suggestions': suggestions})
