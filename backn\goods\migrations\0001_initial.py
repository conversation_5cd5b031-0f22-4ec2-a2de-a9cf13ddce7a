# Generated by Django 4.2.22 on 2025-06-09 12:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=100, verbose_name='分类名称')),
                ('icon', models.ImageField(blank=True, null=True, upload_to='categories/', verbose_name='分类图标')),
                ('sort_order', models.IntegerField(db_index=True, default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(db_index=True, default=True, verbose_name='是否启用')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='goods.category', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '商品分类',
                'verbose_name_plural': '商品分类',
                'ordering': ['sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=200, verbose_name='商品名称')),
                ('description', models.TextField(verbose_name='商品描述')),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='价格')),
                ('main_image', models.ImageField(default='products/default.png', upload_to='products/', verbose_name='主图')),
                ('stock', models.IntegerField(default=0, verbose_name='库存')),
                ('sales', models.IntegerField(default=0, verbose_name='销量')),
                ('is_hot', models.BooleanField(db_index=True, default=False, verbose_name='是否热门')),
                ('is_new', models.BooleanField(db_index=True, default=True, verbose_name='是否新品')),
                ('is_active', models.BooleanField(db_index=True, default=True, verbose_name='是否上架')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='goods.category', verbose_name='商品分类')),
            ],
            options={
                'verbose_name': '商品',
                'verbose_name_plural': '商品',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='products/', verbose_name='图片')),
                ('alt_text', models.CharField(blank=True, max_length=200, verbose_name='图片描述')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='goods.product', verbose_name='商品')),
            ],
            options={
                'verbose_name': '商品图片',
                'verbose_name_plural': '商品图片',
                'ordering': ['sort_order', 'created_time'],
                'indexes': [models.Index(fields=['product', 'sort_order'], name='goods_produ_product_6385ef_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['category', 'is_active'], name='goods_produ_categor_8bd6ac_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['name', 'price'], name='goods_produ_name_a8de4f_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['is_hot', 'is_new', 'is_active'], name='goods_produ_is_hot_7767f1_idx'),
        ),
        migrations.AddIndex(
            model_name='product',
            index=models.Index(fields=['created_time'], name='goods_produ_created_457656_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['parent', 'is_active'], name='goods_categ_parent__7c4d1b_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['sort_order'], name='goods_categ_sort_or_90d769_idx'),
        ),
    ]
