{% extends 'base.html' %}
{% load static %}

{% block title %}我的购物车 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 全局样式 */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    /* 购物车整体样式 */
    .cart-container {
        max-width: 1200px;
        margin: 30px auto;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        overflow: hidden;
    }

    /* 购物车头部 */
    .cart-header {
        padding: 25px 30px;
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .cart-title {
        font-size: 24px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .cart-title i {
        font-size: 28px;
        color: #ffeb3b;
    }

    .cart-actions button {
        padding: 10px 20px;
        border: none;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        background: rgba(255,255,255,0.2);
        color: white;
        backdrop-filter: blur(10px);
    }

    .cart-actions button:hover {
        background: rgba(255,255,255,0.3);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .select-all-label {
        display: flex;
        align-items: center;
        gap: 8px;
        color: white;
        font-weight: 600;
        cursor: pointer;
        padding: 10px 20px;
        border-radius: 25px;
        background: rgba(255,255,255,0.2);
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
    }

    .select-all-label:hover {
        background: rgba(255,255,255,0.3);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .select-all-label input[type="checkbox"] {
        width: 18px;
        height: 18px;
        accent-color: #ffeb3b;
        cursor: pointer;
    }

    /* 购物车列表 */
    .cart-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .cart-item {
        padding: 25px 30px;
        border-bottom: 1px solid rgba(220,53,69,0.1);
        display: grid;
        grid-template-columns: 40px 100px 1fr 150px 120px 80px;
        align-items: center;
        gap: 20px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
    }

    .cart-item:hover {
        background: linear-gradient(135deg, rgba(220,53,69,0.02) 0%, rgba(255,255,255,0.95) 100%);
        transform: translateX(5px);
        box-shadow: 0 5px 20px rgba(220,53,69,0.1);
    }

    .cart-item:last-child {
        border-bottom: none;
    }

    /* 复选框样式 */
    .select-item {
        width: 20px;
        height: 20px;
        accent-color: #dc3545;
        cursor: pointer;
    }

    /* 商品图片 */
    .cart-item-image {
        width: 100px;
        height: 100px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .cart-item-image:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(220,53,69,0.2);
    }

    .cart-item-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .cart-item-image:hover img {
        transform: scale(1.1);
    }

    /* 商品信息 */
    .cart-item-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .cart-item-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        line-height: 1.4;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .cart-item-price {
        color: #dc3545;
        font-weight: 700;
        font-size: 18px;
    }

    /* 数量控制 */
    .cart-item-quantity {
        display: flex;
        align-items: center;
        gap: 8px;
        background: rgba(220,53,69,0.05);
        padding: 8px;
        border-radius: 12px;
    }

    .quantity-btn {
        width: 35px;
        height: 35px;
        border: 2px solid #dc3545;
        border-radius: 50%;
        background: white;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        color: #dc3545;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .quantity-btn:hover {
        background: #dc3545;
        color: white;
        transform: scale(1.1);
    }

    .quantity-input {
        width: 60px;
        height: 35px;
        border: 2px solid rgba(220,53,69,0.2);
        border-radius: 8px;
        padding: 0;
        text-align: center;
        font-weight: 600;
        font-size: 16px;
        background: white;
    }

    .quantity-input:focus {
        outline: none;
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220,53,69,0.1);
    }

    /* 小计 */
    .cart-item-total {
        font-size: 18px;
        font-weight: 700;
        color: #333;
        text-align: center;
        padding: 10px;
        background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(255,107,107,0.1) 100%);
        border-radius: 10px;
    }

    /* 删除按钮 */
    .delete-item-btn {
        width: 40px;
        height: 40px;
        border: none;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff4757 0%, #dc3545 100%);
        color: white;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .delete-item-btn:hover {
        background: linear-gradient(135deg, #dc3545 0%, #b21e2f 100%);
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(220,53,69,0.3);
    }

    /* 满减活动区域 */
    .discount-section {
        padding: 25px 30px;
        background: linear-gradient(135deg, rgba(220,53,69,0.05) 0%, rgba(255,255,255,0.8) 100%);
        border-top: 1px solid rgba(220,53,69,0.1);
    }

    .discount-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .discount-title i {
        color: #dc3545;
        font-size: 20px;
    }

    .discount-rules {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
    }

    .discount-item {
        flex: 1;
        min-width: 200px;
        padding: 15px;
        background: white;
        border: 2px solid rgba(220,53,69,0.1);
        border-radius: 12px;
        text-align: center;
        transition: all 0.3s ease;
    }

    .discount-item.active {
        border-color: #dc3545;
        background: linear-gradient(135deg, rgba(220,53,69,0.1) 0%, rgba(255,107,107,0.1) 100%);
        box-shadow: 0 5px 15px rgba(220,53,69,0.2);
    }

    .discount-text {
        font-size: 16px;
        font-weight: 600;
        color: #dc3545;
        margin-bottom: 5px;
    }

    .discount-status {
        font-size: 14px;
        color: #666;
    }

    .discount-item.active .discount-status {
        color: #dc3545;
        font-weight: 600;
    }

    /* 当前优惠显示 */
    .current-discount {
        margin-top: 15px;
        padding: 15px 20px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border-radius: 12px;
        display: none;
        align-items: center;
        gap: 10px;
        animation: slideIn 0.5s ease-out;
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* 结算栏 */
    .cart-footer {
        padding: 25px 30px;
        background: linear-gradient(135deg, rgba(248,249,250,0.9) 0%, rgba(255,255,255,0.9) 100%);
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid rgba(220,53,69,0.1);
    }

    .total-info {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .total-label {
        font-size: 16px;
        color: #666;
    }

    .total-price {
        font-size: 28px;
        font-weight: 700;
        color: #dc3545;
        text-shadow: 0 1px 2px rgba(220,53,69,0.1);
    }

    .checkout-btn {
        padding: 15px 40px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border: none;
        border-radius: 30px;
        font-size: 18px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
        text-decoration: none;
        display: inline-block;
    }

    .checkout-btn:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    /* 空购物车样式 */
    .empty-cart {
        padding: 80px 40px;
        text-align: center;
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
        border-radius: 20px;
        margin: 40px 0;
    }

    .empty-cart i {
        font-size: 120px;
        color: #dc3545;
        margin-bottom: 30px;
        opacity: 0.7;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .empty-cart p {
        color: #666;
        margin-bottom: 30px;
        font-size: 18px;
        line-height: 1.6;
    }

    .empty-cart .btn {
        padding: 15px 40px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border-radius: 30px;
        text-decoration: none;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
        transition: all 0.3s ease;
    }

    .empty-cart .btn:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .cart-item {
            grid-template-columns: 30px 80px 1fr 100px 60px;
            gap: 15px;
            padding: 20px 15px;
        }

        .cart-item-name {
            font-size: 14px;
            max-width: 200px;
        }

        .cart-footer {
            flex-direction: column;
            gap: 20px;
            text-align: center;
        }

        .discount-rules {
            flex-direction: column;
        }

        .discount-item {
            min-width: auto;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="cart-container">
    {% if cart_items %}
    <div class="cart-header">
        <div class="cart-title">
            <i class="fas fa-shopping-cart"></i>
            我的购物车
        </div>
        <div class="cart-actions">
            <label class="select-all-label">
                <input type="checkbox" id="selectAll">
                <span>全选</span>
            </label>
            <button type="button" id="deleteSelected">
                <i class="fas fa-trash-alt"></i>
                删除选中
            </button>
        </div>
    </div>

    <ul class="cart-list">
        {% for item in cart_items %}
        <li class="cart-item">
            <input type="checkbox" class="select-item" data-product-id="{{ item.product.id }}">
            <div class="cart-item-image">
                <img src="{{ item.product.main_image.url }}" alt="{{ item.product.name }}">
            </div>
            <div class="cart-item-info">
                <div class="cart-item-name" title="{{ item.product.name }}">{{ item.product.name }}</div>
                <div class="cart-item-price">¥{{ item.product.price }}</div>
            </div>
            <div class="cart-item-quantity">
                <button type="button" class="quantity-btn minus" data-product-id="{{ item.product.id }}">-</button>
                <input type="number" class="quantity-input" value="{{ item.quantity }}" min="1" max="99" data-product-id="{{ item.product.id }}">
                <button type="button" class="quantity-btn plus" data-product-id="{{ item.product.id }}">+</button>
            </div>
            <div class="cart-item-total">¥{{ item.total_price }}</div>
            <button type="button" class="delete-item-btn" data-product-id="{{ item.product.id }}">
                <i class="fas fa-times"></i>
            </button>
        </li>
        {% endfor %}
    </ul>

    <!-- 满减活动区域 -->
    <div class="discount-section">
        <div class="discount-title">
            <i class="fas fa-fire"></i>
            满减活动
        </div>
        <div class="discount-rules">
            <div class="discount-item" data-min="1000" data-discount="200">
                <div class="discount-text">满1000减200</div>
                <div class="discount-status" id="discount-1000">
                    还需 ¥<span class="need-value">1000</span>
                </div>
            </div>
            <div class="discount-item" data-min="300" data-discount="50">
                <div class="discount-text">满300减50</div>
                <div class="discount-status" id="discount-300">
                    还需 ¥<span class="need-value">300</span>
                </div>
            </div>
            <div class="discount-item" data-min="100" data-discount="10">
                <div class="discount-text">满100减10</div>
                <div class="discount-status" id="discount-100">
                    还需 ¥<span class="need-value">100</span>
                </div>
            </div>
        </div>
        <div class="current-discount" id="current-discount">
            <i class="fas fa-check-circle"></i>
            <span>当前享受：<strong id="discount-text"></strong></span>
        </div>
    </div>

    <div class="cart-footer">
        <div class="total-info">
            <div class="total-label">总计金额：</div>
            <div class="total-price" id="total-amount">¥{{ total_price }}</div>
        </div>
        <a href="{% url 'order:create' %}" class="checkout-btn">
            <i class="fas fa-credit-card"></i>
            立即结算
        </a>
    </div>
    {% else %}
    <div class="empty-cart">
        <i class="fas fa-shopping-cart"></i>
        <p>购物车空空如也，快去选购心仪的商品吧~</p>
        <a href="{% url 'goods:list' %}" class="btn">
            <i class="fas fa-shopping-bag"></i>
            前往商城
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 获取CSRF Token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        const csrfToken = getCookie('csrftoken');

        // 计算满减优惠
        function calculateDiscount(amount) {
            const discountRules = [
                { min: 1000, discount: 200 },
                { min: 300, discount: 50 },
                { min: 100, discount: 10 }
            ];

            for (let rule of discountRules) {
                if (amount >= rule.min) {
                    return {
                        discount: rule.discount,
                        rule: rule,
                        text: `满${rule.min}减${rule.discount}`
                    };
                }
            }

            return { discount: 0, rule: null, text: '' };
        }

        // 更新满减活动显示
        function updateDiscountDisplay(amount, discountInfo) {
            const discountItems = document.querySelectorAll('.discount-item');
            const currentDiscountDiv = document.getElementById('current-discount');

            discountItems.forEach(item => {
                item.classList.remove('active');
                const minAmount = parseInt(item.dataset.min);
                const needElement = item.querySelector('.need-value');
                const statusElement = item.querySelector('.discount-status');

                if (amount >= minAmount) {
                    item.classList.add('active');
                    statusElement.innerHTML = '<span style="color: #dc3545; font-weight: 600;">✓ 已满足条件</span>';
                } else {
                    const needAmount = minAmount - amount;
                    needElement.textContent = needAmount.toFixed(2);
                    statusElement.innerHTML = `还需 ¥<span class="need-value">${needAmount.toFixed(2)}</span>`;
                }
            });

            if (discountInfo.discount > 0) {
                currentDiscountDiv.style.display = 'flex';
                document.getElementById('discount-text').textContent = discountInfo.text + ` (已减¥${discountInfo.discount})`;
            } else {
                currentDiscountDiv.style.display = 'none';
            }
        }

        // 更新购物车总价
        function updateCartSummary() {
            const selectedItems = document.querySelectorAll('.select-item:checked');
            let totalAmount = 0;

            selectedItems.forEach(checkbox => {
                const cartItem = checkbox.closest('.cart-item');
                const price = parseFloat(cartItem.querySelector('.cart-item-total').textContent.replace(/[^0-9.]/g, ''));
                totalAmount += price;
            });

            const discountInfo = calculateDiscount(totalAmount);
            const finalAmount = totalAmount - discountInfo.discount;

            updateDiscountDisplay(totalAmount, discountInfo);
            document.getElementById('total-amount').textContent = `¥${finalAmount.toFixed(2)}`;
        }

        // 数量增减按钮事件
        const quantityBtns = document.querySelectorAll('.quantity-btn');
        quantityBtns.forEach(button => {
            button.addEventListener('click', function() {
                const productId = this.dataset.productId;
                const input = this.parentElement.querySelector('.quantity-input');
                let quantity = parseInt(input.value);

                if (this.classList.contains('plus')) {
                    quantity = Math.min(quantity + 1, 99);
                } else if (this.classList.contains('minus')) {
                    quantity = Math.max(quantity - 1, 1);
                }

                input.value = quantity;
                updateCartItem(productId, quantity);
            });
        });

        // 数量输入框事件
        const quantityInputs = document.querySelectorAll('.quantity-input');
        quantityInputs.forEach(input => {
            input.addEventListener('change', function() {
                const productId = this.dataset.productId;
                const quantity = Math.max(Math.min(parseInt(this.value) || 1, 99), 1);
                this.value = quantity;
                updateCartItem(productId, quantity);
            });
        });

        // 全选功能
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const selectItems = document.querySelectorAll('.select-item');
                selectItems.forEach(checkbox => {
                    checkbox.checked = this.checked;
                });
                updateCartSummary();
            });
        }

        // 单个商品选择事件
        const selectItems = document.querySelectorAll('.select-item');
        selectItems.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateCartSummary();

                // 更新全选状态
                const allCheckboxes = document.querySelectorAll('.select-item');
                const checkedCheckboxes = document.querySelectorAll('.select-item:checked');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = allCheckboxes.length === checkedCheckboxes.length;
                }
            });
        });

        // 删除单个商品
        const deleteItemBtns = document.querySelectorAll('.delete-item-btn');
        deleteItemBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const productId = this.dataset.productId;
                if (confirm('确认删除该商品？')) {
                    deleteCartItems([productId]);
                }
            });
        });

        // 删除选中商品
        const deleteSelectedBtn = document.getElementById('deleteSelected');
        if (deleteSelectedBtn) {
            deleteSelectedBtn.addEventListener('click', function() {
                const selectedCheckboxes = document.querySelectorAll('.select-item:checked');
                const productIds = Array.from(selectedCheckboxes).map(checkbox => checkbox.dataset.productId);

                if (productIds.length === 0) {
                    showSystemMessage('请选择要删除的商品', '批量删除', 'warning');
                    return;
                }

                if (confirm('确认删除选中的商品？')) {
                    deleteCartItems(productIds);
                }
            });
        }

        // 更新购物车商品
        function updateCartItem(productId, quantity) {
            fetch("{% url 'order:cart_update' %}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken
                },
                body: `product_id=${productId}&quantity=${quantity}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    if (quantity <= 0) {
                        // 删除商品行
                        const cartItem = document.querySelector(`[data-product-id="${productId}"]`).closest('.cart-item');
                        cartItem.style.transition = 'all 0.3s ease';
                        cartItem.style.opacity = '0';
                        cartItem.style.transform = 'translateX(-100%)';
                        setTimeout(() => {
                            cartItem.remove();
                            updateCartSummary();
                            if (document.querySelectorAll('.cart-item').length === 0) {
                                location.reload();
                            }
                        }, 300);
                    } else {
                        // 更新小计
                        const cartItem = document.querySelector(`[data-product-id="${productId}"]`).closest('.cart-item');
                        const totalElement = cartItem.querySelector('.cart-item-total');
                        totalElement.textContent = `¥${data.total_price}`;
                        updateCartSummary();
                    }
                } else {
                    showSystemMessage(data.msg || '更新失败', '更新购物车', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showSystemMessage('更新失败', '更新购物车', 'error');
            });
        }

        // 删除购物车商品
        function deleteCartItems(productIds) {
            fetch("{% url 'order:cart_delete' %}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({ product_ids: productIds })
            })
            .then(response => response.json())
            .then(data => {
                if (data.code === 200) {
                    // 删除前端元素
                    productIds.forEach(productId => {
                        const cartItem = document.querySelector(`[data-product-id="${productId}"]`).closest('.cart-item');
                        if (cartItem) {
                            cartItem.style.transition = 'all 0.3s ease';
                            cartItem.style.opacity = '0';
                            cartItem.style.transform = 'translateX(-100%)';
                            setTimeout(() => {
                                cartItem.remove();
                                updateCartSummary();
                                if (document.querySelectorAll('.cart-item').length === 0) {
                                    location.reload();
                                }
                            }, 300);
                        }
                    });
                } else {
                    showSystemMessage(data.msg || '删除失败', '删除商品', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showSystemMessage('删除失败', '删除商品', 'error');
            });
        }

        // 初始化
        updateCartSummary();
    });
</script>
{% endblock %}
