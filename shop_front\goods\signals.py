"""
商品信号处理器
确保商品修改时前台能实时更新
"""
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.core.cache import cache
from django.utils import timezone
from .models import Product, Category, ProductImage
import logging

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Product)
def product_post_save(sender, instance, created, **kwargs):
    """商品保存后的信号处理"""
    try:
        # 清理相关缓存
        clear_product_cache(instance)
        
        # 记录日志
        action = "创建" if created else "更新"
        logger.info(f"商品{action}: {instance.name} (ID: {instance.id})")
        
        # 如果商品状态发生变化，记录详细信息
        if not created:
            logger.info(f"商品状态 - 上架: {instance.is_active}, 热门: {instance.is_hot}, 新品: {instance.is_new}")
        
    except Exception as e:
        logger.error(f"商品保存信号处理失败: {e}")

@receiver(post_delete, sender=Product)
def product_post_delete(sender, instance, **kwargs):
    """商品删除后的信号处理"""
    try:
        # 清理相关缓存
        clear_product_cache(instance)
        
        # 记录日志
        logger.info(f"商品删除: {instance.name} (ID: {instance.id})")
        
    except Exception as e:
        logger.error(f"商品删除信号处理失败: {e}")

@receiver(post_save, sender=Category)
def category_post_save(sender, instance, created, **kwargs):
    """分类保存后的信号处理"""
    try:
        # 清理分类相关缓存
        clear_category_cache(instance)
        
        # 如果分类状态发生变化，清理该分类下所有商品的缓存
        if not created:
            products = Product.objects.filter(category=instance)
            for product in products:
                clear_product_cache(product)
        
        action = "创建" if created else "更新"
        logger.info(f"分类{action}: {instance.name} (ID: {instance.id}, 激活: {instance.is_active})")
        
    except Exception as e:
        logger.error(f"分类保存信号处理失败: {e}")

@receiver(post_save, sender=ProductImage)
def product_image_post_save(sender, instance, created, **kwargs):
    """商品图片保存后的信号处理"""
    try:
        # 清理商品缓存
        clear_product_cache(instance.product)
        
        action = "添加" if created else "更新"
        logger.info(f"商品图片{action}: 商品 {instance.product.name}")
        
    except Exception as e:
        logger.error(f"商品图片保存信号处理失败: {e}")

@receiver(post_delete, sender=ProductImage)
def product_image_post_delete(sender, instance, **kwargs):
    """商品图片删除后的信号处理"""
    try:
        # 清理商品缓存
        clear_product_cache(instance.product)
        
        logger.info(f"商品图片删除: 商品 {instance.product.name}")
        
    except Exception as e:
        logger.error(f"商品图片删除信号处理失败: {e}")

def clear_product_cache(product):
    """清理商品相关缓存"""
    try:
        # 清理商品详情缓存
        cache.delete(f'product_detail_{product.id}')
        
        # 清理商品列表缓存
        cache.delete('product_list_all')
        cache.delete('product_list_active')
        cache.delete('product_list_hot')
        cache.delete('product_list_new')
        
        # 清理分类商品缓存
        if product.category:
            cache.delete(f'category_products_{product.category.id}')
        
        # 清理首页缓存
        cache.delete('homepage_products')
        cache.delete('homepage_hot_products')
        cache.delete('homepage_new_products')
        cache.delete('homepage_seckill_products')
        
        # 清理搜索缓存
        cache.delete_many([
            'search_results_*',
            'product_search_*'
        ])
        
        logger.debug(f"已清理商品缓存: {product.name}")
        
    except Exception as e:
        logger.error(f"清理商品缓存失败: {e}")

def clear_category_cache(category):
    """清理分类相关缓存"""
    try:
        # 清理分类缓存
        cache.delete(f'category_detail_{category.id}')
        cache.delete('category_list_all')
        cache.delete('category_list_active')
        
        # 清理分类商品缓存
        cache.delete(f'category_products_{category.id}')
        
        logger.debug(f"已清理分类缓存: {category.name}")
        
    except Exception as e:
        logger.error(f"清理分类缓存失败: {e}")

def clear_all_cache():
    """清理所有相关缓存"""
    try:
        cache_keys = [
            # 商品缓存
            'product_list_all',
            'product_list_active',
            'product_list_hot',
            'product_list_new',
            
            # 分类缓存
            'category_list_all',
            'category_list_active',
            
            # 首页缓存
            'homepage_products',
            'homepage_hot_products',
            'homepage_new_products',
            'homepage_seckill_products',
        ]
        
        cache.delete_many(cache_keys)
        logger.info("已清理所有商品相关缓存")
        
    except Exception as e:
        logger.error(f"清理所有缓存失败: {e}")

# 实时更新标记
def mark_product_updated(product_id):
    """标记商品已更新，用于前台实时检测"""
    try:
        cache.set(f'product_updated_{product_id}', timezone.now().timestamp(), timeout=300)  # 5分钟过期
        cache.set('products_last_update', timezone.now().timestamp(), timeout=3600)  # 1小时过期
        
    except Exception as e:
        logger.error(f"标记商品更新失败: {e}")

def get_last_update_time():
    """获取最后更新时间"""
    try:
        return cache.get('products_last_update', 0)
    except Exception as e:
        logger.error(f"获取最后更新时间失败: {e}")
        return 0

def is_product_recently_updated(product_id, check_time=None):
    """检查商品是否最近更新过"""
    try:
        if check_time is None:
            check_time = timezone.now().timestamp() - 300  # 5分钟内
        
        update_time = cache.get(f'product_updated_{product_id}', 0)
        return update_time > check_time
        
    except Exception as e:
        logger.error(f"检查商品更新状态失败: {e}")
        return False
