#!/usr/bin/env python3
"""
验证潮流服饰相关代码已被完全删除
"""

import os
import re
from pathlib import Path

def search_files_for_patterns(directory, patterns, extensions):
    """在指定目录中搜索包含特定模式的文件"""
    results = []
    
    for ext in extensions:
        for file_path in Path(directory).rglob(f"*.{ext}"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                for pattern in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        line_content = content.split('\n')[line_num - 1].strip()
                        results.append({
                            'file': str(file_path),
                            'line': line_num,
                            'pattern': pattern,
                            'content': line_content
                        })
            except Exception as e:
                print(f"读取文件 {file_path} 时出错: {e}")
    
    return results

def main():
    """主函数"""
    print("🔍 验证潮流服饰相关代码删除情况...")
    print("=" * 80)
    
    # 搜索模式
    patterns = [
        r'潮流服饰',
        r'fashion-section',
        r'clothing_products',
        r'fashion-grid',
        r'fashion-card',
        r'fashion-image',
        r'fashion-info',
        r'fashion-name',
        r'fashion-price',
        r'fashion-btn',
        r'fashion-actions',
        r'fashion-action-btn',
        r'section-header',
        r'section-title',
        r'section-icon',
        r'title-highlight',
        r'view-all'
    ]
    
    # 搜索目录和文件类型
    search_configs = [
        {
            'directory': 'D:/python/shop/shop_front/templates',
            'extensions': ['html'],
            'name': '前台模板文件'
        },
        {
            'directory': 'D:/python/shop/shop_front/home/<USER>',
            'extensions': ['html'],
            'name': '首页模板文件'
        },
        {
            'directory': 'D:/python/shop/shop_front/goods/templates',
            'extensions': ['html'],
            'name': '商品模板文件'
        },
        {
            'directory': 'D:/python/shop/shop_front',
            'extensions': ['py'],
            'name': '前台Python文件'
        }
    ]
    
    total_matches = 0
    
    for config in search_configs:
        print(f"\n📁 搜索 {config['name']}...")
        print("-" * 60)
        
        if not os.path.exists(config['directory']):
            print(f"⚠️ 目录不存在: {config['directory']}")
            continue
        
        results = search_files_for_patterns(
            config['directory'], 
            patterns, 
            config['extensions']
        )
        
        if results:
            print(f"❌ 发现 {len(results)} 个匹配项:")
            for result in results:
                rel_path = os.path.relpath(result['file'], 'D:/python/shop')
                print(f"  文件: {rel_path}")
                print(f"  行号: {result['line']}")
                print(f"  模式: {result['pattern']}")
                print(f"  内容: {result['content'][:100]}...")
                print()
            total_matches += len(results)
        else:
            print(f"✅ 未发现潮流服饰相关代码")
    
    print(f"\n🎯 验证结果总结:")
    print("=" * 60)
    
    if total_matches == 0:
        print("🎉 所有潮流服饰相关代码已成功删除!")
        print("✅ 首页潮流服饰区域已删除")
        print("✅ 商品列表页潮流服饰区域已删除")
        print("✅ 相关CSS样式已删除")
        print("✅ 相关视图代码已删除")
    else:
        print(f"⚠️ 仍有 {total_matches} 个潮流服饰相关代码需要清理")
        print("请检查上述文件并手动删除相关代码")
    
    print(f"\n📝 建议测试:")
    print("1. 访问首页: http://127.0.0.1:8001/")
    print("2. 访问商品列表: http://127.0.0.1:8001/goods/")
    print("3. 访问分类页面: http://127.0.0.1:8001/goods/category/334/")
    print("4. 确认页面中不再显示潮流服饰相关内容")
    
    # 检查特定文件
    print(f"\n🔍 重点检查文件:")
    important_files = [
        'D:/python/shop/shop_front/home/<USER>/home/<USER>',
        'D:/python/shop/shop_front/templates/goods/product_list.html',
        'D:/python/shop/shop_front/home/<USER>',
        'D:/python/shop/shop_front/goods/views.py'
    ]
    
    for file_path in important_files:
        if os.path.exists(file_path):
            rel_path = os.path.relpath(file_path, 'D:/python/shop')
            results = search_files_for_patterns(
                os.path.dirname(file_path), 
                patterns, 
                [os.path.splitext(file_path)[1][1:]]
            )
            file_results = [r for r in results if r['file'] == file_path]
            
            if file_results:
                print(f"❌ {rel_path}: 发现 {len(file_results)} 个匹配项")
            else:
                print(f"✅ {rel_path}: 已清理完成")
        else:
            print(f"⚠️ 文件不存在: {file_path}")

if __name__ == "__main__":
    main()
