from django.urls import path
from . import views

app_name = 'account'

urlpatterns = [
    # 登录和退出功能已重定向到users应用
    path('login/', views.login_view, name='login'),  # 重定向到users:login
    path('admin-login/', views.admin_login_view, name='admin_login'),
    path('logout/', views.logout_view, name='logout'),  # 重定向到users:logout

    # 测试页面
    path('test-buttons/', views.test_buttons, name='test_buttons'),

    # 收藏夹相关URL
    path('favorites/', views.favorite_list, name='favorite_list'),
    path('add-favorite/<int:product_id>/', views.add_to_favorite, name='add_favorite'),
    path('remove-from-favorite/<int:product_id>/', views.remove_from_favorite, name='remove_from_favorite'),

    # 管理员商品管理URL
    path('admin/dashboard/', views.admin_dashboard, name='admin_dashboard'),
    path('admin/products/', views.product_list, name='product_list'),
    path('admin/products/add/', views.product_add, name='product_add'),
    path('admin/products/<int:product_id>/edit/', views.product_edit, name='product_edit'),
]