{% extends 'base.html' %}

{% block title %}确认订单 - MARS BUY{% endblock %}

{% load static %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    .container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
    }

    .confirm-container {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 40px;
        margin: 20px 0;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
    }

    .confirm-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .confirm-title {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .product-info {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        gap: 20px;
    }

    .product-image {
        width: 120px;
        height: 120px;
        border-radius: 10px;
        object-fit: cover;
    }

    .product-details h3 {
        color: #333;
        margin-bottom: 10px;
        font-size: 20px;
    }

    .product-price {
        color: #dc3545;
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .product-quantity {
        color: #666;
        font-size: 16px;
    }

    .address-section {
        margin-bottom: 30px;
    }

    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #555;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e0e0e0;
        border-radius: 10px;
        font-size: 14px;
        transition: all 0.3s ease;
        box-sizing: border-box;
    }

    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220,53,69,0.1);
    }

    .order-summary {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px dashed #ddd;
    }

    .summary-item:last-child {
        border-bottom: none;
        font-size: 18px;
        font-weight: 700;
        color: #dc3545;
    }

    .action-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
    }

    .btn {
        padding: 15px 40px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        box-shadow: 0 8px 25px rgba(108,117,125,0.3);
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(108,117,125,0.4);
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .confirm-container {
            padding: 20px;
            margin: 10px;
        }
        
        .product-info {
            flex-direction: column;
            text-align: center;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="confirm-container">
        <div class="confirm-header">
            <h1 class="confirm-title">
                <i class="fas fa-check-circle"></i>
                确认订单
            </h1>
        </div>

        <form method="post" action="{% url 'order:buy_now' %}">
            {% csrf_token %}
            <input type="hidden" name="product_id" value="{{ product.id }}">
            <input type="hidden" name="quantity" value="{{ quantity }}">

            <!-- 商品信息 -->
            <div class="product-info">
                <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="product-image">
                <div class="product-details">
                    <h3>{{ product.name }}</h3>
                    <div class="product-price">¥{{ product.price }}</div>
                    <div class="product-quantity">数量：{{ quantity }}</div>
                </div>
            </div>

            <!-- 收货地址 -->
            <div class="address-section">
                <h3 class="section-title">
                    <i class="fas fa-map-marker-alt"></i>
                    收货信息
                </h3>
                <div class="form-group">
                    <label for="receiver">收货人姓名</label>
                    <input type="text" id="receiver" name="receiver" value="{{ user.username }}" required>
                </div>
                <div class="form-group">
                    <label for="receiver_mobile">联系电话</label>
                    <input type="tel" id="receiver_mobile" name="receiver_mobile" placeholder="请输入手机号码" required>
                </div>
                <div class="form-group">
                    <label for="address">收货地址</label>
                    <textarea id="address" name="address" rows="3" placeholder="请输入详细收货地址" required></textarea>
                </div>
                <div class="form-group">
                    <label for="remark">订单备注</label>
                    <textarea id="remark" name="remark" rows="2" placeholder="选填，对本次交易的说明"></textarea>
                </div>
            </div>

            <!-- 订单汇总 -->
            <div class="order-summary">
                <h3 class="section-title">
                    <i class="fas fa-calculator"></i>
                    订单汇总
                </h3>
                <div class="summary-item">
                    <span>商品金额：</span>
                    <span>¥{{ total_amount }}</span>
                </div>
                <div class="summary-item">
                    <span>运费：</span>
                    <span>免运费</span>
                </div>
                <div class="summary-item">
                    <span>应付总额：</span>
                    <span>¥{{ total_amount }}</span>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
                <a href="{% url 'goods:detail' product.id %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    返回商品页
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-credit-card"></i>
                    提交订单
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
