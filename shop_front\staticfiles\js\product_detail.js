// 商品详情页的JavaScript功能

// 更新商品数量
function updateQuantity(delta) {
    const input = document.getElementById('quantity');
    let value = parseInt(input.value) + delta;
    value = Math.max(1, value);  // 确保数量不小于1
    input.value = value;
}

// 添加到购物车
function addToCart(productId) {
    const quantity = document.getElementById('quantity').value;
    fetch('/order/cart/add/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: quantity
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            alert('添加成功');
        } else {
            alert('添加失败：' + data.msg);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('添加失败，请稍后重试');
    });
}

// 立即购买
function buyNow(productId) {
    const quantity = document.getElementById('quantity').value;
    window.location.href = `/order/create/?product_id=${productId}&quantity=${quantity}`;
}

// 获取Cookie
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 更改主图
function changeMainImage(thumbnail, imageUrl) {
    // 更新主图
    document.getElementById('main-image').src = imageUrl;
    
    // 更新缩略图选中状态
    const thumbnails = document.querySelectorAll('.product-thumbnail');
    thumbnails.forEach(item => {
        item.classList.remove('active');
    });
    thumbnail.classList.add('active');
}

// 打开图片预览弹窗
function openImageModal(imageUrl) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    
    modal.style.display = "block";
    modalImg.src = imageUrl;
}

// 关闭图片预览弹窗
function closeImageModal() {
    document.getElementById('imageModal').style.display = "none";
}

// 点击弹窗外区域关闭弹窗
window.addEventListener('click', function(event) {
    const modal = document.getElementById('imageModal');
    if (event.target == modal) {
        modal.style.display = "none";
    }
}); 