<!-- 通用确认对话框模板 -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body">
                <div class="confirm-icon">
                    <i id="confirmIcon" class="fas fa-question-circle"></i>
                </div>
                <h5 class="confirm-title" id="confirmTitle">确认操作</h5>
                <p class="confirm-message" id="confirmMessage">您确定要执行此操作吗？</p>
                <div class="confirm-buttons">
                    <button type="button" class="btn-cancel" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> 取消
                    </button>
                    <button type="button" class="btn-confirm" id="confirmButton">
                        <i id="confirmButtonIcon" class="fas fa-check"></i> 确认
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 通用消息提示组件 -->
<div id="messageContainer" class="message-container" style="display: none;">
    <div id="messageBox" class="message">
        <i id="messageIcon" class="fas fa-info-circle"></i>
        <span id="messageText">操作成功</span>
    </div>
</div>

<!-- 系统消息提示框 -->
<div id="systemMessageBox" class="system-message-box" style="display: none;">
    <div class="system-message-title">
        <span id="systemMessageTitle">消息提示</span>
    </div>
    <div class="system-message-content">
        <p id="systemMessageText">操作成功</p>
    </div>
    <div class="system-message-footer">
        <button id="systemMessageBtn" class="system-message-btn">确定</button>
    </div>
</div>

<style>
/* 确认对话框样式 */
#confirmModal .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

#confirmModal .modal-body {
    text-align: center;
    padding: 30px;
}

.confirm-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    border: 3px solid #dc3545;
    background: #fff;
    position: relative;
    animation: iconShake 0.5s ease-in-out;
}

.confirm-icon i {
    font-size: 40px;
    color: #dc3545;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.confirm-icon.warning i {
    color: #ffc107;
}

.confirm-icon.warning {
    border-color: #ffc107;
}

.confirm-icon.success i {
    color: #28a745;
}

.confirm-icon.success {
    border-color: #28a745;
}

.confirm-icon.info i {
    color: #17a2b8;
}

.confirm-icon.info {
    border-color: #17a2b8;
}

.confirm-title {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
}

.confirm-message {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
    padding: 0 20px;
}

.confirm-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.confirm-buttons button {
    min-width: 120px;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.confirm-buttons .btn-cancel {
    background: #f8f9fa;
    border: 1px solid #ddd;
    color: #666;
}

.confirm-buttons .btn-cancel:hover {
    background: #e9ecef;
    border-color: #c8c9ca;
}

.confirm-buttons .btn-confirm {
    background: #dc3545;
    border: none;
    color: white;
}

.confirm-buttons .btn-confirm:hover {
    background: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.confirm-buttons .btn-confirm.warning {
    background: #ffc107;
    color: #212529;
}

.confirm-buttons .btn-confirm.warning:hover {
    background: #e0a800;
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
}

.confirm-buttons .btn-confirm.success {
    background: #28a745;
}

.confirm-buttons .btn-confirm.success:hover {
    background: #218838;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
}

.confirm-buttons .btn-confirm.info {
    background: #17a2b8;
}

.confirm-buttons .btn-confirm.info:hover {
    background: #138496;
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
}

@keyframes iconShake {
    0%, 100% { transform: rotate(0); }
    20%, 60% { transform: rotate(-10deg); }
    40%, 80% { transform: rotate(10deg); }
}

/* 动画效果 */
.modal.fade .modal-dialog {
    transform: translate(0, -50px) scale(0.8);
    opacity: 0;
    transition: all 0.3s ease-in-out;
}

.modal.show .modal-dialog {
    transform: translate(0, 0) scale(1);
    opacity: 1;
}

/* 背景遮罩效果 */
.modal-backdrop.show {
    opacity: 0.7;
}

/* 消息提示样式 */
.message-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    width: 100%;
    max-width: 400px;
    text-align: center;
    pointer-events: none;
}

.message {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin: 10px;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: messageSlideIn 0.3s ease-out;
    pointer-events: auto;
}

.message.success {
    border-left: 4px solid #28a745;
}

.message.error {
    border-left: 4px solid #dc3545;
}

.message.info {
    border-left: 4px solid #17a2b8;
}

.message.warning {
    border-left: 4px solid #ffc107;
}

.message i {
    margin-right: 12px;
    font-size: 20px;
}

.message i.success {
    color: #28a745;
}

.message i.error {
    color: #dc3545;
}

.message i.info {
    color: #17a2b8;
}

.message i.warning {
    color: #ffc107;
}

@keyframes messageSlideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes messageSlideOut {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-20px);
        opacity: 0;
    }
}

/* 系统消息提示框样式 */
.system-message-box {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 400px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    overflow: hidden;
    animation: systemMessageSlideIn 0.3s ease;
}

.system-message-title {
    background: #007bff;
    color: #fff;
    padding: 12px 15px;
    font-weight: 500;
    font-size: 16px;
    border-radius: 8px 8px 0 0;
}

.system-message-content {
    padding: 20px 15px;
    text-align: center;
}

.system-message-content p {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.system-message-footer {
    padding: 10px 15px 15px;
    text-align: center;
}

.system-message-btn {
    min-width: 120px;
    padding: 8px 20px;
    background: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.system-message-btn:hover {
    background: #0069d9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.25);
}

/* 系统消息动画 */
@keyframes systemMessageSlideIn {
    from {
        transform: translate(-50%, -70%);
        opacity: 0;
    }
    to {
        transform: translate(-50%, -50%);
        opacity: 1;
    }
}

.system-message-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9998;
    animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* 不同类型消息的样式 */
.system-message-box.success .system-message-title {
    background: #28a745;
}
.system-message-box.success .system-message-btn {
    background: #28a745;
}
.system-message-box.success .system-message-btn:hover {
    background: #218838;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.25);
}

.system-message-box.error .system-message-title {
    background: #dc3545;
}
.system-message-box.error .system-message-btn {
    background: #dc3545;
}
.system-message-box.error .system-message-btn:hover {
    background: #c82333;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.25);
}

.system-message-box.warning .system-message-title {
    background: #ffc107;
    color: #212529;
}
.system-message-box.warning .system-message-btn {
    background: #ffc107;
    color: #212529;
}
.system-message-box.warning .system-message-btn:hover {
    background: #e0a800;
    box-shadow: 0 4px 8px rgba(255, 193, 7, 0.25);
}
</style>

<script>
/**
 * 显示确认对话框
 * @param {Object} options - 对话框选项
 * @param {string} options.title - 对话框标题
 * @param {string} options.message - 对话框内容
 * @param {string} options.type - 对话框类型：'danger', 'warning', 'success', 'info'
 * @param {string} options.confirmText - 确认按钮文本
 * @param {string} options.confirmIcon - 确认按钮图标类名
 * @param {Function} options.onConfirm - 确认回调函数
 * @returns {bootstrap.Modal} 对话框实例
 */
function showConfirm(options) {
    const defaultOptions = {
        title: '确认操作',
        message: '您确定要执行此操作吗？',
        type: 'danger',
        confirmText: '确认',
        confirmIcon: 'fa-check',
        onConfirm: null
    };
    
    const settings = {...defaultOptions, ...options};
    
    // 设置图标和标题
    const iconElement = document.getElementById('confirmIcon');
    const confirmIcon = document.querySelector('.confirm-icon');
    
    if (settings.type === 'danger') {
        iconElement.className = 'fas fa-trash-alt';
        confirmIcon.style.borderColor = '#dc3545';
        iconElement.style.color = '#dc3545';
    } else if (settings.type === 'warning') {
        iconElement.className = 'fas fa-exclamation-triangle';
        confirmIcon.style.borderColor = '#ffc107';
        iconElement.style.color = '#ffc107';
    } else if (settings.type === 'success') {
        iconElement.className = 'fas fa-check-circle';
        confirmIcon.style.borderColor = '#28a745';
        iconElement.style.color = '#28a745';
    } else if (settings.type === 'info') {
        iconElement.className = 'fas fa-info-circle';
        confirmIcon.style.borderColor = '#17a2b8';
        iconElement.style.color = '#17a2b8';
    }
    
    // 设置标题和消息
    document.getElementById('confirmTitle').textContent = settings.title;
    document.getElementById('confirmMessage').innerHTML = settings.message;
    
    // 设置确认按钮
    const confirmButton = document.getElementById('confirmButton');
    confirmButton.innerHTML = `<i class="fas ${settings.confirmIcon}"></i> ${settings.confirmText}`;
    confirmButton.className = `btn-confirm ${settings.type}`;
    
    // 绑定确认事件
    confirmButton.onclick = function() {
        if (typeof settings.onConfirm === 'function') {
            settings.onConfirm();
        }
    };
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
    
    return modal;
}

/**
 * 显示消息提示
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型：'success', 'error', 'info', 'warning'
 * @param {number} duration - 显示时长(毫秒)
 */
function showMessage(message, type = 'success', duration = 3000) {
    const container = document.getElementById('messageContainer');
    const messageBox = document.getElementById('messageBox');
    const messageIcon = document.getElementById('messageIcon');
    const messageText = document.getElementById('messageText');
    
    // 设置消息类型
    messageBox.className = `message ${type}`;
    
    // 设置图标
    if (type === 'success') {
        messageIcon.className = 'fas fa-check-circle success';
    } else if (type === 'error') {
        messageIcon.className = 'fas fa-times-circle error';
    } else if (type === 'info') {
        messageIcon.className = 'fas fa-info-circle info';
    } else if (type === 'warning') {
        messageIcon.className = 'fas fa-exclamation-circle warning';
    }
    
    // 设置消息文本
    messageText.textContent = message;
    
    // 显示消息
    container.style.display = 'block';
    
    // 设置自动消失
    setTimeout(() => {
        messageBox.style.animation = 'messageSlideOut 0.3s ease-in forwards';
        setTimeout(() => {
            container.style.display = 'none';
            messageBox.style.animation = '';
        }, 300);
    }, duration);
}

/**
 * 显示系统消息提示框
 * @param {string} message - 消息内容
 * @param {string} title - 标题，默认为"系统消息"
 * @param {string} type - 消息类型：'info', 'success', 'error', 'warning'
 * @param {Function} callback - 点击确定按钮后的回调函数
 */
function showSystemMessage(message, title = '系统消息', type = 'info', callback = null) {
    // 获取DOM元素
    const messageBox = document.getElementById('systemMessageBox');
    const messageTitle = document.getElementById('systemMessageTitle');
    const messageText = document.getElementById('systemMessageText');
    const messageBtn = document.getElementById('systemMessageBtn');
    
    // 清除之前的类
    messageBox.className = 'system-message-box';
    // 添加类型类
    messageBox.classList.add(type);
    
    // 设置内容
    messageTitle.textContent = title;
    messageText.textContent = message;
    
    // 创建背景遮罩
    const backdrop = document.createElement('div');
    backdrop.className = 'system-message-backdrop';
    document.body.appendChild(backdrop);
    
    // 显示消息框
    messageBox.style.display = 'block';
    
    // 绑定确定按钮事件
    messageBtn.onclick = function() {
        // 关闭消息框
        messageBox.style.display = 'none';
        // 移除背景遮罩
        if (backdrop.parentNode) {
            backdrop.parentNode.removeChild(backdrop);
        }
        // 执行回调
        if (typeof callback === 'function') {
            callback();
        }
    };
    
    // 点击背景遮罩也可以关闭
    backdrop.onclick = function() {
        messageBtn.click();
    };
}

// 将函数暴露给全局环境
window.showConfirm = showConfirm;
window.showMessage = showMessage;
window.showSystemMessage = showSystemMessage;
</script> 