from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
import random
from datetime import datetime, timedelta
from django.apps import apps

User = get_user_model()


class Command(BaseCommand):
    help = '创建测试用户数据'

    def handle(self, *args, **options):
        self.stdout.write('🚀 开始创建测试用户...')
        
        # 用户数据
        users_data = [
            {
                'real_name': '王雨晴',
                'username': 'RainbowWang',
                'email': '<EMAIL>',
                'phone': '13812345678',
                'address': '上海市浦东新区世纪大道 100 号',
                'gender': 'F',
                'birth_date': '1995-03-15'
            },
            {
                'real_name': '陈俊杰',
                'username': '<PERSON><PERSON><PERSON>',
                'email': '<EMAIL>',
                'phone': '13987654321',
                'address': '北京市朝阳区望京街道望京西园三区',
                'gender': 'M',
                'birth_date': '1992-07-22'
            },
            {
                'real_name': '李诗涵',
                'username': 'PoetryLi',
                'email': '<EMAIL>',
                'phone': '15823456789',
                'address': '广州市天河区珠江新城花城大道',
                'gender': 'F',
                'birth_date': '1999-11-08'
            },
            {
                'real_name': '张浩然',
                'username': 'BraveZhang',
                'email': '<EMAIL>',
                'phone': '15967890123',
                'address': '深圳市南山区高新园科技南路',
                'gender': 'M',
                'birth_date': '1990-05-12'
            },
            {
                'real_name': '刘梦婷',
                'username': 'DreamLiu',
                'email': '<EMAIL>',
                'phone': '13654321098',
                'address': '杭州市西湖区文二路 200 号',
                'gender': 'F',
                'birth_date': '1997-09-25'
            },
            {
                'real_name': '赵宇轩',
                'username': 'SkyZhao',
                'email': '<EMAIL>',
                'phone': '13798765432',
                'address': '成都市锦江区春熙路街道红星路三段',
                'gender': 'M',
                'birth_date': '1993-12-03'
            },
            {
                'real_name': '吴思琪',
                'username': 'SparkleWu',
                'email': '<EMAIL>',
                'phone': '15012345678',
                'address': '南京市鼓楼区中山路 101 号',
                'gender': 'F',
                'birth_date': '2000-04-18'
            },
            {
                'real_name': '周嘉豪',
                'username': 'HeroZhou',
                'email': '<EMAIL>',
                'phone': '13478901234',
                'address': '武汉市江汉区解放大道 686 号',
                'gender': 'M',
                'birth_date': '1991-08-14'
            },
            {
                'real_name': '郑雅文',
                'username': 'ElegantZheng',
                'email': '<EMAIL>',
                'phone': '15678901234',
                'address': '重庆市渝中区解放碑街道民权路',
                'gender': 'F',
                'birth_date': '1996-02-29'
            },
            {
                'real_name': '孙泽宇',
                'username': 'SunnySun',
                'email': '<EMAIL>',
                'phone': '13345678901',
                'address': '西安市碑林区东大街 350 号',
                'gender': 'M',
                'birth_date': '1994-10-07'
            },
            {
                'real_name': '林晓薇',
                'username': 'DawnLin',
                'email': '<EMAIL>',
                'phone': '15123456789',
                'address': '福州市鼓楼区五四路 158 号',
                'gender': 'F',
                'birth_date': '1998-06-21'
            },
            {
                'real_name': '何俊辉',
                'username': 'ShineHe',
                'email': '<EMAIL>',
                'phone': '13256789012',
                'address': '长沙市芙蓉区五一大道 882 号',
                'gender': 'M',
                'birth_date': '1989-01-16'
            },
            {
                'real_name': '郭雨桐',
                'username': 'RainGuo',
                'email': '<EMAIL>',
                'phone': '15790123456',
                'address': '天津市和平区南京路 189 号',
                'gender': 'F',
                'birth_date': '2001-12-11'
            },
            {
                'real_name': '马晓峰',
                'username': 'PeakMa',
                'email': '<EMAIL>',
                'phone': '13012345678',
                'address': '合肥市蜀山区长江西路 200 号',
                'gender': 'M',
                'birth_date': '1987-04-30'
            },
            {
                'real_name': '徐若琳',
                'username': 'LilyXu',
                'email': '<EMAIL>',
                'phone': '15345678901',
                'address': '苏州市工业园区金鸡湖大道 100 号',
                'gender': 'F',
                'birth_date': '2002-08-05'
            }
        ]
        
        # 头像文件列表
        avatar_files = [
            'avatars/头像图片生成.png',
            'avatars/头像图片生成 (1).png',
            'avatars/头像图片生成 (2).png',
            'avatars/头像图片生成 (3).png',
            'avatars/头像图片生成 (4).png',
            'avatars/头像图片生成 (5).png',
            'avatars/头像图片生成 (6).png',
            'avatars/头像图片生成 (7).png',
            'avatars/头像图片生成 (8).png',
            'avatars/头像图片生成 (9).png',
            'avatars/默认头像.png'
        ]
        
        # 职业列表
        occupations = [
            '软件工程师', '产品经理', '设计师', '市场专员', '销售经理',
            '教师', '医生', '律师', '会计师', '建筑师',
            '记者', '摄影师', '翻译', '咨询师', '创业者'
        ]
        
        # 兴趣爱好列表
        hobbies = [
            '阅读', '旅行', '摄影', '音乐', '电影', '运动', '烹饪', '绘画',
            '写作', '游戏', '瑜伽', '跑步', '游泳', '登山', '钓鱼'
        ]
        
        try:
            with transaction.atomic():
                created_count = 0
                
                for user_data in users_data:
                    # 检查用户是否已存在
                    if User.objects.filter(username=user_data['username']).exists():
                        self.stdout.write(f'⚠️  用户 {user_data["username"]} 已存在，跳过创建')
                        continue
                    
                    # 随机选择头像
                    avatar_file = random.choice(avatar_files)
                    
                    # 随机选择职业和兴趣爱好
                    occupation = random.choice(occupations)
                    user_hobbies = random.sample(hobbies, random.randint(2, 5))
                    
                    # 创建用户基本信息
                    user_fields = {
                        'username': user_data['username'],
                        'email': user_data['email'],
                        'password': '123456',  # 默认密码
                        'phone': user_data.get('phone', ''),
                        'avatar': avatar_file,
                        'is_active': True,
                        'is_staff': False,
                        'is_superuser': False
                    }
                    
                    # 如果User模型有这些字段，添加额外信息
                    user_model_fields = [field.name for field in User._meta.get_fields()]
                    
                    if 'first_name' in user_model_fields:
                        user_fields['first_name'] = user_data['real_name']
                    
                    if 'gender' in user_model_fields:
                        user_fields['gender'] = user_data.get('gender', 'M')
                    
                    if 'birth_date' in user_model_fields:
                        try:
                            birth_date = datetime.strptime(user_data['birth_date'], '%Y-%m-%d').date()
                            user_fields['birth_date'] = birth_date
                        except:
                            pass
                    
                    if 'occupation' in user_model_fields:
                        user_fields['occupation'] = occupation
                    
                    if 'bio' in user_model_fields:
                        user_fields['bio'] = f"我是{user_data['real_name']}，职业是{occupation}，喜欢{', '.join(user_hobbies[:3])}等。"
                    
                    # 创建用户
                    user = User.objects.create_user(**user_fields)

                    # 创建用户地址（如果有Address模型）
                    try:
                        from users.models import Address
                        # 解析地址信息
                        address_text = user_data.get('address', '')
                        if address_text:
                            # 简单解析地址（可以根据需要改进）
                            if '市' in address_text:
                                parts = address_text.split('市')
                                if len(parts) >= 2:
                                    city_part = parts[0] + '市'
                                    detail_part = parts[1]

                                    # 尝试提取省份
                                    province = ''
                                    if '省' in city_part:
                                        province = city_part.split('省')[0] + '省'
                                        city = city_part.split('省')[1] if '省' in city_part else city_part
                                    else:
                                        # 直辖市情况
                                        if any(x in city_part for x in ['北京', '上海', '天津', '重庆']):
                                            province = city_part
                                            city = city_part
                                        else:
                                            province = city_part
                                            city = city_part

                                    # 提取区县
                                    district = ''
                                    if '区' in detail_part:
                                        district = detail_part.split('区')[0] + '区'
                                        detail_address = detail_part.split('区')[1] if '区' in detail_part else detail_part
                                    elif '县' in detail_part:
                                        district = detail_part.split('县')[0] + '县'
                                        detail_address = detail_part.split('县')[1] if '县' in detail_part else detail_part
                                    else:
                                        district = '未知区'
                                        detail_address = detail_part

                                    Address.objects.create(
                                        user=user,
                                        receiver=user_data['real_name'],
                                        phone=user_data.get('phone', ''),
                                        province=province,
                                        city=city,
                                        district=district,
                                        address=detail_address.strip(),
                                        is_default=True
                                    )
                    except ImportError:
                        # 如果没有Address模型，跳过
                        pass
                    except Exception as e:
                        self.stdout.write(f'⚠️  创建地址失败: {e}')

                    created_count += 1

                    # 显示创建信息
                    self.stdout.write(
                        f'✅ 创建用户: {user.username} ({user_data["real_name"]}) '
                        f'- 头像: {avatar_file.split("/")[-1]} '
                        f'- 职业: {occupation}'
                    )
                
                self.stdout.write(self.style.SUCCESS(f'\n🎉 成功创建了 {created_count} 个新用户！'))
                self.stdout.write(self.style.SUCCESS('📝 所有用户的默认密码为: 123456'))
                
                # 显示用户统计
                total_users = User.objects.count()
                regular_users = User.objects.filter(is_staff=False, is_superuser=False).count()
                admin_users = User.objects.filter(is_staff=True).count()
                
                self.stdout.write(f'\n📊 用户统计:')
                self.stdout.write(f'  - 总用户数: {total_users}')
                self.stdout.write(f'  - 普通用户: {regular_users}')
                self.stdout.write(f'  - 管理员: {admin_users}')
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ 创建用户时出错: {e}'))
            
        self.stdout.write('\n🏁 用户创建完成！')
