/* 全局样式 */
body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

.container {
    max-width: 1200px;
    margin: 30px auto;
    padding: 0 15px;
}

h1 {
    text-align: center;
    color: #333;
    font-size: 28px;
    margin-bottom: 30px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

/* 通用样式 */
.clearfix::after {
    content: "";
    display: block;
    clear: both;
}

.leftfix {
    float: left;
}

.rightfix {
    float: right;
}

/* 面包屑导航 */
.breadcrumb {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #666;
    margin-top: 10px;
}

.breadcrumb a {
    color: #666;
}

.breadcrumb a:hover {
    color: #DD302D;
}

.breadcrumb span {
    margin: 0 5px;
}

/* 购物车样式 */
.cart-container {
    margin-top: 20px;
    border: 1px solid #e6e6e6;
    background-color: #fff;
}

.cart-header {
    height: 50px;
    line-height: 50px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
}

.cart-col {
    float: left;
    text-align: center;
}

.col-check {
    width: 10%;
    text-align: center;
}

.col-name {
    width: 40%;
    text-align: left;
    padding-left: 10px;
}

.col-price, .col-num, .col-total {
    width: 13%;
}

.col-action {
    width: 11%;
}

.cart-item {
    padding: 20px 0;
    border-bottom: 1px solid #e6e6e6;
}

.item-image {
    float: left;
    width: 80px;
    height: 80px;
    margin-right: 10px;
}

.item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.item-info {
    float: left;
    width: calc(100% - 90px);
}

.item-title {
    margin-bottom: 10px;
}

.item-title a {
    color: #333;
    font-size: 14px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.item-title a:hover {
    color: #DD302D;
}

.item-sku {
    color: #999;
    font-size: 12px;
}

.num-input {
    display: inline-block;
    border: 1px solid #e6e6e6;
    width: 120px;
    height: 30px;
    line-height: 30px;
}

.num-input button {
    width: 30px;
    height: 28px;
    background: #f5f5f5;
    border: none;
    float: left;
    cursor: pointer;
}

.num-input input {
    width: 60px;
    height: 28px;
    text-align: center;
    border: none;
    border-left: 1px solid #e6e6e6;
    border-right: 1px solid #e6e6e6;
    float: left;
}

.delete {
    color: #999;
}

.delete:hover {
    color: #DD302D;
}

.cart-footer {
    padding: 20px;
    background-color: #f5f5f5;
}

.cart-left {
    float: left;
}

.cart-right {
    float: right;
}

.delete-selected, .clear-cart {
    margin-left: 15px;
    color: #666;
}

.delete-selected:hover, .clear-cart:hover {
    color: #DD302D;
}

.selected-count {
    display: inline-block;
    margin-right: 20px;
}

.total-price {
    display: inline-block;
    margin-right: 20px;
    font-size: 16px;
    color: #DD302D;
    font-weight: bold;
}

.btn-checkout {
    display: inline-block;
    width: 120px;
    height: 40px;
    line-height: 40px;
    background-color: #DD302D;
    color: #fff;
    text-align: center;
    border: none;
    font-size: 16px;
    cursor: pointer;
}

.btn-checkout:hover {
    background-color: #c01e1a;
}

.btn-checkout.active {
    background-color: #DD302D;
}

.empty-cart {
    padding: 50px 0;
    text-align: center;
}

.empty-cart-icon {
    font-size: 60px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-cart p {
    color: #999;
    margin-bottom: 20px;
}

.empty-cart .btn {
    display: inline-block;
    padding: 8px 20px;
    background-color: #DD302D;
    color: #fff;
    border-radius: 4px;
}

.recommend-section {
    margin-top: 30px;
}

.section-title {
    height: 40px;
    line-height: 40px;
    border-bottom: 2px solid #DD302D;
}

.section-title h2 {
    font-size: 18px;
    color: #DD302D;
}

.recommend-products {
    margin-top: 20px;
}

.product-item {
    float: left;
    width: 230px;
    margin-right: 10px;
    margin-bottom: 20px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    transition: all 0.3s;
}

.product-item:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.product-item:nth-child(5n) {
    margin-right: 0;
}

.product-img {
    width: 100%;
    height: 230px;
}

.product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-info {
    padding: 10px;
}

.product-title {
    height: 40px;
    margin-bottom: 10px;
}

.product-title a {
    color: #333;
    font-size: 14px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.product-title a:hover {
    color: #DD302D;
}

.product-price {
    color: #DD302D;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
}

.btn-add-cart {
    display: block;
    width: 100%;
    height: 30px;
    line-height: 30px;
    background-color: #DD302D;
    color: #fff;
    text-align: center;
    border-radius: 4px;
}

/* 订单列表页样式 */
.user-center {
    margin-top: 20px;
}

.user-sidebar {
    float: left;
    width: 200px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
}

.user-info {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #e6e6e6;
}

.avatar {
    width: 80px;
    height: 80px;
    margin: 0 auto 10px;
}

.avatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.username {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.user-level {
    color: #DD302D;
    font-size: 12px;
}

.menu-list {
    padding: 10px 0;
}

.menu-item {
    height: 40px;
    line-height: 40px;
}

.menu-item a {
    display: block;
    padding-left: 20px;
    color: #333;
    font-size: 14px;
}

.menu-item a:hover, .menu-item.active a {
    color: #DD302D;
    background-color: #f5f5f5;
}

.menu-item i {
    margin-right: 5px;
}

.user-content {
    float: right;
    width: 980px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
}

.content-header {
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    border-bottom: 1px solid #e6e6e6;
}

.content-header h3 {
    font-size: 16px;
    color: #333;
}

.order-filter {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
}

.order-filter a {
    display: inline-block;
    padding: 0 15px;
    color: #333;
    font-size: 14px;
}

.order-filter a:hover, .order-filter a.active {
    color: #DD302D;
}

.content-body {
    padding: 20px;
}

.order-box {
    margin-bottom: 20px;
    border: 1px solid #e6e6e6;
}

.order-header {
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
}

.order-id {
    margin-right: 20px;
}

.order-status span {
    color: #DD302D;
}

.status-pending {
    color: #FF9900 !important;
}

.status-paid {
    color: #3399FF !important;
}

.status-shipped {
    color: #9933FF !important;
}

.status-completed {
    color: #339933 !important;
}

.status-cancelled {
    color: #999999 !important;
}

.status-refunded {
    color: #FF3366 !important;
}

.order-goods {
    padding: 15px;
}

.order-goods-item {
    padding: 10px 0;
    border-bottom: 1px dashed #e6e6e6;
}

.order-goods-item:last-child {
    border-bottom: none;
}

.goods-img {
    float: left;
    width: 80px;
    height: 80px;
    margin-right: 10px;
}

.goods-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.goods-info {
    float: left;
    width: 300px;
}

.goods-name {
    margin-bottom: 5px;
}

.goods-name a {
    color: #333;
    font-size: 14px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.goods-name a:hover {
    color: #DD302D;
}

.goods-sku {
    color: #999;
    font-size: 12px;
    margin-bottom: 5px;
}

.goods-price {
    color: #666;
    font-size: 12px;
}

.goods-num {
    line-height: 80px;
}

.order-footer {
    height: 60px;
    line-height: 60px;
    padding: 0 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #e6e6e6;
}

.order-total {
    float: left;
    font-size: 14px;
}

.order-total .total-price {
    font-size: 16px;
    color: #DD302D;
    font-weight: bold;
}

.order-actions {
    float: right;
}

.btn-detail, .btn-pay, .btn-cancel, .btn-confirm, .btn-logistics, .btn-review, .btn-buy-again {
    display: inline-block;
    padding: 5px 15px;
    margin-left: 10px;
    border: 1px solid #ddd;
    color: #333;
    border-radius: 4px;
    background-color: #fff;
}

.btn-pay {
    background-color: #DD302D;
    color: #fff;
    border-color: #DD302D;
}

.btn-detail:hover, .btn-cancel:hover, .btn-confirm:hover, .btn-logistics:hover, .btn-review:hover, .btn-buy-again:hover {
    color: #DD302D;
    border-color: #DD302D;
}

.btn-pay:hover {
    background-color: #c01e1a;
}

.pagination {
    margin-top: 20px;
    text-align: center;
}

.pagination ul {
    display: inline-block;
}

.pagination li {
    float: left;
    margin: 0 5px;
}

.pagination li a {
    display: block;
    padding: 5px 10px;
    border: 1px solid #ddd;
    color: #333;
}

.pagination li.active a {
    background-color: #DD302D;
    color: #fff;
    border-color: #DD302D;
}

.pagination li a:hover {
    color: #DD302D;
    border-color: #DD302D;
}

.empty-order {
    padding: 50px 0;
    text-align: center;
}

.empty-order-icon {
    font-size: 60px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-order p {
    color: #999;
    margin-bottom: 20px;
}

.empty-order .btn {
    display: inline-block;
    padding: 8px 20px;
    background-color: #DD302D;
    color: #fff;
    border-radius: 4px;
}

/* 订单详情页样式 */
.order-detail-container {
    margin-top: 20px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
}

.detail-header {
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    border-bottom: 1px solid #e6e6e6;
}

.detail-header h3 {
    font-size: 16px;
    color: #333;
}

.order-status-section {
    padding: 20px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #e6e6e6;
}

.status-info {
    margin-bottom: 30px;
}

.status-icon {
    float: left;
    width: 60px;
    height: 60px;
    line-height: 60px;
    text-align: center;
    background-color: #DD302D;
    color: #fff;
    border-radius: 50%;
    font-size: 30px;
    margin-right: 20px;
}

.status-text {
    float: left;
}

.status-text h4 {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
}

.status-text p {
    color: #666;
}

.order-progress {
    clear: both;
    padding-top: 20px;
}

.progress-steps {
    position: relative;
    padding-top: 30px;
}

.progress-steps:before {
    content: "";
    position: absolute;
    top: 40px;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: #e6e6e6;
}

.step {
    position: relative;
    float: left;
    width: 25%;
    text-align: center;
}

.step-icon {
    position: relative;
    width: 20px;
    height: 20px;
    line-height: 20px;
    background-color: #999;
    color: #fff;
    border-radius: 50%;
    margin: 0 auto;
    z-index: 1;
}

.step.active .step-icon {
    background-color: #DD302D;
}

.step-info {
    margin-top: 10px;
}

.step-name {
    font-size: 14px;
    color: #333;
    margin-bottom: 5px;
}

.step-time {
    font-size: 12px;
    color: #999;
}

.step-line {
    position: absolute;
    top: 10px;
    left: 50%;
    width: 100%;
    height: 2px;
    background-color: #e6e6e6;
}

.step.active .step-line {
    background-color: #DD302D;
}

.detail-section {
    padding: 20px;
    border-bottom: 1px solid #e6e6e6;
}

.section-header {
    margin-bottom: 15px;
}

.section-header h4 {
    font-size: 16px;
    color: #333;
}

.info-item {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.item-label {
    display: inline-block;
    width: 100px;
    color: #999;
}

.goods-header {
    height: 40px;
    line-height: 40px;
    background-color: #f5f5f5;
    border: 1px solid #e6e6e6;
    margin-bottom: 10px;
}

.col-goods {
    float: left;
    width: 50%;
    padding-left: 10px;
}

.col-price, .col-num, .col-total {
    float: left;
    width: 16.6%;
    text-align: center;
}

.goods-item {
    padding: 10px 0;
    border-bottom: 1px dashed #e6e6e6;
}

.goods-item:last-child {
    border-bottom: none;
}

.payment-section {
    background-color: #f9f9f9;
}

.payment-item {
    margin-bottom: 10px;
    text-align: right;
}

.payment-label {
    display: inline-block;
    width: 100px;
    text-align: right;
    color: #999;
}

.payment-value {
    display: inline-block;
    width: 100px;
    text-align: right;
    color: #666;
}

.payment-total {
    font-size: 16px;
    font-weight: bold;
}

.payment-total .payment-value {
    color: #DD302D;
}

.order-actions {
    text-align: center;
    padding: 20px 0;
}

.btn-back {
    display: inline-block;
    padding: 5px 15px;
    margin-left: 10px;
    border: 1px solid #ddd;
    color: #333;
    border-radius: 4px;
    background-color: #fff;
}

.btn-back:hover {
    color: #DD302D;
    border-color: #DD302D;
}

/* 购物车优惠区域样式 */
.cart-promotion {
    margin-top: 20px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
}

.promotion-header {
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
}

.promotion-header h3 {
    font-size: 16px;
    color: #333;
}

.promotion-body {
    padding: 20px;
}

.coupon-section, .delivery-section, .remark-section {
    margin-bottom: 20px;
}

.section-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.coupon-list select {
    width: 300px;
    height: 36px;
    padding: 0 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
}

.no-coupon {
    color: #999;
}

.delivery-options {
    margin-top: 10px;
}

.delivery-option {
    display: block;
    margin-bottom: 10px;
    cursor: pointer;
}

.delivery-option input {
    margin-right: 5px;
    vertical-align: middle;
}

.option-text {
    display: inline-block;
    width: 100px;
}

.delivery-fee {
    color: #DD302D;
}

.remark-input textarea {
    width: 100%;
    height: 80px;
    padding: 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    resize: none;
}

/* 订单列表页增强样式 */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-search {
    display: flex;
    align-items: center;
}

.order-search input {
    width: 250px;
    height: 36px;
    padding: 0 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px 0 0 4px;
}

.order-search button {
    height: 36px;
    padding: 0 15px;
    background-color: #DD302D;
    color: #fff;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.order-stats {
    display: flex;
    justify-content: space-between;
    margin: 20px 0;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    padding: 15px 0;
}

.stats-item {
    flex: 1;
    text-align: center;
    border-right: 1px solid #e6e6e6;
}

.stats-item:last-child {
    border-right: none;
}

.stats-number {
    font-size: 24px;
    color: #DD302D;
    font-weight: bold;
    margin-bottom: 5px;
}

.stats-label {
    font-size: 14px;
    color: #666;
}

.batch-actions {
    display: flex;
    align-items: center;
    height: 40px;
    margin-bottom: 10px;
    padding: 0 10px;
    background-color: #f5f5f5;
    border: 1px solid #e6e6e6;
}

.select-all {
    margin-right: 20px;
}

.select-all input {
    margin-right: 5px;
    vertical-align: middle;
}

.action-buttons button {
    margin-right: 10px;
    padding: 5px 15px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.action-buttons button:disabled {
    background-color: #f5f5f5;
    color: #999;
    cursor: not-allowed;
}

.action-buttons button:not(:disabled):hover {
    color: #DD302D;
    border-color: #DD302D;
}

/* 消息提示样式 */
.message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 10px 20px;
    border-radius: 4px;
    z-index: 9999;
    transition: opacity 0.5s;
}

.message.success {
    background-color: #f0f9eb;
    color: #67c23a;
    border: 1px solid #c2e7b0;
}

.message.error {
    background-color: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
}

.message.fade-out {
    opacity: 0;
}

/* 商品列表页样式 */
.goods-container {
    margin-top: 20px;
}

.category-sidebar {
    float: left;
    width: 200px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
}

.sidebar-title {
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #e6e6e6;
}

.sidebar-title h3 {
    font-size: 16px;
    color: #333;
}

.category-list {
    padding: 10px 0;
}

.category-list li {
    height: 36px;
    line-height: 36px;
    padding: 0 15px;
}

.category-list li.active a {
    color: #DD302D;
}

.category-list li a {
    display: block;
    color: #333;
    font-size: 14px;
}

.category-list li a:hover {
    color: #DD302D;
}

.hot-goods {
    margin-top: 20px;
}

.hot-goods-list {
    padding: 10px 15px;
}

.hot-goods-item {
    padding: 10px 0;
    border-bottom: 1px dashed #e6e6e6;
}

.hot-goods-item:last-child {
    border-bottom: none;
}

.hot-goods-img {
    float: left;
    width: 60px;
    height: 60px;
}

.hot-goods-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hot-goods-info {
    float: left;
    width: calc(100% - 70px);
    margin-left: 10px;
}

.hot-goods-name {
    height: 40px;
    margin-bottom: 5px;
}

.hot-goods-name a {
    color: #333;
    font-size: 12px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.hot-goods-name a:hover {
    color: #DD302D;
}

.hot-goods-price {
    color: #DD302D;
    font-size: 14px;
    font-weight: bold;
}

.goods-content {
    float: right;
    width: 980px;
}

.filter-box {
    padding: 15px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    margin-bottom: 20px;
}

.search-box {
    margin-bottom: 15px;
}

.search-box form {
    display: flex;
}

.search-box input {
    width: 300px;
    height: 36px;
    padding: 0 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px 0 0 4px;
}

.search-box button {
    height: 36px;
    padding: 0 15px;
    background-color: #DD302D;
    color: #fff;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
}

.filter-options {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.filter-title {
    margin-right: 10px;
    color: #666;
}

.filter-items a {
    display: inline-block;
    margin-right: 15px;
    color: #333;
}

.filter-items a.active, .filter-items a:hover {
    color: #DD302D;
}

.price-filter {
    display: flex;
    align-items: center;
}

.price-filter span {
    margin-right: 10px;
    color: #666;
}

.price-filter input {
    width: 80px;
    height: 30px;
    padding: 0 10px;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    text-align: center;
}

.price-filter button {
    height: 30px;
    padding: 0 15px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-left: 10px;
    cursor: pointer;
}

.price-filter button:hover {
    color: #DD302D;
    border-color: #DD302D;
}

.goods-list {
    margin-right: -20px;
}

.goods-item {
    float: left;
    width: 230px;
    margin: 0 20px 20px 0;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    transition: all 0.3s;
}

.goods-item:hover {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.goods-img {
    width: 100%;
    height: 230px;
}

.goods-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.goods-info {
    padding: 10px;
}

.goods-name {
    height: 40px;
    margin-bottom: 10px;
}

.goods-name a {
    color: #333;
    font-size: 14px;
    line-height: 20px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
}

.goods-name a:hover {
    color: #DD302D;
}

.goods-price {
    color: #DD302D;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 5px;
}

.goods-sales {
    color: #999;
    font-size: 12px;
    margin-bottom: 10px;
}

.goods-actions {
    display: flex;
    justify-content: space-between;
}

.btn-add-cart, .btn-buy-now {
    display: block;
    width: 48%;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 4px;
    font-size: 12px;
}

.btn-add-cart {
    background-color: #fff;
    color: #DD302D;
    border: 1px solid #DD302D;
}

.btn-buy-now {
    background-color: #DD302D;
    color: #fff;
    border: 1px solid #DD302D;
}

.btn-add-cart:hover {
    background-color: #fef0f0;
}

.btn-buy-now:hover {
    background-color: #c01e1a;
}

.empty-goods {
    padding: 50px 0;
    text-align: center;
}

.empty-goods-icon {
    font-size: 60px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-goods p {
    color: #999;
    margin-bottom: 20px;
}

.order-checkbox-wrapper {
    margin-right: 10px;
    display: flex;
    align-items: center;
}

.order-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.advanced-filter {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed #e6e6e6;
}

.filter-row {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
}

.filter-label {
    width: 80px;
    color: #666;
}

.filter-values {
    flex: 1;
}

.filter-values a {
    display: inline-block;
    margin-right: 15px;
    color: #333;
    padding: 2px 8px;
    border-radius: 4px;
}

.filter-values a.active {
    background-color: #DD302D;
    color: #fff;
}

.filter-values a:hover:not(.active) {
    color: #DD302D;
}

/* 商品快速预览样式 */
.quick-preview-btn {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.quick-preview-btn:hover {
    background-color: rgba(221, 48, 45, 0.8);
}

.preview-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-content {
    position: relative;
    width: 800px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
    padding: 20px;
}

.preview-close {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    font-size: 24px;
    color: #999;
    cursor: pointer;
}

.preview-close:hover {
    color: #DD302D;
}

.preview-header {
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e6e6e6;
}

.preview-header h3 {
    font-size: 18px;
    color: #333;
}

.preview-body {
    display: flex;
}

.preview-image {
    width: 350px;
    height: 350px;
    margin-right: 20px;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-info {
    flex: 1;
}

.preview-price {
    font-size: 24px;
    color: #DD302D;
    font-weight: bold;
    margin-bottom: 10px;
}

.preview-sales {
    color: #999;
    margin-bottom: 15px;
}

.preview-desc {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    max-height: 150px;
    overflow-y: auto;
}

.preview-actions {
    margin-top: 20px;
}

.preview-actions .num-input {
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 15px;
}

.btn-add-to-cart, .btn-view-detail {
    display: inline-block;
    padding: 8px 20px;
    border-radius: 4px;
    margin-right: 10px;
}

.btn-add-to-cart {
    background-color: #DD302D;
    color: #fff;
    border: none;
    cursor: pointer;
}

.btn-view-detail {
    border: 1px solid #ddd;
    color: #333;
}

.btn-add-to-cart:hover {
    background-color: #c01e1a;
}

.btn-view-detail:hover {
    color: #DD302D;
    border-color: #DD302D;
}

.preview-loading {
    text-align: center;
    padding: 40px 0;
    color: #666;
}

.preview-loading i {
    margin-right: 5px;
}

.preview-error {
    text-align: center;
    padding: 40px 0;
    color: #DD302D;
}

.preview-error i {
    font-size: 40px;
    margin-bottom: 10px;
}

.goods-img {
    position: relative;
    overflow: hidden;
}

/* 订单列表样式 */
.order-list {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.order-item {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    overflow: hidden;
    transition: all 0.3s ease;
}

.order-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

/* 订单头部 */
.order-header {
    padding: 20px 30px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-info {
    display: flex;
    gap: 20px;
    font-size: 14px;
}

.order-sn {
    font-weight: 600;
}

/* 订单状态样式 */
.order-status span {
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 14px;
}

.status-pending {
    background: rgba(255, 193, 7, 0.2);
    color: #ffc107;
}

.status-paid {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
}

.status-shipped {
    background: rgba(0, 123, 255, 0.2);
    color: #007bff;
}

.status-completed {
    background: rgba(23, 162, 184, 0.2);
    color: #17a2b4;
}

.status-cancelled {
    background: rgba(108, 117, 125, 0.2);
    color: #6c757d;
}

/* 商品列表样式 */
.order-products {
    padding: 20px 30px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    background: white;
}

.product-item {
    display: grid;
    grid-template-columns: 80px 1fr auto;
    gap: 20px;
    align-items: center;
    padding: 10px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.product-item:hover {
    background: rgba(220,53,69,0.05);
}

.product-image {
    width: 80px;
    height: 80px;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-image:hover img {
    transform: scale(1.1);
}

.product-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.product-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.product-price {
    color: #dc3545;
    font-weight: 700;
    font-size: 16px;
}

/* 订单底部 */
.order-footer {
    padding: 20px 30px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid rgba(0,0,0,0.1);
}

.order-total {
    font-size: 15px;
    color: #666;
}

.total-price {
    color: #dc3545;
    font-weight: 700;
    font-size: 20px;
    margin-left: 5px;
}

.order-actions {
    display: flex;
    gap: 15px;
}

.order-actions a {
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-detail {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.btn-pay {
    background: #dc3545;
    color: white;
}

.btn-cancel {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

.btn-confirm {
    background: #28a745;
    color: white;
}

.btn-review {
    background: #007bff;
    color: white;
}

.order-actions a:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* 空订单样式 */
.empty-order {
    text-align: center;
    padding: 50px 20px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 50px rgba(0,0,0,0.1);
}

.empty-order img {
    width: 200px;
    margin-bottom: 20px;
}

.empty-order p {
    color: #666;
    font-size: 16px;
    margin-bottom: 20px;
}

.go-shopping {
    display: inline-block;
    padding: 12px 30px;
    background: #dc3545;
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.go-shopping:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220,53,69,0.2);
} 