from rest_framework import viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils.translation import gettext_lazy as _
from .models import SystemConfig, SharedCategory
from .permissions import IsFrontendUser, IsBackendUser


class SystemConfigViewSet(viewsets.ModelViewSet):
    """系统配置API视图集"""
    queryset = SystemConfig.objects.all()
    
    def get_permissions(self):
        """
        根据请求方法设置权限:
        - 前台用户只能查看
        - 后台用户可以完全控制
        """
        if self.action in ['list', 'retrieve', 'get_by_key']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsBackendUser]
        return [permission() for permission in permission_classes]
    
    @action(detail=False, methods=['get'], url_path='by-key/(?P<key>[^/.]+)')
    def get_by_key(self, request, key=None):
        """通过配置键获取配置值"""
        try:
            config = SystemConfig.objects.get(key=key, is_active=True)
            return Response({'key': config.key, 'value': config.value})
        except SystemConfig.DoesNotExist:
            return Response({'error': _('配置不存在')}, status=404)


class SharedCategoryViewSet(viewsets.ModelViewSet):
    """共享商品分类API视图集"""
    queryset = SharedCategory.objects.all()
    
    def get_permissions(self):
        """
        根据请求方法设置权限:
        - 前台用户只能查看
        - 后台用户可以完全控制
        """
        if self.action in ['list', 'retrieve', 'get_children']:
            permission_classes = [permissions.IsAuthenticated]
        else:
            permission_classes = [IsBackendUser]
        return [permission() for permission in permission_classes]
    
    @action(detail=True, methods=['get'])
    def get_children(self, request, pk=None):
        """获取子分类"""
        category = self.get_object()
        children = SharedCategory.objects.filter(parent=category, is_active=True)
        data = [{'id': c.id, 'name': c.name, 'code': c.code} for c in children]
        return Response(data) 