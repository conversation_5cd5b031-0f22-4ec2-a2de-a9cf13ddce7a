<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dc3545;
        }
        
        .test-header h1 {
            color: #dc3545;
            margin: 0;
        }
        
        /* 轮播图样式 */
        .banner-box {
            width: 100%;
            height: 450px;
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .banner-list {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .banner-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            transition: opacity 0.8s ease-in-out;
        }

        .banner-item.active {
            opacity: 1;
        }

        .banner-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .banner-dots {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 10;
        }

        .banner-dots span {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .banner-dots span.active {
            background: #dc3545;
            transform: scale(1.2);
        }

        .banner-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.3s ease;
            opacity: 0;
            z-index: 10;
        }

        .banner-box:hover .banner-arrow {
            opacity: 1;
        }

        .banner-arrow:hover {
            background: rgba(220, 53, 69, 0.8);
        }

        .banner-arrow.prev {
            left: 20px;
        }

        .banner-arrow.next {
            right: 20px;
        }
        
        .status-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: bold;
            color: #495057;
        }
        
        .status-value {
            color: #28a745;
            font-family: monospace;
        }
        
        .control-panel {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .control-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .control-btn.primary {
            background: #dc3545;
            color: white;
        }
        
        .control-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .log-panel {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            height: 200px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-images"></i> 轮播图功能测试</h1>
            <p>测试轮播图的自动切换、手动控制和交互功能</p>
        </div>

        <!-- 轮播图 -->
        <div class="banner-box">
            <div class="banner-list">
                <div class="banner-item active">
                    <img src="/media/banners/轮播图 (1).png" alt="夏季数码促销">
                </div>
                <div class="banner-item">
                    <img src="/media/banners/轮播图 (2).png" alt="家电满减活动">
                </div>
                <div class="banner-item">
                    <img src="/media/banners/轮播图 (3).png" alt="新品美妆上市">
                </div>
                <div class="banner-item">
                    <img src="/media/banners/轮播图.png" alt="母婴促销">
                </div>
            </div>
            <!-- 轮播图指示点 -->
            <div class="banner-dots">
                <span class="active"></span>
                <span></span>
                <span></span>
                <span></span>
            </div>
            <!-- 轮播图箭头 -->
            <div class="banner-arrow prev"><i class="fas fa-chevron-left"></i></div>
            <div class="banner-arrow next"><i class="fas fa-chevron-right"></i></div>
        </div>

        <!-- 状态面板 -->
        <div class="status-panel">
            <h3><i class="fas fa-info-circle"></i> 轮播图状态</h3>
            <div class="status-item">
                <span class="status-label">当前图片:</span>
                <span class="status-value" id="current-index">1 / 4</span>
            </div>
            <div class="status-item">
                <span class="status-label">自动轮播:</span>
                <span class="status-value" id="auto-status">运行中</span>
            </div>
            <div class="status-item">
                <span class="status-label">轮播间隔:</span>
                <span class="status-value" id="interval-time">3000ms</span>
            </div>
            <div class="status-item">
                <span class="status-label">总切换次数:</span>
                <span class="status-value" id="switch-count">0</span>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="control-panel">
            <button class="control-btn primary" onclick="startCarousel()">
                <i class="fas fa-play"></i> 开始轮播
            </button>
            <button class="control-btn secondary" onclick="stopCarousel()">
                <i class="fas fa-pause"></i> 暂停轮播
            </button>
            <button class="control-btn primary" onclick="prevImage()">
                <i class="fas fa-step-backward"></i> 上一张
            </button>
            <button class="control-btn primary" onclick="nextImage()">
                <i class="fas fa-step-forward"></i> 下一张
            </button>
            <button class="control-btn secondary" onclick="clearLog()">
                <i class="fas fa-trash"></i> 清空日志
            </button>
        </div>

        <!-- 日志面板 -->
        <div class="log-panel" id="log-panel">
            <div>🎠 轮播图测试控制台</div>
            <div>等待初始化...</div>
        </div>
    </div>

    <script>
        // 全局变量
        let current = 0;
        let timer = null;
        let switchCount = 0;
        const bannerItems = $('.banner-item');
        const dots = $('.banner-dots span');
        
        // 日志函数
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logPanel = document.getElementById('log-panel');
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        // 更新状态显示
        function updateStatus() {
            document.getElementById('current-index').textContent = `${current + 1} / ${bannerItems.length}`;
            document.getElementById('auto-status').textContent = timer ? '运行中' : '已暂停';
            document.getElementById('switch-count').textContent = switchCount;
        }
        
        // 显示指定轮播图
        function showBanner(index) {
            log(`🖼️ 切换到轮播图 ${index + 1}/${bannerItems.length}`);
            bannerItems.removeClass('active');
            bannerItems.eq(index).addClass('active');
            dots.removeClass('active');
            dots.eq(index).addClass('active');
            switchCount++;
            updateStatus();
        }
        
        // 开始自动轮播
        function startCarousel() {
            if (timer) {
                clearInterval(timer);
            }
            log('⏰ 启动自动轮播定时器');
            timer = setInterval(function() {
                current = current >= bannerItems.length - 1 ? 0 : current + 1;
                log(`🔄 自动切换到轮播图 ${current + 1}/${bannerItems.length}`);
                showBanner(current);
            }, 3000);
            updateStatus();
        }
        
        // 停止自动轮播
        function stopCarousel() {
            if (timer) {
                clearInterval(timer);
                timer = null;
                log('⏸️ 暂停自动轮播');
                updateStatus();
            }
        }
        
        // 上一张
        function prevImage() {
            stopCarousel();
            current = current <= 0 ? bannerItems.length - 1 : current - 1;
            log('👆 手动切换到上一张');
            showBanner(current);
            setTimeout(startCarousel, 1000);
        }
        
        // 下一张
        function nextImage() {
            stopCarousel();
            current = current >= bannerItems.length - 1 ? 0 : current + 1;
            log('👆 手动切换到下一张');
            showBanner(current);
            setTimeout(startCarousel, 1000);
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log-panel').innerHTML = '<div>🎠 轮播图测试控制台</div>';
            switchCount = 0;
            updateStatus();
        }
        
        // 初始化
        $(function() {
            log('🚀 轮播图测试页面初始化');
            log(`找到 ${bannerItems.length} 个轮播图项目`);
            log(`找到 ${dots.length} 个指示点`);
            
            // 绑定箭头点击事件
            $('.banner-arrow.prev').click(prevImage);
            $('.banner-arrow.next').click(nextImage);
            
            // 绑定指示点点击事件
            dots.click(function() {
                stopCarousel();
                current = $(this).index();
                log(`👆 点击指示点 ${current + 1}`);
                showBanner(current);
                setTimeout(startCarousel, 1000);
            });
            
            // 鼠标悬停控制
            $('.banner-box').hover(function() {
                log('🐭 鼠标悬停，暂停轮播');
                stopCarousel();
            }, function() {
                log('🐭 鼠标离开，恢复轮播');
                startCarousel();
            });
            
            // 初始化状态
            updateStatus();
            
            // 延迟启动自动轮播
            setTimeout(function() {
                log('🎯 启动自动轮播功能');
                startCarousel();
            }, 2000);
        });
    </script>
</body>
</html>
