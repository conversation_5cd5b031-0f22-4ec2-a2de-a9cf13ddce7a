{% extends 'base.html' %}
{% load static %}

{% block title %}个人中心 - MARS BUY 后台管理{% endblock %}

{% block page_title %}个人中心{% endblock %}

{% block page_actions %}
<div class="d-flex justify-content-end">
    <button type="button" class="btn btn-primary me-2" onclick="showEditProfileModal()">
        <i class="fas fa-edit"></i> 修改信息
    </button>
    <button type="button" class="btn btn-warning me-2" onclick="showChangePasswordModal()">
        <i class="fas fa-key"></i> 修改密码
    </button>
    <button type="button" class="btn btn-danger" onclick="confirmLogout()">
        <i class="fas fa-sign-out-alt"></i> 注销账户
    </button>
</div>
{% endblock %}

{% block content %}
<!-- 个人信息卡片 -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-body text-center">
                <div class="mb-3">
                    {% if user.avatar %}
                        <img src="{{ user.avatar.url }}" alt="{{ user.username }}" id="avatar-preview" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">
                    {% else %}
                        <img src="{% static 'images/default-avatar.png' %}" alt="默认头像" id="avatar-preview" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover;">
                    {% endif %}
                </div>
                <h5 class="card-title">{{ user.username }}</h5>
                {% if user.is_superuser %}
                    <span class="badge bg-danger mb-2">超级管理员</span>
                {% elif user.is_staff %}
                    <span class="badge bg-primary mb-2">管理员</span>
                {% endif %}
                <p class="text-muted">{{ user.first_name|default:"未设置姓名" }}</p>
                <button type="button" class="btn btn-outline-primary btn-sm" onclick="showUploadAvatarModal()">
                    <i class="fas fa-camera"></i> 更换头像
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user"></i> 基本信息</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>用户名：</strong></td>
                                <td>{{ user.username }}</td>
                            </tr>
                            <tr>
                                <td><strong>姓名：</strong></td>
                                <td>{{ user.first_name|default:"未设置" }}</td>
                            </tr>
                            <tr>
                                <td><strong>邮箱：</strong></td>
                                <td>{{ user.email|default:"未设置" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>手机号：</strong></td>
                                <td>{{ user.phone|default:"未设置" }}</td>
                            </tr>
                            <tr>
                                <td><strong>注册时间：</strong></td>
                                <td>{{ user.date_joined|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <td><strong>最后登录：</strong></td>
                                <td>
                                    {% if user.last_login %}
                                        {{ user.last_login|date:"Y-m-d H:i" }}
                                    {% else %}
                                        从未登录
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.total_logs }}</h4>
                        <p class="mb-0">总操作次数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-history fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.recent_logs_count }}</h4>
                        <p class="mb-0">近30天操作</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.login_count }}</h4>
                        <p class="mb-0">登录次数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-sign-in-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>
                            {% if stats.last_login %}
                                {{ stats.last_login|timesince }}前
                            {% else %}
                                从未
                            {% endif %}
                        </h4>
                        <p class="mb-0">最后登录</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近操作日志 -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0"><i class="fas fa-history"></i> 最近操作记录</h6>
    </div>
    <div class="card-body">
        {% if recent_logs %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>操作</th>
                        <th>描述</th>
                        <th>IP地址</th>
                        <th>时间</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in recent_logs %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ log.action }}</span>
                        </td>
                        <td>{{ log.description|default:"-" }}</td>
                        <td>
                            <code>{{ log.ip_address|default:"-" }}</code>
                        </td>
                        <td>{{ log.created_time|date:"Y-m-d H:i:s" }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <div class="text-center mt-3">
            <a href="{% url 'admin_panel:logs' %}" class="btn btn-outline-primary">
                <i class="fas fa-list"></i> 查看全部日志
            </a>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-history fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">暂无操作记录</h5>
            <p class="text-muted">您还没有进行过任何操作</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_content %}
<!-- 修改个人信息模态框 -->
<div class="modal fade" id="editProfileModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改个人信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editProfileForm">
                    <div class="mb-3">
                        <label for="editFirstName" class="form-label">姓名</label>
                        <input type="text" class="form-control" id="editFirstName" value="{{ user.first_name|default:'' }}">
                    </div>
                    <div class="mb-3">
                        <label for="editEmail" class="form-label">邮箱</label>
                        <input type="email" class="form-control" id="editEmail" value="{{ user.email|default:'' }}">
                    </div>
                    <div class="mb-3">
                        <label for="editPhone" class="form-label">手机号</label>
                        <input type="text" class="form-control" id="editPhone" value="{{ user.phone|default:'' }}">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveProfile()">保存修改</button>
            </div>
        </div>
    </div>
</div>

<!-- 修改密码模态框 -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">修改密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="changePasswordForm">
                    <div class="mb-3">
                        <label for="oldPassword" class="form-label">原密码 *</label>
                        <input type="password" class="form-control" id="oldPassword" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">新密码 *</label>
                        <input type="password" class="form-control" id="newPassword" required minlength="6">
                        <div class="form-text">密码长度不能少于6位</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">确认新密码 *</label>
                        <input type="password" class="form-control" id="confirmPassword" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="changePassword()">修改密码</button>
            </div>
        </div>
    </div>
</div>

<!-- 上传头像模态框 -->
<div class="modal fade" id="uploadAvatarModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更换头像</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="mb-3">
                        <img id="avatarPreview" src="" alt="头像预览" class="rounded-circle" style="width: 150px; height: 150px; object-fit: cover; display: none;">
                    </div>
                    <div class="mb-3">
                        <input type="file" class="form-control" id="avatarFile" accept="image/*" onchange="previewAvatar(this)">
                        <div class="form-text">支持 JPEG、PNG、GIF、WebP 格式，文件大小不超过5MB</div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadAvatar()" id="uploadBtn" disabled>上传头像</button>
            </div>
        </div>
    </div>
</div>
<script>
// 显示修改个人信息模态框
function showEditProfileModal() {
    new bootstrap.Modal(document.getElementById('editProfileModal')).show();
}

// 显示修改密码模态框
function showChangePasswordModal() {
    // 清空表单
    document.getElementById('changePasswordForm').reset();
    new bootstrap.Modal(document.getElementById('changePasswordModal')).show();
}

// 显示上传头像模态框
function showUploadAvatarModal() {
    // 清空文件选择和预览
    document.getElementById('avatarFile').value = '';
    document.getElementById('avatarPreview').style.display = 'none';
    document.getElementById('uploadBtn').disabled = true;
    new bootstrap.Modal(document.getElementById('uploadAvatarModal')).show();
}

// 保存个人信息
function saveProfile() {
    const formData = new FormData();
    formData.append('first_name', document.getElementById('editFirstName').value);
    formData.append('email', document.getElementById('editEmail').value);
    formData.append('phone', document.getElementById('editPhone').value);

    fetch('{% url "admin_panel:update_admin_profile" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('editProfileModal')).hide();
            location.reload();
        } else {
            alert('更新失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新失败，请重试');
    });
}

// 修改密码
function changePassword() {
    const oldPassword = document.getElementById('oldPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (!oldPassword || !newPassword || !confirmPassword) {
        alert('请填写所有密码字段');
        return;
    }

    if (newPassword !== confirmPassword) {
        alert('两次输入的新密码不一致');
        return;
    }

    if (newPassword.length < 6) {
        alert('新密码长度不能少于6位');
        return;
    }

    const formData = new FormData();
    formData.append('old_password', oldPassword);
    formData.append('new_password', newPassword);
    formData.append('confirm_password', confirmPassword);

    fetch('{% url "admin_panel:change_admin_password" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
            // 密码修改成功后跳转到登录页面
            setTimeout(() => {
                window.location.href = '{% url "admin_panel:login" %}';
            }, 1000);
        } else {
            alert('密码修改失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('密码修改失败，请重试');
    });
}

// 预览头像
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // 验证文件类型
        if (!file.type.match('image.*')) {
            alert('请选择图片文件');
            input.value = '';
            return;
        }

        // 验证文件大小
        if (file.size > 5 * 1024 * 1024) {
            alert('文件大小不能超过5MB');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('avatarPreview');
            preview.src = e.target.result;
            preview.style.display = 'block';
            document.getElementById('uploadBtn').disabled = false;
        };
        reader.readAsDataURL(file);
    }
}

// 上传头像
function uploadAvatar() {
    const fileInput = document.getElementById('avatarFile');
    if (!fileInput.files || !fileInput.files[0]) {
        alert('请选择头像文件');
        return;
    }

    const formData = new FormData();
    formData.append('avatar', fileInput.files[0]);

    // 禁用上传按钮
    const uploadBtn = document.getElementById('uploadBtn');
    uploadBtn.disabled = true;
    uploadBtn.textContent = '上传中...';

    fetch('{% url "admin_panel:upload_admin_avatar" %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': getCookie('csrftoken'),
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            // 更新页面上的头像
            if (data.avatar_url) {
                document.getElementById('avatar-preview').src = data.avatar_url;
            }
            bootstrap.Modal.getInstance(document.getElementById('uploadAvatarModal')).hide();
        } else {
            alert('头像上传失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('头像上传失败，请重试');
    })
    .finally(() => {
        // 恢复上传按钮
        uploadBtn.disabled = false;
        uploadBtn.textContent = '上传头像';
    });
}

// 获取Cookie
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 确认注销账户
function confirmLogout() {
    if (confirm('确定要注销当前账户吗？注销后需要重新登录。')) {
        fetch('{% url "admin_panel:admin_profile_logout" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('注销成功！');
                window.location.href = '{% url "admin_panel:login" %}';
            } else {
                alert('注销失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('注销失败，请重试');
        });
    }
}
</script>

{% csrf_token %}
{% endblock %}
