#!/usr/bin/env python3
"""
测试评价流程
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from users.models import User
from order.models import Order, OrderItem
from goods.models import Product
from reviews.models import Review

def test_review_flow():
    """测试评价流程"""
    print("🧪 测试评价流程...")
    print("=" * 60)
    
    # 1. 检查测试用户
    try:
        user = User.objects.get(username='testuser')
        print(f"✅ 找到测试用户: {user.username}")
    except User.DoesNotExist:
        print("❌ 测试用户不存在")
        return
    
    # 2. 检查用户的订单
    orders = Order.objects.filter(user=user)
    print(f"📦 用户订单数量: {orders.count()}")
    
    if orders.exists():
        for order in orders:
            print(f"   订单: {order.order_number} - 状态: {order.get_status_display()}")
            print(f"   可以评价: {'是' if order.can_review else '否'}")
            print(f"   已完全评价: {'是' if order.is_fully_reviewed else '否'}")
            
            # 检查订单项
            for item in order.items.all():
                print(f"     商品: {item.product.name} - 已评价: {'是' if item.is_reviewed else '否'}")
    
    # 3. 检查用户的评价
    reviews = Review.objects.filter(user=user)
    print(f"\n⭐ 用户评价数量: {reviews.count()}")
    
    if reviews.exists():
        for review in reviews:
            print(f"   评价: {review.product.name} - 评分: {review.score}星")
            print(f"   内容: {review.content[:50]}...")
            print(f"   时间: {review.created_at.strftime('%Y-%m-%d %H:%M')}")
    
    # 4. 检查URL配置
    print(f"\n🔗 URL配置检查:")
    
    from django.urls import reverse
    try:
        my_reviews_url = reverse('reviews:my_reviews')
        print(f"   ✅ 我的评价页面URL: {my_reviews_url}")
    except Exception as e:
        print(f"   ❌ 我的评价页面URL错误: {e}")
    
    try:
        add_review_url = reverse('reviews:add_review')
        print(f"   ✅ 添加评价页面URL: {add_review_url}")
    except Exception as e:
        print(f"   ❌ 添加评价页面URL错误: {e}")

def create_test_order_for_review():
    """创建测试订单用于评价"""
    print("\n🔧 创建测试订单...")
    
    try:
        user = User.objects.get(username='testuser')
        
        # 获取一个商品
        product = Product.objects.first()
        if not product:
            print("❌ 没有商品可用于测试")
            return
        
        # 创建订单
        order = Order.objects.create(
            user=user,
            order_number=f"TEST{user.id}{Order.objects.count() + 1:04d}",
            total_amount=product.price,
            status='received',  # 设置为已收货状态，可以评价
            shipping_address="测试地址",
            shipping_phone="13800138000",
            shipping_name="测试用户"
        )
        
        # 创建订单项
        order_item = OrderItem.objects.create(
            order=order,
            product=product,
            quantity=1,
            price=product.price,
            is_reviewed=False  # 未评价
        )
        
        print(f"✅ 创建测试订单: {order.order_number}")
        print(f"   商品: {product.name}")
        print(f"   状态: {order.get_status_display()}")
        print(f"   可以评价: {'是' if order.can_review else '否'}")
        
    except Exception as e:
        print(f"❌ 创建测试订单失败: {e}")

def test_order_review_status():
    """测试订单评价状态"""
    print("\n📊 测试订单评价状态...")
    
    orders = Order.objects.all()[:5]  # 取前5个订单测试
    
    for order in orders:
        print(f"\n订单: {order.order_number}")
        print(f"状态: {order.get_status_display()}")
        print(f"用户: {order.user.username}")
        print(f"可以评价: {'是' if order.can_review else '否'}")
        print(f"已完全评价: {'是' if order.is_fully_reviewed else '否'}")
        
        # 检查订单项评价状态
        total_items = order.items.count()
        reviewed_items = order.items.filter(is_reviewed=True).count()
        print(f"评价进度: {reviewed_items}/{total_items}")

def show_review_urls():
    """显示评价相关的URL"""
    print("\n🌐 评价相关URL:")
    print("前台页面:")
    print("  我的评价: http://127.0.0.1:8001/reviews/my-reviews/")
    print("  添加评价: http://127.0.0.1:8001/reviews/add-review/")
    print("  我的订单: http://127.0.0.1:8001/order/list/")
    print("  个人中心: http://127.0.0.1:8001/users/center/")

if __name__ == "__main__":
    try:
        test_review_flow()
        test_order_review_status()
        
        # 询问是否创建测试订单
        response = input("\n❓ 是否创建测试订单用于评价测试? (y/n): ")
        if response.lower() == 'y':
            create_test_order_for_review()
        
        show_review_urls()
        
        print("\n🎯 测试完成！")
        print("现在可以测试以下流程:")
        print("1. 登录用户账户")
        print("2. 查看我的订单")
        print("3. 点击'去评价'按钮")
        print("4. 提交评价")
        print("5. 查看'我的评价'页面")
        print("6. 确认订单状态变为'已评价'")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
