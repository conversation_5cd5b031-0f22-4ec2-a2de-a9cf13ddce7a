{% extends 'base.html' %}
{% load static %}

{% block title %}我的订单 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 修改为红色主题样式 */
    .profile-container {
        max-width: 1200px;
        margin: 40px auto;
        display: flex;
        gap: 20px;
    }

    .sidebar {
        width: 250px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.1);
        padding: 20px;
        border: 1px solid rgba(220, 53, 69, 0.1);
    }

    .user-avatar {
        text-align: center;
        margin-bottom: 20px;
    }

    .user-avatar img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #dc3545;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
    }

    .user-name {
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 10px;
    }

    .sidebar-menu a {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #666;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        transform: translateY(-2px);
    }

    .sidebar-menu a.active {
        background: #dc3545;
        color: white;
    }

    .sidebar-menu i {
        margin-right: 10px;
        width: 20px;
        font-size: 16px;
        text-align: center;
    }

    .main-content {
        flex: 1;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 30px;
        position: relative;
        overflow: hidden;
    }

    .main-content:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }

    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .profile-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .profile-header h2 {
        font-size: 24px;
        color: #dc3545;
        margin-bottom: 0;
        font-weight: 700;
        display: flex;
        align-items: center;
    }

    .profile-header h2 i {
        margin-right: 10px;
        color: #dc3545;
        font-size: 24px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #5cb85c 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #28a745 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(40, 167, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #5bc0de 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(23, 162, 184, 0.3);
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #138496 0%, #17a2b8 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(23, 162, 184, 0.4);
        color: white;
        text-decoration: none;
    }

    .table {
        width: 100%;
        margin-bottom: 1rem;
        border-collapse: separate;
        border-spacing: 0;
    }

    .table th,
    .table td {
        padding: 15px;
        vertical-align: middle;
        border-top: 1px solid #f0f0f0;
    }

    .table thead th {
        vertical-align: bottom;
        border-bottom: 2px solid #f0f0f0;
        color: #666;
        font-weight: 600;
        background-color: #f9f9f9;
    }

    .table tbody tr:hover {
        background-color: rgba(220, 53, 69, 0.03);
    }

    .order-product-img {
        width: 50px;
        height: 50px;
        object-fit: cover;
        border-radius: 5px;
        margin-right: 10px;
        border: 1px solid #f0f0f0;
    }

    .order-product-info {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .order-product-name {
        flex: 1;
        font-size: 14px;
    }

    .order-status {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-paid {
        background-color: #d4edda;
        color: #155724;
    }

    .status-shipped {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-received {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }

    .empty-orders {
        text-align: center;
        padding: 50px 0;
    }

    .empty-orders img {
        width: 150px;
        margin-bottom: 20px;
        opacity: 0.7;
    }

    .empty-orders p {
        color: #666;
        margin-bottom: 20px;
        font-size: 16px;
    }

    /* 响应式设计 */
    @media (max-width: 992px) {
        .profile-container {
            flex-direction: column;
        }

        .sidebar {
            width: 100%;
            margin-bottom: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="profile-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-avatar">
                <img src="{{ user.avatar.url }}" alt="{{ user.username }}" onerror="this.src='/media/avatars/default.png'">
                    </div>
            <div class="user-name">{{ user.username }}</div>
            <ul class="sidebar-menu">
                <li>
                    <a href="{% url 'users:center' %}">
                            <i class="fas fa-home"></i> 个人中心
                        </a>
                </li>
                <li>
                    <a href="{% url 'users:profile' %}">
                            <i class="fas fa-user"></i> 个人资料
                        </a>
                </li>
                <li>
                    <a href="{% url 'users:orders' %}" class="active">
                            <i class="fas fa-shopping-bag"></i> 我的订单
                        </a>
                </li>
                <li>
                    <a href="{% url 'users:address' %}">
                        <i class="fas fa-map-marker-alt"></i> 收货地址
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:password' %}">
                        <i class="fas fa-lock"></i> 修改密码
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- 主要内容 -->
        <div class="main-content">
            <div class="profile-header">
                <h2><i class="fas fa-shopping-bag"></i> 我的订单</h2>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="{% if message.tags == 'success' %}success-message{% else %}error-message{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

                    {% if orders %}
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>商品信息</th>
                                        <th>金额</th>
                                        <th>状态</th>
                                        <th>时间</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in orders %}
                                    <tr>
                                        <td>{{ order.order_number }}</td>
                                        <td>
                                            {% for item in order.items.all %}
                                        <div class="order-product-info">
                                            <img src="{{ item.product.main_image.url }}" alt="{{ item.product.name }}" class="order-product-img">
                                            <span class="order-product-name">{{ item.product.name }} x {{ item.quantity }}</span>
                                                </div>
                                            {% endfor %}
                                        </td>
                                        <td>¥{{ order.total_amount }}</td>
                                <td>
                                    <span class="order-status {% if order.status == 'pending' %}status-pending{% elif order.status == 'paid' %}status-paid{% elif order.status == 'shipped' %}status-shipped{% elif order.status == 'received' %}status-received{% elif order.status == 'cancelled' %}status-cancelled{% endif %}">
                                        {{ order.status_display }}
                                    </span>
                                </td>
                                        <td>{{ order.created_time|date:"Y-m-d H:i" }}</td>
                                        <td>
                                            <div class="btn-group-vertical">
                                        <a href="{% url 'order:detail' order.order_number %}" class="btn btn-primary mb-2">查看详情</a>
                                                {% if order.status == 'pending' %}
                                            <a href="{% url 'payment:pay_simple' order.order_number %}" class="btn btn-success">立即支付</a>
                                                {% endif %}
                                                {% if order.status == 'shipped' %}
                                            <button class="btn btn-info" onclick="confirmReceive('{{ order.order_number }}')">确认收货</button>
                                        {% endif %}
                                        {% if order.status == 'received' %}
                                            {% with first_item=order.items.first %}
                                                {% if first_item %}
                                                <a href="{% url 'reviews:add' first_item.product.id %}" class="btn btn-warning">评价商品</a>
                                                {% endif %}
                                            {% endwith %}
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                <div class="empty-orders">
                    <img src="{% static 'images/no-orders.png' %}" alt="暂无订单" onerror="this.src='https://cdn.jsdelivr.net/gh/twbs/icons/icons/basket.svg'">
                    <p>您还没有任何订单</p>
                    <a href="{% url 'goods:list' %}" class="btn btn-primary">去购物</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmReceive(orderNumber) {
    if (confirm('确认已收到商品吗？')) {
        fetch(`/api/orders/${orderNumber}/receive/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示确认成功消息
                alert('确认收货成功！即将跳转到商品评价页面');
                // 跳转到评价页面
                if (data.review_url) {
                    window.location.href = data.review_url;
                } else {
                location.reload();
                }
            } else {
                alert(data.message || '操作失败，请重试');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %} 