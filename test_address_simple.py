#!/usr/bin/env python3
"""
简单测试地址API
"""

import requests
import json

def test_address_api_simple():
    base_url = "http://127.0.0.1:8001"
    
    print("🔍 简单测试地址API...")
    
    # 1. 测试API端点是否存在
    print("1. 测试API端点...")
    try:
        response = requests.get(f"{base_url}/users/api/addresses/")
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text[:200]}")
        
        if response.status_code == 401:
            print("   ✅ API端点存在，需要认证")
        elif response.status_code == 200:
            print("   ✅ API端点存在，无需认证")
        else:
            print(f"   ⚠️  API端点状态异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ API测试异常: {e}")
    
    # 2. 测试地址页面
    print("\n2. 测试地址页面...")
    try:
        response = requests.get(f"{base_url}/users/address/")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 地址页面可访问")
            if "收货地址" in response.text:
                print("   ✅ 页面内容正常")
        elif response.status_code == 302:
            print("   ⚠️  页面重定向（可能需要登录）")
        else:
            print(f"   ❌ 页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 页面测试异常: {e}")
    
    # 3. 检查URL配置
    print("\n3. 检查URL配置...")
    endpoints_to_test = [
        "/users/api/addresses/",
        "/api/addresses/",
        "/users/address/",
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{base_url}{endpoint}")
            print(f"   {endpoint}: {response.status_code}")
        except Exception as e:
            print(f"   {endpoint}: 异常 - {e}")

if __name__ == "__main__":
    test_address_api_simple()
