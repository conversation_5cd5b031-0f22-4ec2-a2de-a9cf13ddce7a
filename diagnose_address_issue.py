#!/usr/bin/env python3
"""
诊断地址功能问题
"""

import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def diagnose_address_issue():
    base_url = "http://127.0.0.1:8001"
    session = requests.Session()
    
    print("🔍 诊断地址功能问题...")
    print("=" * 60)
    
    # 1. 先登录
    print("1. 测试用户登录...")
    try:
        # 获取登录页面
        login_url = urljoin(base_url, "/users/login/")
        response = session.get(login_url)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
            
            if csrf_input:
                csrf_token = csrf_input.get('value')
                print(f"   ✅ 获取CSRF令牌: {csrf_token[:20]}...")
                
                # 登录
                login_data = {
                    'username': 'testuser',
                    'password': '123456',
                    'user_type': 'normal',
                    'csrfmiddlewaretoken': csrf_token
                }
                
                login_response = session.post(login_url, data=login_data)
                print(f"   登录响应状态码: {login_response.status_code}")
                
                if login_response.status_code == 302:
                    print("   ✅ 登录成功")
                elif login_response.status_code == 200:
                    if "错误" in login_response.text or "失败" in login_response.text:
                        print("   ❌ 登录失败 - 用户名或密码错误")
                        return
                    else:
                        print("   ⚠️  登录状态不明确")
                        
            else:
                print("   ❌ 无法获取CSRF令牌")
                return
        else:
            print(f"   ❌ 无法访问登录页面: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return
    
    # 2. 检查地址页面
    print("\n2. 检查地址页面...")
    try:
        address_url = urljoin(base_url, "/users/address/")
        response = session.get(address_url)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 地址页面可访问")
            
            # 检查页面内容
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 检查关键元素
            add_btn = soup.find('button', {'id': 'addAddressBtn'})
            modal = soup.find('div', {'id': 'addressModal'})
            form = soup.find('form', {'id': 'addressForm'})
            
            print(f"   添加按钮: {'✅ 存在' if add_btn else '❌ 缺失'}")
            print(f"   模态框: {'✅ 存在' if modal else '❌ 缺失'}")
            print(f"   表单: {'✅ 存在' if form else '❌ 缺失'}")
            
            # 检查JavaScript错误
            if "error" in response.text.lower() or "undefined" in response.text.lower():
                print("   ⚠️  页面可能包含JavaScript错误")
                
        else:
            print(f"   ❌ 地址页面访问失败: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 地址页面检查异常: {e}")
        return
    
    # 3. 测试API端点
    print("\n3. 测试API端点...")
    api_tests = [
        ("GET", "/users/api/addresses/", None, "获取地址列表"),
        ("POST", "/users/api/addresses/", {
            'receiver': '测试用户',
            'phone': '13800138000',
            'province': '北京市',
            'city': '北京市',
            'district': '朝阳区',
            'detail': '测试地址',
            'is_default': True
        }, "添加地址"),
    ]
    
    for method, endpoint, data, description in api_tests:
        try:
            url = urljoin(base_url, endpoint)
            
            # 获取CSRF令牌
            csrf_token = None
            for cookie in session.cookies:
                if cookie.name == 'csrftoken':
                    csrf_token = cookie.value
                    break
            
            headers = {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrf_token
            } if csrf_token else {}
            
            if method == "GET":
                response = session.get(url)
            elif method == "POST":
                response = session.post(url, data=json.dumps(data), headers=headers)
            
            print(f"   {description}: {response.status_code}")
            
            if response.status_code in [200, 201]:
                print(f"     ✅ 成功")
                if method == "GET":
                    try:
                        data = response.json()
                        print(f"     返回数据: {len(data) if isinstance(data, list) else 'object'}")
                    except:
                        print(f"     返回内容: {response.text[:100]}...")
            elif response.status_code == 400:
                print(f"     ❌ 请求错误: {response.text}")
            elif response.status_code == 403:
                print(f"     ❌ 权限错误: {response.text}")
            elif response.status_code == 500:
                print(f"     ❌ 服务器错误: {response.text}")
            else:
                print(f"     ⚠️  状态码: {response.status_code}")
                print(f"     响应: {response.text[:200]}...")
                
        except Exception as e:
            print(f"   ❌ {description} 异常: {e}")
    
    # 4. 检查数据库连接
    print("\n4. 检查数据库和模型...")
    try:
        # 这里我们通过API间接检查
        response = session.get(urljoin(base_url, "/users/api/addresses/"))
        if response.status_code == 200:
            print("   ✅ 数据库连接正常")
        else:
            print(f"   ⚠️  数据库可能有问题: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 数据库检查异常: {e}")
    
    # 5. 检查URL配置
    print("\n5. 检查URL配置...")
    url_tests = [
        "/users/address/",
        "/users/api/addresses/",
        "/api/addresses/",
    ]
    
    for test_url in url_tests:
        try:
            response = session.get(urljoin(base_url, test_url))
            status = "✅" if response.status_code in [200, 403] else "❌"
            print(f"   {test_url}: {status} {response.status_code}")
        except Exception as e:
            print(f"   {test_url}: ❌ 异常 - {e}")
    
    print("\n" + "=" * 60)
    print("🎯 诊断完成！")
    print("\n💡 建议检查:")
    print("1. 浏览器开发者工具的Console标签页是否有JavaScript错误")
    print("2. 浏览器开发者工具的Network标签页查看API请求详情")
    print("3. 检查前台服务器日志是否有错误信息")

if __name__ == "__main__":
    diagnose_address_issue()
