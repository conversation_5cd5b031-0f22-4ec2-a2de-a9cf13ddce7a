# Generated by Django 4.2.22 on 2025-06-07 07:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('order', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('payment_method', models.CharField(choices=[('alipay', '支付宝'), ('wechat', '微信支付')], max_length=10, verbose_name='支付方式')),
                ('trade_no', models.CharField(db_index=True, max_length=100, unique=True, verbose_name='支付流水号')),
                ('amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='支付金额')),
                ('status', models.CharField(choices=[('pending', '待支付'), ('success', '支付成功'), ('failed', '支付失败'), ('refunded', '已退款')], db_index=True, default='pending', max_length=20, verbose_name='支付状态')),
                ('created_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('paid_time', models.DateTimeField(blank=True, null=True, verbose_name='支付时间')),
                ('refund_time', models.DateTimeField(blank=True, null=True, verbose_name='退款时间')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='order.order', verbose_name='订单')),
            ],
            options={
                'verbose_name': '支付记录',
                'verbose_name_plural': '支付记录',
                'ordering': ['-created_time'],
                'indexes': [models.Index(fields=['order', 'status'], name='payment_pay_order_i_b37863_idx'), models.Index(fields=['trade_no'], name='payment_pay_trade_n_68e1fe_idx'), models.Index(fields=['payment_method', 'status'], name='payment_pay_payment_c42eba_idx')],
            },
        ),
    ]
