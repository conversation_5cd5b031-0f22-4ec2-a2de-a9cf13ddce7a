<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MARS BUY 后台管理系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 12px 20px;
            border-radius: 8px;
            margin: 2px 10px;
            transition: all 0.3s ease;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }
        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .navbar {
            background: white !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        .card:hover {
            transform: translateY(-2px);
        }
        .btn-primary {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
            font-weight: 600;
        }
        .badge {
            border-radius: 6px;
        }
        .stats-card {
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            border-left: 4px solid #dc3545;
        }

        /* 管理后台Logo样式 */
        .admin-logo-container {
            padding: 20px 15px;
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border-radius: 15px;
            margin-bottom: 10px;
            box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .admin-logo-container:before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .admin-logo-container:hover:before {
            animation: shine 1.5s ease-in-out;
        }

        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); opacity: 0; }
        }

        .admin-logo-container:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(220, 53, 69, 0.4);
        }

        .admin-logo-icon {
            font-size: 2.5rem;
            color: #fff;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .admin-logo-text {
            color: #fff;
        }

        .admin-logo-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin: 0;
            letter-spacing: 1px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #fff, #f8f9fa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .admin-logo-subtitle {
            font-size: 0.75rem;
            opacity: 0.9;
            font-weight: 400;
            letter-spacing: 0.5px;
            margin-top: 5px;
            display: block;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <a href="{% url 'admin_panel:dashboard' %}" class="text-decoration-none">
                            <div class="admin-logo-container">
                                <div class="admin-logo-icon">
                                    <i class="fas fa-rocket"></i>
                                </div>
                                <div class="admin-logo-text">
                                    <h4 class="admin-logo-title">MARS BUY</h4>
                                    <small class="admin-logo-subtitle">后台管理系统</small>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}" 
                               href="{% url 'admin_panel:dashboard' %}">
                                <i class="fas fa-tachometer-alt"></i>
                                管理首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'users' in request.resolver_match.namespace %}active{% endif %}" 
                               href="{% url 'users:list' %}">
                                <i class="fas fa-users"></i>
                                用户管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'goods' in request.resolver_match.namespace and 'category' not in request.resolver_match.url_name %}active{% endif %}"
                               href="{% url 'goods:list' %}">
                                <i class="fas fa-box"></i>
                                商品管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'goods' in request.resolver_match.namespace and 'category' in request.resolver_match.url_name %}active{% endif %}"
                               href="{% url 'goods:category_list' %}">
                                <i class="fas fa-tags"></i>
                                分类管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'orders' in request.resolver_match.namespace %}active{% endif %}" 
                               href="{% url 'orders:list' %}">
                                <i class="fas fa-shopping-bag"></i>
                                订单管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'statistics' %}active{% endif %}" 
                               href="{% url 'admin_panel:statistics' %}">
                                <i class="fas fa-chart-bar"></i>
                                数据统计
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'settings' %}active{% endif %}" 
                               href="{% url 'admin_panel:settings' %}">
                                <i class="fas fa-cog"></i>
                                系统设置
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'logs' %}active{% endif %}"
                               href="{% url 'admin_panel:logs' %}">
                                <i class="fas fa-history"></i>
                                操作日志
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'admin_profile' %}active{% endif %}"
                               href="{% url 'admin_panel:admin_profile' %}">
                                <i class="fas fa-user-circle"></i>
                                个人中心
                            </a>
                        </li>
                    </ul>
                    
                    <hr class="text-white-50 my-4">
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'admin_panel:logout' %}">
                                <i class="fas fa-sign-out-alt"></i>
                                退出登录
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区域 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <!-- 顶部导航栏 -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}管理首页{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            {% block page_actions %}{% endblock %}
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user-circle me-1"></i>
                                {{ user.username }}
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'admin_panel:settings' %}">
                                    <i class="fas fa-cog me-2"></i>设置
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'admin_panel:logout' %}">
                                    <i class="fas fa-sign-out-alt me-2"></i>退出
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- 消息提示 -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
