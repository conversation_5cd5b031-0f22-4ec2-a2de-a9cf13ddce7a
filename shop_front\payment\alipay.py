from alipay import AliPay

class AliPayAPI:
    def __init__(self, appid, app_notify_url, app_private_key_string,
                 alipay_public_key_string, sign_type="RSA2", debug=False):
        """
        初始化支付宝接口
        :param appid: 支付宝应用ID
        :param app_notify_url: 异步通知URL
        :param app_private_key_string: 应用私钥字符串
        :param alipay_public_key_string: 支付宝公钥字符串
        :param sign_type: 签名类型
        :param debug: 是否为调试模式
        """
        self.appid = appid
        self.app_notify_url = app_notify_url
        self.app_private_key_string = app_private_key_string
        self.alipay_public_key_string = alipay_public_key_string
        self.sign_type = sign_type
        self.debug = debug

        self._alipay = AliPay(
            appid=self.appid,
            app_notify_url=self.app_notify_url,
            app_private_key_string=self.app_private_key_string,
            alipay_public_key_string=self.alipay_public_key_string,
            sign_type=self.sign_type,
            debug=self.debug
        )
    
    def api_alipay_trade_page_pay(self, out_trade_no, total_amount,
                                 subject, return_url=None, notify_url=None):
        """
        生成支付链接
        :param out_trade_no: 订单号
        :param total_amount: 总金额
        :param subject: 订单标题
        :param return_url: 同步通知URL
        :param notify_url: 异步通知URL
        :return: 支付链接
        """
        order_string = self._alipay.api_alipay_trade_page_pay(
            out_trade_no=out_trade_no,
            total_amount=total_amount,
            subject=subject,
            return_url=return_url,
            notify_url=notify_url
        )
        return order_string
    
    def verify(self, data, signature):
        """
        验证支付宝返回的数据
        :param data: 数据
        :param signature: 签名
        :return: 验证结果
        """
        return self._alipay.verify(data, signature) 