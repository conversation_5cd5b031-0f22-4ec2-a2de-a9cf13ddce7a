#!/usr/bin/env python3
"""
测试用户管理功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backn.settings')
django.setup()

from users.models import User
from django.contrib.auth.hashers import make_password

def test_user_management_features():
    """测试用户管理功能"""
    print("🧪 测试用户管理功能...")
    print("=" * 60)
    
    # 1. 检查现有用户
    users = User.objects.all()
    print(f"📊 当前用户总数: {users.count()}")
    
    admin_users = User.objects.filter(is_staff=True)
    regular_users = User.objects.filter(is_staff=False)
    active_users = User.objects.filter(is_active=True)
    inactive_users = User.objects.filter(is_active=False)
    
    print(f"   管理员用户: {admin_users.count()}")
    print(f"   普通用户: {regular_users.count()}")
    print(f"   已激活用户: {active_users.count()}")
    print(f"   已禁用用户: {inactive_users.count()}")
    
    # 2. 显示用户列表
    print(f"\n👥 用户列表:")
    for user in users[:10]:  # 显示前10个用户
        status = []
        if user.is_superuser:
            status.append("超级管理员")
        elif user.is_staff:
            status.append("管理员")
        else:
            status.append("普通用户")
        
        if user.is_active:
            status.append("已激活")
        else:
            status.append("已禁用")
        
        print(f"   {user.username} - {user.email or '无邮箱'} - {' | '.join(status)}")
    
    return True

def test_user_crud_operations():
    """测试用户CRUD操作"""
    print(f"\n🔧 测试用户CRUD操作...")
    print("=" * 60)
    
    # 创建测试用户
    test_username = 'test_user_mgmt'
    
    # 清理可能存在的测试用户
    if User.objects.filter(username=test_username).exists():
        User.objects.filter(username=test_username).delete()
        print(f"✅ 清理已存在的测试用户: {test_username}")
    
    try:
        # 1. 创建用户
        user = User.objects.create(
            username=test_username,
            email='<EMAIL>',
            first_name='测试用户',
            phone='13800138888',
            password=make_password('TestPassword123!'),
            is_staff=False,
            is_active=True
        )
        print(f"✅ 创建用户成功: {user.username}")
        
        # 2. 读取用户
        retrieved_user = User.objects.get(username=test_username)
        print(f"✅ 读取用户成功: {retrieved_user.username}")
        
        # 3. 更新用户
        retrieved_user.first_name = '更新后的测试用户'
        retrieved_user.is_staff = True
        retrieved_user.save()
        print(f"✅ 更新用户成功: {retrieved_user.first_name}")
        
        # 4. 验证更新
        updated_user = User.objects.get(username=test_username)
        assert updated_user.first_name == '更新后的测试用户'
        assert updated_user.is_staff == True
        print(f"✅ 验证更新成功")
        
        # 5. 删除用户
        updated_user.delete()
        print(f"✅ 删除用户成功")
        
        # 6. 验证删除
        assert not User.objects.filter(username=test_username).exists()
        print(f"✅ 验证删除成功")
        
        return True
        
    except Exception as e:
        print(f"❌ CRUD操作失败: {e}")
        return False

def test_password_update():
    """测试密码更新功能"""
    print(f"\n🔐 测试密码更新功能...")
    print("=" * 60)
    
    test_username = 'test_password_user'
    
    try:
        # 清理可能存在的测试用户
        if User.objects.filter(username=test_username).exists():
            User.objects.filter(username=test_username).delete()
        
        # 创建测试用户
        user = User.objects.create(
            username=test_username,
            email='<EMAIL>',
            password=make_password('OldPassword123!'),
            is_active=True
        )
        print(f"✅ 创建测试用户: {user.username}")
        
        # 保存原密码哈希
        old_password_hash = user.password
        
        # 更新密码
        new_password = 'NewPassword456!'
        user.password = make_password(new_password)
        user.save()
        print(f"✅ 更新密码成功")
        
        # 验证密码已更改
        updated_user = User.objects.get(username=test_username)
        assert updated_user.password != old_password_hash
        print(f"✅ 验证密码哈希已更改")
        
        # 验证新密码可以认证
        from django.contrib.auth import authenticate
        auth_user = authenticate(username=test_username, password=new_password)
        assert auth_user is not None
        print(f"✅ 验证新密码认证成功")
        
        # 清理测试用户
        updated_user.delete()
        print(f"✅ 清理测试用户")
        
        return True
        
    except Exception as e:
        print(f"❌ 密码更新测试失败: {e}")
        return False

def show_management_urls():
    """显示管理相关URL"""
    print(f"\n🌐 用户管理相关URL:")
    print("=" * 60)
    print("后台管理:")
    print("  用户管理: http://127.0.0.1:8003/users/")
    print("  后台首页: http://127.0.0.1:8003/dashboard/")
    print("  管理员登录: http://127.0.0.1:8003/login/")
    print()
    print("前台用户:")
    print("  个人中心: http://127.0.0.1:8001/users/center/")
    print("  个人资料: http://127.0.0.1:8001/users/profile/")
    print("  地址管理: http://127.0.0.1:8001/users/address/")
    print("  用户登录: http://127.0.0.1:8001/account/login/")

def show_test_instructions():
    """显示测试说明"""
    print(f"\n📋 手动测试说明:")
    print("=" * 60)
    print("后台用户管理测试:")
    print("1. 启动后台服务器: cd backn && python manage.py runserver 127.0.0.1:8003")
    print("2. 登录后台管理: http://127.0.0.1:8003/login/")
    print("3. 访问用户管理: http://127.0.0.1:8003/users/")
    print("4. 测试搜索、筛选、编辑、删除功能")
    print("5. 测试修改用户名、密码、权限等")
    print()
    print("前台注销功能测试:")
    print("1. 启动前台服务器: cd shop_front && python manage.py runserver 127.0.0.1:8001")
    print("2. 登录前台用户: http://127.0.0.1:8001/account/login/")
    print("3. 访问个人中心: http://127.0.0.1:8001/users/center/")
    print("4. 点击侧边栏的'注销账户'链接")
    print("5. 确认注销功能正常工作")

if __name__ == "__main__":
    print("🚀 开始测试用户管理功能...")
    print("=" * 80)
    
    try:
        # 测试基本功能
        basic_test = test_user_management_features()
        
        # 测试CRUD操作
        crud_test = test_user_crud_operations()
        
        # 测试密码更新
        password_test = test_password_update()
        
        # 显示URL信息
        show_management_urls()
        
        # 显示测试说明
        show_test_instructions()
        
        # 测试结果总结
        print(f"\n🎯 测试结果总结:")
        print("=" * 60)
        print(f"基本功能测试: {'✅ 通过' if basic_test else '❌ 失败'}")
        print(f"CRUD操作测试: {'✅ 通过' if crud_test else '❌ 失败'}")
        print(f"密码更新测试: {'✅ 通过' if password_test else '❌ 失败'}")
        
        if all([basic_test, crud_test, password_test]):
            print(f"\n🎉 所有测试通过！用户管理功能正常工作。")
        else:
            print(f"\n⚠️ 部分测试失败，请检查相关功能。")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
