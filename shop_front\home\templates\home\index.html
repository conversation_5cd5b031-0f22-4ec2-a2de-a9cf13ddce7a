{% extends 'base.html' %}
{% load static %}

{% block title %}MARS BUY - 火星购物，品质生活{% endblock %}

{% block extra_css %}
<style>
    /* 全局重置和基础设置 */
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #ffffff;
    }

    /* 清除浮动 */
    .clearfix:after {
        content: "";
        display: table;
        clear: both;
    }

    .leftfix {
        float: left;
    }

    .rightfix {
        float: right;
    }

    /* 基础容器 */
    .container {
        width: 1190px;
        margin: 0 auto;
    }

    /* 顶部导航条 */
    .topbar {
        height: 30px;
        background-color: #ECECEC;
    }

    .welcome {
        height: 30px;
        line-height: 30px;
        font-size: 0;
        color: #666666;
    }

    .welcome span,
    .welcome a {
        font-size: 12px;
        text-decoration: none;
        color: #666666;
    }

    .welcome .hello {
        margin-right: 28px;
    }

    .welcome .login {
        padding-right: 10px;
        border-right: 1px solid #666666;
        color: #dd302d;
    }

    .welcome .register {
        padding-left: 10px;
        color: #dd302d;
    }

    .topbar-nav .list {
        height: 30px;
        line-height: 30px;
        list-style: none;
    }

    .topbar-nav .list li {
        float: left;
    }

    .topbar-nav .list li a {
        padding: 0 15px;
        border-right: 1px solid #666666;
        text-decoration: none;
        color: #666666;
        font-size: 12px;
    }

    .topbar-nav .list li:first-child a {
        padding-left: 0;
    }

    .topbar-nav .list li:last-child a {
        padding-right: 0;
        border: 0;
    }

    .topbar-nav .list li a:hover {
        color: #dd302d;
    }

    /* 头部 */
    .header {
        height: 120px;
        background-color: #ffffff;
    }

    .logo {
        margin-top: 25px;
    }

    .logo h1 {
        font-size: 48px;
        color: #dd302d;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(221, 48, 45, 0.3);
    }

    .search {
        margin-top: 42px;
    }

    .search form {
        font-size: 0;
    }

    .search input {
        width: 508px;
        height: 34px;
        border: 2px solid #dd302d;
        padding: 0 15px;
        font-size: 14px;
        outline: none;
    }

    .search input:focus {
        border-color: #c82333;
        box-shadow: 0 0 5px rgba(221, 48, 45, 0.3);
    }

    .search button {
        width: 80px;
        height: 38px;
        background-color: #dd302d;
        border: none;
        color: white;
        font-size: 14px;
        cursor: pointer;
        vertical-align: top;
        transition: background-color 0.3s;
    }

    .search button:hover {
        background-color: #c82333;
    }

    /* 分类导航样式调整 */
    .category-dropdown {
        position: relative;
        height: 400px;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        background-color: #fff;
    }

    .all-category-title {
        padding: 10px 15px;
        background-color: #dd302d;
        color: #fff;
        font-weight: bold;
        font-size: 16px;
        display: flex;
        align-items: center;
    }

    .all-category-title i {
        margin-right: 8px;
    }

    .category-list {
        list-style: none;
        height: calc(400px - 42px); /* 总高度减去标题高度 */
        overflow-y: auto;
    }

    .category-list::-webkit-scrollbar {
        width: 5px;
    }

    .category-list::-webkit-scrollbar-thumb {
        background-color: rgba(221, 48, 45, 0.3);
        border-radius: 3px;
    }

    .category-list::-webkit-scrollbar-track {
        background-color: #f5f5f5;
    }

    .category-list > li {
        position: relative;
        padding: 8px 15px;
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s;
    }

    .category-list > li:hover {
        background-color: #dd302d;
    }

    .category-list > li:hover > a {
        color: #fff;
    }

    .category-list > li > a {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333;
        text-decoration: none;
        font-size: 14px;
    }

    .category-list > li > a i:first-child {
        margin-right: 8px;
        width: 16px;
        text-align: center;
    }

    .sub-categories {
        position: absolute;
        left: 100%;
        top: 0;
        width: 500px;
        min-height: 100%;
        background-color: #fff;
        border: 1px solid #dd302d;
        border-left: none;
        padding: 15px;
        display: none;
        z-index: 100;
        box-shadow: 5px 5px 15px rgba(0,0,0,0.1);
    }

    .category-list > li:hover .sub-categories {
        display: block;
    }

    .sub-category-group {
        margin-bottom: 15px;
    }

    .sub-category-title {
        font-weight: bold;
        color: #dd302d;
        margin-bottom: 8px;
        padding-bottom: 5px;
        border-bottom: 1px dashed #f0f0f0;
    }

    .sub-category-items {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .sub-category-items a {
        color: #666;
        text-decoration: none;
        padding: 3px 8px;
        border-radius: 15px;
        font-size: 12px;
        transition: all 0.3s;
        background-color: #f9f9f9;
    }

    .sub-category-items a:hover {
        color: #fff;
        background-color: #dd302d;
    }

    /* 轮播图样式 */
    .banner-box {
        position: relative;
        width: 100%;
        height: 400px;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .banner-list {
        width: 100%;
        height: 100%;
    }

    .banner-item {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        transition: opacity 0.5s ease;
    }

    .banner-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .banner-dots {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 8px;
    }

    .banner-dots span {
        width: 10px;
        height: 10px;
        background-color: rgba(255, 255, 255, 0.5);
        border-radius: 50%;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .banner-dots span.active {
        width: 20px;
        border-radius: 5px;
        background-color: #fff;
    }

    .banner-arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        width: 40px;
        height: 40px;
        background-color: rgba(0, 0, 0, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        cursor: pointer;
        transition: all 0.3s ease;
        z-index: 10;
    }

    .banner-arrow.prev {
        left: 20px;
    }

    .banner-arrow.next {
        right: 20px;
    }

    .banner-arrow:hover {
        background-color: rgba(0, 0, 0, 0.6);
    }

    /* 消息区域样式 */
    .message {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        padding: 15px;
        margin-bottom: 15px;
        height: 200px; /* 调整高度为轮播图的一半 */
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-radius: 12px;
        border: 1px solid rgba(220, 53, 69, 0.1);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        transform: translateX(0);
    }

    .message:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(220, 53, 69, 0.15);
    }

    .message:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: linear-gradient(to bottom, #dc3545, #ff6b6b);
    }

    .message .title {
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 10px;
        margin-bottom: 12px;
        font-weight: 600;
        color: #333;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .message .title .fl {
        position: relative;
        padding-left: 8px;
        font-size: 16px;
    }

    .message .title .fr {
        font-size: 12px;
        color: #999;
        transition: all 0.3s ease;
        text-decoration: none;
        padding: 4px 8px;
        border-radius: 4px;
        background-color: #f8f9fa;
    }

    .message .title .fr:hover {
        color: #dc3545;
        background-color: #f0f0f0;
        text-decoration: none;
    }

    .message-list {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .message-list li {
        height: 32px;
        line-height: 32px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
        padding-left: 15px;
        color: #666;
        transition: all 0.3s ease;
        border-radius: 4px;
        margin-bottom: 4px;
    }

    .message-list li:hover {
        background-color: rgba(220, 53, 69, 0.05);
        padding-left: 18px;
        color: #dc3545;
    }

    .message-list li i {
        margin-right: 8px;
        font-size: 14px;
    }

    .message-list li .text-danger {
        color: #dc3545;
    }

    .message-list li .text-primary {
        color: #007bff;
    }

    .message-list li .text-success {
        color: #28a745;
    }

    .message-list li .text-warning {
        color: #ffc107;
    }

    /* 添加一些动画效果 */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }

    .message-list li:nth-child(1) {
        animation: fadeInRight 0.5s ease-out 0.1s both;
    }
    
    .message-list li:nth-child(2) {
        animation: fadeInRight 0.5s ease-out 0.2s both;
    }
    
    .message-list li:nth-child(3) {
        animation: fadeInRight 0.5s ease-out 0.3s both;
    }
    
    .message-list li:nth-child(4) {
        animation: fadeInRight 0.5s ease-out 0.4s both;
    }
    
    @keyframes fadeInRight {
        from {
            opacity: 0;
            transform: translateX(20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* 快捷功能区样式 */
    .quick-functions {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border: 1px solid rgba(220, 53, 69, 0.1);
        overflow: hidden;
        height: 185px; /* 与消息区域高度相匹配 */
    }

    .function-item {
        border-bottom: 1px solid #f0f0f0;
        transition: all 0.3s ease;
    }

    .function-item:last-child {
        border-bottom: none;
    }

    .function-item:hover {
        background-color: rgba(220, 53, 69, 0.05);
        transform: translateX(3px);
    }

    .function-link {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        text-decoration: none;
        color: #333;
        transition: all 0.3s ease;
    }

    .function-link:hover {
        color: #dc3545;
        text-decoration: none;
    }

    .function-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        transition: all 0.3s ease;
    }

    .function-icon i {
        color: white;
        font-size: 16px;
    }

    .function-item:hover .function-icon {
        transform: scale(1.1);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    }

    .function-text {
        flex: 1;
    }

    .function-title {
        display: block;
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
    }

    .function-desc {
        display: block;
        font-size: 12px;
        color: #666;
    }

    .function-item:hover .function-title {
        color: #dc3545;
    }

    /* 推荐区域增强样式 */
    .recommend-section {
        margin: 30px 0;
        background: linear-gradient(135deg, #fff 0%, #f9f9f9 100%);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        padding: 20px 0;
        position: relative;
    }
    
    .recommend-section:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6c5ce7, #a29bfe, #6c5ce7);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }
    
    .recommend-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px 15px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 20px;
    }
    
    .recommend-title {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .recommend-title i {
        margin-right: 10px;
        color: #6c5ce7;
        font-size: 24px;
    }
    
    .recommend-filters {
        display: flex;
        gap: 15px;
    }
    
    .recommend-filters .filter {
        padding: 6px 15px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
        color: #666;
        background: #f8f9fa;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .recommend-filters .filter:hover,
    .recommend-filters .filter.active {
        background: #6c5ce7;
        color: white;
    }
    
    .recommend-list {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 20px;
        padding: 0 15px;
    }
    
    .recommend-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
    }
    
    .recommend-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(108,92,231,0.15);
    }
    
    .recommend-link {
        display: block;
        text-decoration: none;
        color: inherit;
    }
    
    .recommend-image {
        height: 160px;
        overflow: hidden;
    }
    
    .recommend-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
    }
    
    .recommend-item:hover .recommend-image img {
        transform: scale(1.1);
    }
    
    .recommend-info {
        padding: 12px;
        position: relative;
    }
    
    .recommend-name {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        height: 40px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #333;
    }
    
    .recommend-price {
        font-size: 16px;
        font-weight: 700;
        color: #6c5ce7;
        margin-bottom: 5px;
    }
    
    .recommend-tag {
        display: inline-block;
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 10px;
        background: #f0f0f0;
        color: #666;
    }
    
    .quick-actions {
        position: absolute;
        bottom: 15px;
        right: 15px;
        display: flex;
        gap: 10px;
        opacity: 0;
        transform: translateY(10px);
        transition: all 0.3s ease;
    }
    
    .recommend-item:hover .quick-actions {
        opacity: 1;
        transform: translateY(0);
    }
    
    .quick-action {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background: white;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #666;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .quick-action:hover {
        transform: translateY(-3px);
    }
    
    .quick-action.view:hover {
        background: #6c5ce7;
        color: white;
    }
    
    .quick-action.cart:hover {
        background: #28a745;
        color: white;
    }
    
    .quick-action.favorite:hover {
        background: #dc3545;
        color: white;
    }
    
    /* 添加动画定义 */
    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    /* 响应式设计适配 */
    @media (max-width: 1200px) {
        .category-products {
            flex-direction: column;
        }
        
        .category-banner {
            width: 100%;
            height: 200px;
        }
        
        .category-items {
            width: 100%;
        }
        
        .recommend-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .recommend-filters {
            overflow-x: auto;
            padding-bottom: 5px;
            width: 100%;
        }
    }
    
    @media (max-width: 768px) {

        .recommend-list {
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }

        .category-title, .recommend-title, .section-title {
            font-size: 20px;
        }
    }

    @media (max-width: 576px) {

        .recommend-list {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        

        
        .recommend-image {
            height: 140px;
        }
        
        .recommend-name {
            font-size: 13px;
            height: 36px;
        }

        .recommend-price {
            font-size: 15px;
        }
        
        .quick-actions {
            display: none;
        }
    }

    /* 热门商品区域增强样式 */
    .hot-products-section {
        margin: 30px 0;
        background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
        padding: 20px 0;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        position: relative;
        overflow: hidden;
    }
    
    .hot-products-section:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }
    
    .hot-products-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px 15px;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 20px;
    }
    
    .hot-products-title {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .hot-products-title i {
        margin-right: 10px;
        color: #dc3545;
        font-size: 24px;
    }
    
    .hot-products-more {
        color: #666;
        font-size: 14px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }
    
    .hot-products-more:hover {
        color: #dc3545;
    }
    
    .hot-products-more i {
        margin-left: 5px;
        transition: transform 0.3s ease;
    }
    
    .hot-products-more:hover i {
        transform: translateX(3px);
    }
    
    .hot-products-layout {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 15px;
        padding: 0 15px;
    }
    
    .hot-product-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        position: relative;
    }
    
    .hot-product-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(220,53,69,0.15);
    }
    
    .hot-product-item.featured {
        grid-column: span 1;
    }
    
    .hot-product-image {
        height: 180px;
        overflow: hidden;
        position: relative;
    }
    
    .hot-product-item.featured .hot-product-image {
        height: 220px;
    }
    
    .hot-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
    }
    
    .hot-product-item:hover .hot-product-image img {
        transform: scale(1.08);
    }
    
    .hot-product-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 700;
        color: white;
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        box-shadow: 0 3px 10px rgba(220,53,69,0.3);
        z-index: 2;
    }
    
    .hot-product-info {
        padding: 15px;
    }
    
    .hot-product-item.featured .hot-product-info {
        background: linear-gradient(0deg, rgba(0,0,0,0.03) 0%, rgba(0,0,0,0.0) 100%);
    }
    
    .hot-product-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
        height: 44px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    
    .hot-product-item.featured .hot-product-name {
        font-size: 18px;
    }
    
    .hot-product-name a {
        color: inherit;
        text-decoration: none;
    }
    
    .hot-product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .hot-product-price {
        font-size: 18px;
        font-weight: 700;
        color: #dc3545;
    }
    
    .hot-product-item.featured .hot-product-price {
        font-size: 20px;
    }
    
    .hot-product-sales {
        font-size: 12px;
        color: #666;
    }
    
    .hot-product-btn {
        display: inline-block;
        width: 100%;
        padding: 8px 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #333;
        border-radius: 20px;
        text-decoration: none;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .hot-product-btn:hover {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: white;
        box-shadow: 0 5px 15px rgba(220,53,69,0.3);
    }
    
    .hot-product-item.featured .hot-product-btn {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: white;
    }
    
    .hot-product-item.featured .hot-product-btn:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
    }
    
    /* 新品区域增强样式 */
    .new-products-section {
        margin: 30px 0;
        background: linear-gradient(135deg, #fff 0%, #f0f7ff 100%);
        padding: 20px 0;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        position: relative;
        overflow: hidden;
    }
    
    .new-products-section:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #007bff, #00c3ff, #007bff);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }
    
    .new-products-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px 15px;
        border-bottom: 1px solid #e6f0ff;
        margin-bottom: 20px;
    }
    
    .new-products-title {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .new-products-title i {
        margin-right: 10px;
        color: #007bff;
        font-size: 24px;
    }
    
    .new-products-more {
        color: #666;
        font-size: 14px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
    }
    
    .new-products-more:hover {
        color: #007bff;
    }
    
    .new-products-more i {
        margin-left: 5px;
        transition: transform 0.3s ease;
    }
    
    .new-products-more i {
        margin-left: 5px;
        transition: transform 0.3s ease;
    }
    
    .new-products-more:hover i {
        transform: translateX(3px);
    }
    
    .new-products-carousel {
        padding: 0 15px;
        position: relative;
    }
    
    .new-products-slider {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 15px;
        padding: 10px 0;
    }
    
    .new-product-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        position: relative;
    }
    
    .new-product-item:hover {
        transform: translateY(-5px) scale(1.02);
        box-shadow: 0 15px 30px rgba(0,123,255,0.15);
    }
    
    .new-product-image {
        height: 180px;
        overflow: hidden;
        position: relative;
    }
    
    .new-product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.6s ease;
    }
    
    .new-product-item:hover .new-product-image img {
        transform: scale(1.08);
    }
    
    .new-product-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 700;
        color: white;
        background: linear-gradient(135deg, #007bff 0%, #00c3ff 100%);
        box-shadow: 0 3px 10px rgba(0,123,255,0.3);
        z-index: 2;
    }
    
    .new-product-info {
        padding: 15px;
    }
    
    .new-product-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
        height: 44px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #333;
    }
    
    .new-product-name a {
        color: inherit;
        text-decoration: none;
        transition: color 0.3s ease;
    }
    
    .new-product-name a:hover {
        color: #007bff;
    }
    
    .new-product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .new-product-price {
        font-size: 18px;
        font-weight: 700;
        color: #007bff;
    }
    
    .new-product-sales {
        font-size: 12px;
        color: #666;
    }
    
    .new-product-btn {
        display: inline-block;
        width: 100%;
        padding: 8px 0;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #333;
        border-radius: 20px;
        text-decoration: none;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .new-product-btn:hover {
        background: linear-gradient(135deg, #007bff 0%, #00c3ff 100%);
        color: white;
        box-shadow: 0 5px 15px rgba(0,123,255,0.3);
    }
    
    /* 移除轮播导航按钮，改为网格布局 */
    .carousel-nav {
        display: none;
    }
    
    @media (max-width: 1200px) {
        .container {
            width: 100%;
            padding: 0 15px;
        }
        
        .hot-products-layout {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .hot-product-item.featured {
            grid-column: span 1;
        }
        
        .new-products-slider {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        }
    }
    
    @media (max-width: 992px) {
        .hot-products-layout {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .hot-product-item.featured {
            grid-column: span 1;
        }
        
        .new-products-slider {
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        }
    }
    
    @media (max-width: 768px) {
        .hot-products-layout {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .new-products-slider {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .hot-product-image, .hot-product-item.featured .hot-product-image {
            height: 160px;
        }
        
        .new-product-image {
            height: 160px;
        }
    }
    
    @media (max-width: 576px) {
        .hot-products-layout {
            grid-template-columns: 1fr;
        }
        
        .hot-product-item.featured {
            grid-column: span 1;
        }
        
        .new-products-slider {
            grid-template-columns: 1fr;
        }
        
        .hot-product-image, .hot-product-item.featured .hot-product-image {
            height: 180px;
        }
        
        .new-product-image {
            height: 180px;
        }
    }



    /* 秒杀专区样式 */
    .seckill {
        margin: 30px 0;
        background: linear-gradient(135deg, #fff 0%, #fff0f0 100%);
        padding: 20px 0;
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.08);
        position: relative;
        overflow: hidden;
    }
    
    .seckill:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #ff4d4d, #ff8080, #ff4d4d);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }
    
    .seckill-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 15px 15px;
        border-bottom: 1px solid #ffe0e0;
        margin-bottom: 20px;
    }
    
    .seckill-title {
        font-size: 24px;
        font-weight: 700;
        color: #ff4d4d;
        margin: 0;
        display: flex;
        align-items: center;
    }
    
    .seckill-title:before {
        content: '\f017';
        font-family: 'Font Awesome 5 Free';
        margin-right: 10px;
        font-weight: 900;
    }
    
    .seckill-timer {
        background: rgba(255, 77, 77, 0.1);
        padding: 8px 15px;
        border-radius: 20px;
        color: #ff4d4d;
        font-weight: 600;
        font-size: 16px;
    }
    
    .seckill-timer strong {
        background: #ff4d4d;
        color: white;
        padding: 2px 5px;
        border-radius: 4px;
        margin: 0 2px;
        display: inline-block;
        min-width: 28px;
        text-align: center;
    }
    
    .seckill-content {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 15px;
        padding: 0 15px;
    }
    
    .seckill-item {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
        text-decoration: none;
        color: inherit;
        display: block;
        position: relative;
    }
    
    .seckill-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background: linear-gradient(135deg, #ff4d4d 0%, #ff8080 100%);
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: bold;
        z-index: 2;
        box-shadow: 0 3px 10px rgba(255,77,77,0.3);
    }
    
    .seckill-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(255,77,77,0.15);
    }
    
    .seckill-item img {
        width: 100%;
        height: 180px;
        object-fit: cover;
        transition: transform 0.6s ease;
    }
    
    .seckill-item:hover img {
        transform: scale(1.08);
    }
    
    .seckill-info {
        padding: 15px;
    }
    
    .seckill-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
        height: 44px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        color: #333;
    }
    
    .seckill-price {
        font-size: 14px;
        color: #ff4d4d;
        margin-bottom: 3px;
    }
    
    .seckill-price span {
        font-size: 20px;
        font-weight: 700;
    }
    
    .original-price {
        font-size: 12px;
        color: #999;
    }
    
    .original-price del {
        text-decoration: line-through;
    }
    
    /* 秒杀倒计时动画 */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.05); }
        100% { transform: scale(1); }
    }
    
    .seckill-countdown {
        animation: pulse 2s infinite;
        display: inline-block;
    }
    
    @media (max-width: 768px) {
        .seckill-content {
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        }
        
        .seckill-item img {
            height: 140px;
        }
        
        .seckill-name {
            font-size: 14px;
            height: 38px;
        }
        
        .seckill-price span {
            font-size: 18px;
        }
    }
    
    @media (max-width: 576px) {
        .seckill-content {
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .seckill-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }
        
        .seckill-item img {
            height: 120px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 新的导航栏 -->
<div class="nav-bar-container">
    {% include 'includes/top_navbar.html' %}
</div>

<div class="main-content py-3">
    <div class="container">
        <div class="row g-3">
            <!-- 左侧分类导航 -->
            <div class="col-md-3 col-lg-3">
                {% include 'includes/category_nav.html' %}
            </div>
            
            <!-- 中间轮播图 -->
            <div class="col-md-6 col-lg-6">
                <div class="banner-box">
                    <div class="banner-list">
                        {% for banner in banners %}
                        <div class="banner-item {% if forloop.first %}active{% endif %}">
                            <a href="{{ banner.url }}" target="_blank">
                                <img src="{{ banner.image.url }}" alt="{{ banner.title }}">
                            </a>
                        </div>
                        {% empty %}
                        <!-- 默认轮播图 - 使用用户提供的四张图片 -->
                        <div class="banner-item active">
                            <a href="{% url 'goods:list' %}">
                                <img src="/media/banners/轮播图 (1).png" alt="夏季数码促销">
                            </a>
                        </div>
                        <div class="banner-item">
                            <a href="{% url 'goods:list' %}">
                                <img src="/media/banners/轮播图 (2).png" alt="家电满减活动">
                            </a>
                        </div>
                        <div class="banner-item">
                            <a href="{% url 'goods:list' %}">
                                <img src="/media/banners/轮播图 (3).png" alt="新品美妆上市">
                            </a>
                        </div>
                        <div class="banner-item">
                            <a href="{% url 'goods:list' %}">
                                <img src="/media/banners/轮播图.png" alt="母婴促销">
                            </a>
                        </div>
                        {% endfor %}
                    </div>
                    <!-- 轮播图指示点 -->
                    <div class="banner-dots">
                        {% for banner in banners %}
                        <span class="{% if forloop.first %}active{% endif %}"></span>
                        {% empty %}
                        <span class="active"></span>
                        <span></span>
                        <span></span>
                        <span></span>
                        {% endfor %}
                    </div>
                    <!-- 轮播图箭头 -->
                    <div class="banner-arrow prev"><i class="fas fa-chevron-left"></i></div>
                    <div class="banner-arrow next"><i class="fas fa-chevron-right"></i></div>
                </div>
            </div>
            
            <!-- 右侧信息区 -->
            <div class="col-md-3 col-lg-3">
                <!-- 网站公告 -->
                <div class="message">
                    <div class="title">
                        <div class="fl">网站公告</div>
                        <a href="#" class="fr">更多</a>
                    </div>
                    <ul class="message-list">
                        <li><i class="fas fa-volume-up text-danger"></i> 欢迎来到MARS BUY购物平台</li>
                        <li><i class="fas fa-gift text-primary"></i> 新人注册送100元优惠券</li>
                        <li><i class="fas fa-tags text-success"></i> 全场满199元包邮</li>
                        <li><i class="fas fa-bolt text-warning"></i> 限时秒杀，每日10点开始</li>
                    </ul>
                </div>

                <!-- 快捷功能区 -->
                <div class="quick-functions">
                    <div class="function-item">
                        <a href="{% if user.is_authenticated %}{% url 'account:favorite_list' %}{% else %}{% url 'users:login' %}{% endif %}" class="function-link">
                            <div class="function-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="function-text">
                                <span class="function-title">我的收藏</span>
                                <span class="function-desc">收藏的商品</span>
                            </div>
                        </a>
                    </div>

                    <div class="function-item">
                        <a href="#" class="function-link" onclick="openCustomerService()">
                            <div class="function-icon">
                                <i class="fas fa-headset"></i>
                            </div>
                            <div class="function-text">
                                <span class="function-title">联系客服</span>
                                <span class="function-desc">在线咨询</span>
                            </div>
                        </a>
                    </div>

                    <div class="function-item">
                        <a href="{% if user.is_authenticated %}{% url 'users:orders' %}{% else %}{% url 'users:login' %}{% endif %}" class="function-link">
                            <div class="function-icon">
                                <i class="fas fa-shopping-bag"></i>
                            </div>
                            <div class="function-text">
                                <span class="function-title">我的订单</span>
                                <span class="function-desc">查看订单</span>
                            </div>
                        </a>
                    </div>

                    <div class="function-item">
                        <a href="{% if user.is_authenticated %}{% url 'users:center' %}{% else %}{% url 'users:login' %}{% endif %}" class="function-link">
                            <div class="function-icon">
                                <i class="fas fa-user-circle"></i>
                            </div>
                            <div class="function-text">
                                <span class="function-title">个人中心</span>
                                <span class="function-desc">账户管理</span>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 秒杀专区 -->
<div class="seckill">
    <div class="container">
        <div class="seckill-header">
            <h3 class="seckill-title">限时秒杀</h3>
            <div class="seckill-timer">
                <span class="seckill-countdown">距结束：<strong id="hour">08</strong>:<strong id="minute">30</strong>:<strong id="second">45</strong></span>
            </div>
        </div>
        <div class="seckill-content">
            {% for product in seckill_products %}
            <a href="{% url 'goods:detail' product.id %}" class="seckill-item">
                <div class="seckill-badge">秒杀</div>
                {% if product.main_image %}
                <img src="{{ product.main_image.url }}" alt="{{ product.name }}">
                {% else %}
                <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}">
                {% endif %}
                <div class="seckill-info">
                    <p class="seckill-name">{{ product.name }}</p>
                    <p class="seckill-price">秒杀价: <span>¥{{ product.price|floatformat:0|add:-200 }}</span></p>
                    <p class="original-price">原价: <del>¥{{ product.price }}</del></p>
                </div>
            </a>
            {% empty %}
            <!-- 如果没有秒杀商品，显示默认内容 -->
            <a href="#" class="seckill-item">
                <div class="seckill-badge">秒杀</div>
                <img src="{% static 'images/seckill/seckill1.jpg' %}" alt="轻薄笔记本电脑">
                <div class="seckill-info">
                    <p class="seckill-name">轻薄笔记本电脑</p>
                    <p class="seckill-price">秒杀价: <span>¥3999</span></p>
                    <p class="original-price">原价: <del>¥5999</del></p>
                </div>
            </a>
            <a href="#" class="seckill-item">
                <div class="seckill-badge">秒杀</div>
                <img src="{% static 'images/seckill/seckill2.jpg' %}" alt="无线降噪蓝牙耳机">
                <div class="seckill-info">
                    <p class="seckill-name">无线降噪蓝牙耳机</p>
                    <p class="seckill-price">秒杀价: <span>¥199</span></p>
                    <p class="original-price">原价: <del>¥599</del></p>
                </div>
            </a>
            <a href="#" class="seckill-item">
                <div class="seckill-badge">秒杀</div>
                <img src="{% static 'images/seckill/seckill3.jpg' %}" alt="高清智能投影仪">
                <div class="seckill-info">
                    <p class="seckill-name">高清智能投影仪</p>
                    <p class="seckill-price">秒杀价: <span>¥1299</span></p>
                    <p class="original-price">原价: <del>¥2999</del></p>
                </div>
            </a>
            <a href="#" class="seckill-item">
                <div class="seckill-badge">秒杀</div>
                <img src="{% static 'images/seckill/seckill4.jpg' %}" alt="智能运动手环">
                <div class="seckill-info">
                    <p class="seckill-name">智能运动手环</p>
                    <p class="seckill-price">秒杀价: <span>¥99</span></p>
                    <p class="original-price">原价: <del>¥299</del></p>
                </div>
            </a>
            {% endfor %}
        </div>
    </div>
</div>



<!-- 为你推荐 -->
<div class="recommend-section">
    <div class="container">
        <div class="recommend-header">
            <div class="recommend-title">
                <i class="fas fa-thumbs-up"></i>
                <span>为你推荐</span>
            </div>
            <div class="recommend-filters">
                <a href="#" class="filter active">猜你喜欢</a>
                <a href="#" class="filter">热卖单品</a>
                <a href="#" class="filter">新品尝鲜</a>
            </div>
        </div>
        
        <div class="recommend-list">
            {% for product in hot_products %}
            <div class="recommend-item">
                <a href="{% url 'goods:detail' product.id %}" class="recommend-link">
                    <div class="recommend-image">
                        {% if product.image %}
                        <img src="{{ product.image.url }}" alt="{{ product.name }}">
                        {% else %}
                        <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}">
                        {% endif %}
                    </div>
                    <div class="recommend-info">
                        <h4 class="recommend-name">{{ product.name }}</h4>
                        <div class="recommend-price">¥{{ product.price }}</div>
                        <div class="recommend-tag">库存充足</div>
                    </div>
                </a>
                <div class="quick-actions">
                    <a href="{% url 'goods:detail' product.id %}" class="quick-action view" title="查看详情">
                        <i class="fas fa-eye"></i>
                    </a>
                    <button class="quick-action cart" data-product-id="{{ product.id }}" title="加入购物车" onclick="addToCartFromHome({{ product.id }})">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                    <button class="quick-action favorite" data-product-id="{{ product.id }}" title="添加收藏" onclick="addToFavoriteFromHome({{ product.id }})">
                        <i class="far fa-heart"></i>
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 热门商品区域增强样式 -->
<div class="hot-products-section">
    <div class="container">
        <div class="hot-products-header clearfix">
            <h3 class="hot-products-title fl">
                <i class="fas fa-fire"></i> 热门商品
            </h3>
            <a href="{% url 'goods:hot' %}" class="hot-products-more fr">
                更多热门商品 <i class="fas fa-chevron-right"></i>
            </a>
        </div>
        <div class="hot-products-layout">
            {% for product in real_hot_products %}
            <div class="hot-product-item {% if forloop.counter == 1 %}featured{% endif %}">
                <div class="hot-product-image">
                    <a href="{% url 'goods:detail' product.id %}">
                        {% if product.main_image %}
                        <img src="{{ product.main_image.url }}" alt="{{ product.name }}">
                        {% else %}
                        <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}">
                        {% endif %}
                    </a>
                    {% if product.is_hot %}
                    <div class="hot-product-badge">热卖</div>
                    {% elif product.is_new %}
                    <div class="hot-product-badge">新品</div>
                    {% endif %}
                </div>
                <div class="hot-product-info">
                    <h3 class="hot-product-name">{{ product.name }}</h3>
                    <div class="hot-product-meta">
                        <div class="hot-product-price">¥{{ product.price }}</div>
                        <div class="hot-product-sales">{{ product.sales }}人已购买</div>
                    </div>
                    <div class="hot-product-actions">
                        <a href="{% url 'goods:detail' product.id %}" class="hot-product-btn">查看详情</a>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="empty-category">
                <i class="fas fa-fire"></i>
                <p>暂无热门商品</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- 新品区域增强样式 -->
<div class="new-products-section">
    <div class="container">
        <div class="new-products-header clearfix">
            <h3 class="new-products-title fl">
                <i class="fas fa-star"></i> 新品上市
            </h3>
            <a href="{% url 'goods:new' %}" class="new-products-more fr">
                更多新品 <i class="fas fa-chevron-right"></i>
            </a>
        </div>
        <div class="new-products-carousel">
            <div class="new-products-slider">
                {% for product in new_products %}
                <div class="new-product-item">
                    <div class="new-product-image">
                        <a href="{% url 'goods:detail' product.id %}">
                            {% if product.image %}
                            <img src="{{ product.image.url }}" alt="{{ product.name }}">
                            {% else %}
                            <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}">
                            {% endif %}
                        </a>
                        {% if product.is_new %}
                        <div class="new-product-badge">新品</div>
                        {% endif %}
                    </div>
                    <div class="new-product-info">
                        <h3 class="new-product-name">{{ product.name }}</h3>
                        <div class="new-product-meta">
                            <div class="new-product-price">¥{{ product.price }}</div>
                            <div class="new-product-sales">{{ product.sales }}人已购买</div>
                        </div>
                        <div class="new-product-actions">
                            <a href="{% url 'goods:detail' product.id %}" class="new-product-btn">查看详情</a>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="empty-category">
                    <i class="fas fa-star"></i>
                    <p>暂无新品上市</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 轮播图脚本 - 原生JavaScript版本
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🎠 轮播图初始化开始...');

        // 轮播图变量
        var bannerBox = document.querySelector('.banner-box');
        var bannerItems = document.querySelectorAll('.banner-item');
        var dots = document.querySelectorAll('.banner-dots span');
        var arrowPrev = document.querySelector('.banner-arrow.prev');
        var arrowNext = document.querySelector('.banner-arrow.next');
        var currentIndex = 0;
        var autoTimer = null;
        var isPlaying = false;

        console.log(`找到 ${bannerItems.length} 个轮播图项目`);
        console.log(`找到 ${dots.length} 个指示点`);

        // 如果没有轮播图项目，直接返回
        if (bannerItems.length <= 1) {
            console.log('❌ 轮播图项目不足，无需轮播');
            return;
        }

        // 初始化轮播图
        function initBanner() {
            console.log('🔧 初始化轮播图状态');
            bannerItems.forEach(function(item, index) {
                item.classList.remove('active');
                item.style.display = 'none';
                if (index === 0) {
                    item.classList.add('active');
                    item.style.display = 'block';
                }
            });

            dots.forEach(function(dot, index) {
                dot.classList.remove('active');
                if (index === 0) {
                    dot.classList.add('active');
                }
            });

            currentIndex = 0;
        }

        // 显示指定索引的轮播图
        function showSlide(index) {
            if (index < 0 || index >= bannerItems.length) {
                console.log(`❌ 无效的索引: ${index}`);
                return;
            }

            console.log(`🖼️ 切换到轮播图 ${index + 1}/${bannerItems.length}`);

            // 隐藏所有图片
            bannerItems.forEach(function(item) {
                item.classList.remove('active');
                item.style.opacity = '0';
                setTimeout(function() {
                    item.style.display = 'none';
                }, 300);
            });

            // 移除所有指示点的active类
            dots.forEach(function(dot) {
                dot.classList.remove('active');
            });

            // 显示新图片
            setTimeout(function() {
                bannerItems[index].style.display = 'block';
                bannerItems[index].classList.add('active');
                bannerItems[index].style.opacity = '1';
                dots[index].classList.add('active');
                currentIndex = index;
            }, 300);
        }

        // 下一张
        function nextSlide() {
            var nextIndex = (currentIndex + 1) % bannerItems.length;
            console.log(`➡️ 下一张: ${currentIndex} -> ${nextIndex}`);
            showSlide(nextIndex);
        }

        // 上一张
        function prevSlide() {
            var prevIndex = (currentIndex - 1 + bannerItems.length) % bannerItems.length;
            console.log(`⬅️ 上一张: ${currentIndex} -> ${prevIndex}`);
            showSlide(prevIndex);
        }

        // 开始自动播放
        function startAutoPlay() {
            if (isPlaying) {
                console.log('⚠️ 自动播放已在运行');
                return;
            }

            console.log('▶️ 开始自动播放');
            isPlaying = true;
            autoTimer = setInterval(function() {
                console.log(`🔄 自动播放: 当前 ${currentIndex}, 切换到下一张`);
                nextSlide();
            }, 3000);
        }

        // 停止自动播放
        function stopAutoPlay() {
            if (!isPlaying) {
                return;
            }

            console.log('⏸️ 停止自动播放');
            isPlaying = false;
            if (autoTimer) {
                clearInterval(autoTimer);
                autoTimer = null;
            }
        }

        // 重启自动播放
        function restartAutoPlay() {
            stopAutoPlay();
            setTimeout(startAutoPlay, 1000);
        }

        // 绑定事件
        if (arrowNext) {
            arrowNext.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('👆 点击下一张按钮');
                stopAutoPlay();
                nextSlide();
                restartAutoPlay();
            });
        }

        if (arrowPrev) {
            arrowPrev.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('👆 点击上一张按钮');
                stopAutoPlay();
                prevSlide();
                restartAutoPlay();
            });
        }

        dots.forEach(function(dot, index) {
            dot.addEventListener('click', function(e) {
                e.preventDefault();
                console.log(`👆 点击指示点: ${index}`);
                if (index !== currentIndex) {
                    stopAutoPlay();
                    showSlide(index);
                    restartAutoPlay();
                }
            });
        });

        // 鼠标悬停控制
        if (bannerBox) {
            bannerBox.addEventListener('mouseenter', function() {
                console.log('🐭 鼠标进入，暂停自动播放');
                stopAutoPlay();
            });

            bannerBox.addEventListener('mouseleave', function() {
                console.log('🐭 鼠标离开，恢复自动播放');
                startAutoPlay();
            });
        }

        // 初始化并启动
        initBanner();

        // 延迟启动自动播放
        setTimeout(function() {
            console.log('🚀 启动轮播图自动播放');
            startAutoPlay();
        }, 2000);

        console.log('✅ 轮播图初始化完成');
    });

    // 客服功能
    function openCustomerService() {
        // 可以打开客服聊天窗口或显示联系方式
        alert('客服电话：400-123-4567\n在线客服：周一至周日 9:00-22:00\n或者您可以发送邮件至：<EMAIL>');
    }

    // 秒杀倒计时功能
    $(function() {
        // 设置结束时间 - 当前时间加上8小时30分钟45秒
        var endTime = new Date();
        endTime.setHours(endTime.getHours() + 8);
        endTime.setMinutes(endTime.getMinutes() + 30);
        endTime.setSeconds(endTime.getSeconds() + 45);
        
        // 更新倒计时
        function updateCountdown() {
            var now = new Date();
            var diff = Math.floor((endTime - now) / 1000); // 剩余秒数
            
            if (diff <= 0) {
                // 倒计时结束
                clearInterval(countdownTimer);
                $('#hour').text('00');
                $('#minute').text('00');
                $('#second').text('00');
                return;
            }
            
            // 计算剩余时间
            var hours = Math.floor(diff / 3600);
            var minutes = Math.floor((diff % 3600) / 60);
            var seconds = diff % 60;
            
            // 更新显示
            $('#hour').text(hours < 10 ? '0' + hours : hours);
            $('#minute').text(minutes < 10 ? '0' + minutes : minutes);
            $('#second').text(seconds < 10 ? '0' + seconds : seconds);
        }
        
        // 立即更新一次
        updateCountdown();
        
        // 设置定时器，每秒更新一次
        var countdownTimer = setInterval(updateCountdown, 1000);
    });
    
    // 首页加入购物车功能
    function addToCartFromHome(productId) {
        // 检查用户是否登录
        {% if user.is_authenticated %}
        // 获取CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

        // 发送AJAX请求
        fetch("{% url 'order:cart_add' %}", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken
            },
            body: `product_id=${productId}&quantity=1`
        })
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                // 成功提示
                showMessage('商品已加入购物车！', 'success');
            } else {
                throw new Error(data.msg || '添加失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('添加失败，请重试', 'error');
        });
        {% else %}
        // 未登录时跳转到登录页面
        window.location.href = "{% url 'account:login' %}?next=" + window.location.pathname;
        {% endif %}
    }

    // 首页添加收藏功能
    function addToFavoriteFromHome(productId) {
        // 检查用户是否登录
        {% if user.is_authenticated %}
        // 获取CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

        // 发送AJAX请求
        fetch("{% url 'account:add_favorite' 0 %}".replace('0', productId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success' || data.status === 'info') {
                // 成功提示
                showMessage(data.message || '商品已添加到收藏夹！', 'success');

                // 更新按钮状态
                const button = event.target.closest('button');
                const icon = button.querySelector('i');
                icon.className = 'fas fa-heart';
                button.style.color = '#ff6b6b';
            } else {
                throw new Error(data.message || '添加失败');
            }
        })
        .catch(error => {
            console.error('收藏失败:', error);
            showMessage(error.message || '收藏失败，请重试', 'error');
        });
        {% else %}
        // 未登录时跳转到登录页面
        window.location.href = "{% url 'account:login' %}?next=" + window.location.pathname;
        {% endif %}
    }

    // 显示消息提示
    function showMessage(message, type) {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} message-toast`;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideInRight 0.3s ease-out;
        `;
        messageDiv.textContent = message;

        // 添加到页面
        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 300);
        }, 3000);
    }
</script>

<style>
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}
</style>
{% endblock %}