from django.urls import path
from order import views




app_name = 'order'

urlpatterns = [
    path('cart/', views.CartView.as_view(), name='cart'),
    path('cart/add/', views.CartAddView.as_view(), name='cart_add'),
    path('cart/update/', views.CartUpdateView.as_view(), name='cart_update'),
    path('cart/delete/', views.CartDeleteView.as_view(), name='cart_delete'),
    path('buy-now/', views.BuyNowView.as_view(), name='buy_now'),
    path('list/', views.OrderListView.as_view(), name='list'),
    path('create/', views.OrderCreateView.as_view(), name='create'),
    path('detail/<str:order_number>/', views.OrderDetailView.as_view(), name='detail'),
    path('cancel/<str:order_number>/', views.cancel_order, name='cancel'),
    path('pay/<str:order_number>/', views.pay_order, name='pay'),
    path('confirm/<str:order_number>/', views.confirm_order, name='confirm'),
]
