from django.db import models
from django.utils.translation import gettext_lazy as _

class SystemConfig(models.Model):
    """系统配置模型"""
    key = models.CharField(max_length=100, unique=True, verbose_name=_('配置键'))
    value = models.TextField(verbose_name=_('配置值'))
    description = models.CharField(max_length=255, blank=True, null=True, verbose_name=_('配置描述'))
    is_active = models.BooleanField(default=True, verbose_name=_('是否启用'))

    class Meta:
        verbose_name = _('系统配置')
        verbose_name_plural = verbose_name
        ordering = ['key']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.key}: {self.value[:30]}"

class Settings(models.Model):
    """系统设置模型"""
    class Meta:
        verbose_name = '系统设置'
        verbose_name_plural = verbose_name

    # 基本设置
    site_name = models.CharField('网站名称', max_length=100, default='MARS BUY商城')
    site_description = models.TextField('网站描述', blank=True)
    contact_email = models.EmailField('联系邮箱', blank=True)
    contact_phone = models.CharField('联系电话', max_length=20, blank=True)

    # 邮件设置
    smtp_host = models.CharField('SMTP服务器', max_length=100, blank=True)
    smtp_port = models.IntegerField('SMTP端口', default=25)
    smtp_user = models.CharField('SMTP用户名', max_length=100, blank=True)
    smtp_password = models.CharField('SMTP密码', max_length=100, blank=True)
    smtp_ssl = models.BooleanField('使用SSL', default=True)

    # 支付设置
    alipay_app_id = models.CharField('支付宝AppID', max_length=100, blank=True)
    alipay_private_key = models.TextField('支付宝私钥', blank=True)
    alipay_public_key = models.TextField('支付宝公钥', blank=True)
    wechat_app_id = models.CharField('微信支付AppID', max_length=100, blank=True)
    wechat_mch_id = models.CharField('微信支付商户号', max_length=100, blank=True)
    wechat_key = models.CharField('微信支付密钥', max_length=100, blank=True)

    # 其他设置
    order_timeout = models.IntegerField('订单超时时间(分钟)', default=30)
    maintenance_mode = models.BooleanField('维护模式', default=False)
    maintenance_message = models.TextField('维护提示信息', blank=True)

    def __str__(self):
        return self.site_name
