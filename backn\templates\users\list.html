{% extends 'base.html' %}

{% block title %}用户管理 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}用户管理{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
        <i class="fas fa-filter me-1"></i>筛选
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="?status=active">活跃用户</a></li>
        <li><a class="dropdown-item" href="?status=inactive">禁用用户</a></li>
        <li><a class="dropdown-item" href="?vip=vip">VIP用户</a></li>
        <li><a class="dropdown-item" href="?vip=normal">普通用户</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'users:list' %}">全部用户</a></li>
    </ul>
</div>
{% endblock %}

{% block content %}
<!-- 搜索栏 -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="搜索用户名、邮箱或手机号" value="{{ search_query }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <span class="text-muted">共 {{ total_users }} 个用户</span>
    </div>
</div>

<div class="row">
    <!-- 管理员列表 -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>管理员 ({{ admin_page_obj.paginator.count }})</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>头像</th>
                                <th>用户名</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in admin_page_obj %}
                            <tr{% if user.id == current_user.id %} class="table-primary"{% endif %}>
                                <td>
                                    {% if user.avatar %}
                                        <img src="{{ user.avatar.url }}" alt="{{ user.username }}" 
                                             class="rounded-circle" width="40" height="40" style="object-fit: cover; border: 2px solid #dc3545;">
                                    {% else %}
                                        <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px; border: 2px solid #dc3545; background-color: #f8f9fa;">
                                            <i class="fas fa-user text-primary"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ user.username }}</strong>
                                    {% if user.id == current_user.id %}
                                        <span class="badge bg-info ms-1">当前用户</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_superuser %}
                                        <span class="badge bg-danger">超级管理员</span>
                                    {% elif user.is_staff %}
                                        <span class="badge bg-danger">管理员</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">活跃</span>
                                    {% else %}
                                        <span class="badge bg-danger">禁用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'users:detail' user.id %}" 
                                           class="btn btn-outline-primary" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if not user.is_superuser %}
                                        <button type="button" class="btn btn-outline-warning toggle-status" 
                                                data-user-id="{{ user.id }}" 
                                                data-current-status="{{ user.is_active }}"
                                                title="{% if user.is_active %}禁用{% else %}激活{% endif %}用户">
                                            {% if user.is_active %}
                                                <i class="fas fa-ban"></i>
                                            {% else %}
                                                <i class="fas fa-check"></i>
                                            {% endif %}
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-user-shield fa-3x mb-3"></i>
                                    <p>暂无管理员数据</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <!-- 管理员分页 -->
                {% if admin_page_obj.has_other_pages %}
                <nav aria-label="管理员列表分页" class="mt-3">
                    <ul class="pagination pagination-sm justify-content-center">
                        {% if admin_page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?admin_page=1{% if regular_page_number %}&regular_page={{ regular_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?admin_page={{ admin_page_obj.previous_page_number }}{% if regular_page_number %}&regular_page={{ regular_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ admin_page_obj.number }} / {{ admin_page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if admin_page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?admin_page={{ admin_page_obj.next_page_number }}{% if regular_page_number %}&regular_page={{ regular_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?admin_page={{ admin_page_obj.paginator.num_pages }}{% if regular_page_number %}&regular_page={{ regular_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 普通用户列表 -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>普通用户 ({{ regular_page_obj.paginator.count }})</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>头像</th>
                                <th>用户名</th>
                                <th>VIP</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in regular_page_obj %}
                            <tr{% if user.id == current_user.id %} class="table-primary"{% endif %}>
                                <td>
                                    {% if user.avatar %}
                                        <img src="{{ user.avatar.url }}" alt="{{ user.username }}" 
                                             class="rounded-circle" width="40" height="40" style="object-fit: cover; border: 2px solid #dc3545;">
                                    {% else %}
                                        <div class="rounded-circle d-flex align-items-center justify-content-center" 
                                             style="width: 40px; height: 40px; border: 2px solid #dc3545; background-color: #f8f9fa;">
                                            <i class="fas fa-user text-primary"></i>
                                        </div>
                                    {% endif %}
                                </td>
                                <td>
                                    <strong>{{ user.username }}</strong>
                                    {% if user.id == current_user.id %}
                                        <span class="badge bg-info ms-1">当前用户</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_vip %}
                                        <span class="badge bg-warning">VIP</span>
                                    {% else %}
                                        <span class="badge bg-secondary">普通</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_active %}
                                        <span class="badge bg-success">活跃</span>
                                    {% else %}
                                        <span class="badge bg-danger">禁用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'users:detail' user.id %}" 
                                           class="btn btn-outline-primary" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-outline-warning toggle-status" 
                                                data-user-id="{{ user.id }}" 
                                                data-current-status="{{ user.is_active }}"
                                                title="{% if user.is_active %}禁用{% else %}激活{% endif %}用户">
                                            {% if user.is_active %}
                                                <i class="fas fa-ban"></i>
                                            {% else %}
                                                <i class="fas fa-check"></i>
                                            {% endif %}
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-users fa-3x mb-3"></i>
                                    <p>暂无普通用户数据</p>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <!-- 普通用户分页 -->
                {% if regular_page_obj.has_other_pages %}
                <nav aria-label="普通用户列表分页" class="mt-3">
                    <ul class="pagination pagination-sm justify-content-center">
                        {% if regular_page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?regular_page=1{% if admin_page_number %}&admin_page={{ admin_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">首页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?regular_page={{ regular_page_obj.previous_page_number }}{% if admin_page_number %}&admin_page={{ admin_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">上一页</a>
                            </li>
                        {% endif %}
                        
                        <li class="page-item active">
                            <span class="page-link">{{ regular_page_obj.number }} / {{ regular_page_obj.paginator.num_pages }}</span>
                        </li>
                        
                        {% if regular_page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?regular_page={{ regular_page_obj.next_page_number }}{% if admin_page_number %}&admin_page={{ admin_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">下一页</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?regular_page={{ regular_page_obj.paginator.num_pages }}{% if admin_page_number %}&admin_page={{ admin_page_number }}{% endif %}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if vip_filter %}&vip={{ vip_filter }}{% endif %}">末页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 切换用户状态
    $('.toggle-status').click(function() {
        const userId = $(this).data('user-id');
        const currentStatus = $(this).data('current-status');
        const button = $(this);
        
        if (confirm(currentStatus ? '确定要禁用此用户吗？' : '确定要激活此用户吗？')) {
            $.post('{% url "users:toggle_status" 0 %}'.replace('0', userId), {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .fail(function() {
                alert('操作失败，请重试');
            });
        }
    });
});
</script>
{% endblock %}
