from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _

class User(AbstractUser):
    """自定义用户模型"""
    avatar = models.ImageField(upload_to='avatars/', default='avatars/default.png', verbose_name=_('头像'))
    phone = models.CharField(max_length=11, blank=True, verbose_name=_('手机号'), db_index=True)
    is_vip = models.BooleanField(default=False, verbose_name=_('是否VIP'))
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('账户余额'))
    
    class Meta:
        verbose_name = _('用户')
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['username', 'phone']),
            models.Index(fields=['is_vip']),
        ]
        
    def __str__(self):
        return self.username

class Address(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='addresses', verbose_name='用户')
    receiver = models.CharField(max_length=50, verbose_name='收货人')
    phone = models.CharField(max_length=11, verbose_name='手机号码')
    province = models.CharField(max_length=50, verbose_name='省份')
    city = models.CharField(max_length=50, verbose_name='城市')
    district = models.CharField(max_length=50, verbose_name='区县')
    address = models.CharField(max_length=200, verbose_name='详细地址')
    is_default = models.BooleanField(default=False, verbose_name='是否默认')
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_time = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '收货地址'
        verbose_name_plural = verbose_name
        ordering = ['-is_default', '-updated_time']

    def __str__(self):
        return f'{self.receiver} {self.phone}'

    def save(self, *args, **kwargs):
        if self.is_default:
            # 将其他地址设置为非默认
            Address.objects.filter(user=self.user, is_default=True).update(is_default=False)
        elif not self.is_default and not Address.objects.filter(user=self.user, is_default=True).exists():
            # 如果没有默认地址，将当前地址设为默认
            self.is_default = True
        super().save(*args, **kwargs)
