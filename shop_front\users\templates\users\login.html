{% extends 'base.html' %}
{% load static %}

{% block title %}登录 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 红色主题样式 */
    .login-container {
        max-width: 450px;
        margin: 40px auto;
        padding: 30px;
        background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .login-container:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    .login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .login-header h2 {
        font-size: 28px;
        color: #dc3545;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .login-header p {
        color: #666;
        font-size: 14px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        height: 45px;
        padding: 12px 16px;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #dc3545;
        outline: none;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    }

    .btn-login {
        width: 100%;
        height: 50px;
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    }
    
    .btn-login:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s;
    }
    
    .btn-login:hover:before {
        left: 100%;
    }

    .btn-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    }

    .login-footer {
        text-align: center;
        margin-top: 20px;
        color: #666;
    }

    .login-footer a {
        color: #dc3545;
        text-decoration: none;
        font-weight: 500;
    }

    .login-footer a:hover {
        text-decoration: underline;
    }

    .error-message {
        color: #dc3545;
        margin-bottom: 15px;
        padding: 12px;
        background: #fdecea;
        border-radius: 8px;
        border-left: 4px solid #dc3545;
    }

    /* 记住我选项 */
    .remember-me {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .remember-me input {
        margin-right: 8px;
    }

    .remember-me label {
        color: #666;
        font-size: 14px;
        cursor: pointer;
    }

    /* 社交登录按钮 */
    .social-login {
        margin-top: 25px;
        text-align: center;
    }

    .social-login p {
        color: #666;
        margin-bottom: 15px;
        position: relative;
    }

    .social-login p:before,
    .social-login p:after {
        content: '';
        position: absolute;
        top: 50%;
        width: 30%;
        height: 1px;
        background: #ddd;
    }

    .social-login p:before {
        left: 0;
    }

    .social-login p:after {
        right: 0;
    }

    .social-buttons {
        display: flex;
        justify-content: center;
        gap: 15px;
    }

    .social-button {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 18px;
        transition: all 0.3s;
    }

    .social-button:hover {
        transform: translateY(-3px);
    }

    .social-wechat {
        background: #07C160;
        box-shadow: 0 4px 10px rgba(7, 193, 96, 0.3);
    }

    .social-qq {
        background: #12B7F5;
        box-shadow: 0 4px 10px rgba(18, 183, 245, 0.3);
    }

    .social-weibo {
        background: #E6162D;
        box-shadow: 0 4px 10px rgba(230, 22, 45, 0.3);
    }

    /* 用户类型选择样式 */
    .user-type-selection {
        display: flex;
        gap: 15px;
        margin-top: 8px;
    }

    .radio-group {
        flex: 1;
        position: relative;
    }

    .radio-group input[type="radio"] {
        display: none;
    }

    .radio-label {
        display: block;
        padding: 15px 12px;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
        position: relative;
        overflow: hidden;
    }

    .radio-label:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, transparent, transparent);
        transition: all 0.3s ease;
    }

    .radio-label i {
        font-size: 24px;
        margin-bottom: 8px;
        display: block;
        transition: all 0.3s ease;
    }

    .radio-label span {
        display: block;
        font-weight: 600;
        font-size: 14px;
        margin-bottom: 4px;
        transition: all 0.3s ease;
    }

    .radio-label small {
        display: block;
        font-size: 11px;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .radio-label:hover {
        border-color: #dc3545;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.1);
    }

    /* 普通用户选中状态 */
    .radio-group input[type="radio"]:checked + .radio-label {
        border-color: #dc3545;
        background: linear-gradient(135deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.02) 100%);
        box-shadow: 0 5px 20px rgba(220, 53, 69, 0.15);
    }

    .radio-group input[type="radio"]:checked + .radio-label:before {
        background: linear-gradient(90deg, #dc3545, #ff6b6b);
    }

    .radio-group input[type="radio"]:checked + .radio-label i {
        color: #dc3545;
        transform: scale(1.1);
    }

    .radio-group input[type="radio"]:checked + .radio-label span {
        color: #dc3545;
    }

    /* 管理员特殊样式 */
    #user_type_admin:checked + .radio-label {
        border-color: #28a745;
        background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.02) 100%);
        box-shadow: 0 5px 20px rgba(40, 167, 69, 0.15);
    }

    #user_type_admin:checked + .radio-label:before {
        background: linear-gradient(90deg, #28a745, #20c997);
    }

    #user_type_admin:checked + .radio-label i {
        color: #28a745;
    }

    #user_type_admin:checked + .radio-label span {
        color: #28a745;
    }

    /* 响应式调整 */
    @media (max-width: 576px) {
        .user-type-selection {
            flex-direction: column;
            gap: 10px;
        }

        .radio-label {
            padding: 12px 10px;
        }

        .radio-label i {
            font-size: 20px;
            margin-bottom: 6px;
        }

        .radio-label span {
            font-size: 13px;
        }

        .radio-label small {
            font-size: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="login-container">
        <div class="login-header">
            <h2>
                <i class="fas fa-user-circle"></i>
                用户登录
            </h2>
            <p>欢迎回来，请登录您的账号</p>
        </div>

        {% if messages %}
        {% for message in messages %}
        <div class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            {{ message }}
        </div>
        {% endfor %}
        {% endif %}
        
        {% if form.errors %}
        <div class="error-message">
            <i class="fas fa-exclamation-circle"></i>
            用户名或密码错误，请重试
        </div>
        {% endif %}

        <form method="post" action="{% url 'users:login' %}">
            {% csrf_token %}
            
            {% if request.GET.next %}
            <input type="hidden" name="next" value="{{ request.GET.next }}">
            {% endif %}
            
            <div class="form-group">
                <label for="id_username">
                    <i class="fas fa-user"></i> 用户名
                </label>
                {{ form.username }}
            </div>
            
            <div class="form-group">
                <label for="id_password">
                    <i class="fas fa-lock"></i> 密码
                </label>
                {{ form.password }}
            </div>

            <div class="form-group">
                <label for="user_type">
                    <i class="fas fa-user-tag"></i> 登录类型
                </label>
                <div class="user-type-selection">
                    <div class="radio-group">
                        <input type="radio" id="user_type_normal" name="user_type" value="normal" checked>
                        <label for="user_type_normal" class="radio-label">
                            <i class="fas fa-user"></i>
                            <span>普通用户</span>
                            <small>购物、浏览商品</small>
                        </label>
                    </div>
                    <div class="radio-group">
                        <input type="radio" id="user_type_admin" name="user_type" value="admin">
                        <label for="user_type_admin" class="radio-label">
                            <i class="fas fa-user-shield"></i>
                            <span>管理员</span>
                            <small>后台管理、商品管理</small>
                        </label>
                    </div>
                </div>
            </div>

            <div class="remember-me">
                <input type="checkbox" id="remember-me" name="remember_me">
                <label for="remember-me">记住我</label>
            </div>
            
            <button type="submit" class="btn-login">
                <i class="fas fa-sign-in-alt"></i> 登录
            </button>
        </form>
        
        <div class="login-footer">
            <p>还没有账号？ <a href="{% url 'users:register' %}">立即注册</a></p>
            <p><a href="#">忘记密码？</a></p>
        </div>
        
        <div class="social-login">
            <p>其他登录方式</p>
            <div class="social-buttons">
                <a href="#" class="social-button social-wechat" title="微信登录">
                    <i class="fab fa-weixin"></i>
                </a>
                <a href="#" class="social-button social-qq" title="QQ登录">
                    <i class="fab fa-qq"></i>
                </a>
                <a href="#" class="social-button social-weibo" title="微博登录">
                    <i class="fab fa-weibo"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const userTypeRadios = document.querySelectorAll('input[name="user_type"]');
    const loginButton = document.querySelector('.btn-login');
    const loginForm = document.querySelector('form');

    // 监听用户类型选择变化
    userTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            updateLoginButton();
        });
    });

    function updateLoginButton() {
        const selectedType = document.querySelector('input[name="user_type"]:checked').value;
        const buttonIcon = loginButton.querySelector('i');
        const buttonText = loginButton.querySelector('span') || loginButton;

        if (selectedType === 'admin') {
            buttonIcon.className = 'fas fa-user-shield';
            if (buttonText.tagName === 'SPAN') {
                buttonText.textContent = ' 管理员登录';
            } else {
                loginButton.innerHTML = '<i class="fas fa-user-shield"></i> 管理员登录';
            }
            loginButton.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
            loginButton.style.borderColor = '#28a745';
        } else {
            buttonIcon.className = 'fas fa-sign-in-alt';
            if (buttonText.tagName === 'SPAN') {
                buttonText.textContent = ' 登录';
            } else {
                loginButton.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录';
            }
            loginButton.style.background = 'linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%)';
            loginButton.style.borderColor = '#dc3545';
        }
    }

    // 表单提交时的额外验证和提示
    loginForm.addEventListener('submit', function(e) {
        const selectedType = document.querySelector('input[name="user_type"]:checked').value;

        if (selectedType === 'admin') {
            // 管理员登录提示
            const confirmAdmin = confirm('您选择了管理员登录，登录成功后将跳转到后台管理系统。确认继续吗？');
            if (!confirmAdmin) {
                e.preventDefault();
                return false;
            }
        }
    });

    // 初始化按钮状态
    updateLoginButton();
});
</script>
{% endblock %}