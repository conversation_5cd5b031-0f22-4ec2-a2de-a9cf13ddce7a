<!DOCTYPE html>
<html>
<head>
    <title>调试按钮功能</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .button { padding: 10px 15px; margin: 5px; cursor: pointer; border: none; border-radius: 5px; }
        .favorite-btn { background: #ff6b6b; color: white; }
        .cart-btn { background: #4ecdc4; color: white; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>🔧 按钮功能调试工具</h1>
    
    <div class="test-section">
        <h2>测试按钮</h2>
        <button class="button favorite-btn" data-product-id="1" onclick="testFavorite(this)">
            ❤️ 测试收藏按钮
        </button>
        <button class="button cart-btn" data-product-id="1" onclick="testCart(this)">
            🛒 测试购物车按钮
        </button>
    </div>
    
    <div class="test-section">
        <h2>调试信息</h2>
        <div id="log" class="log">点击按钮查看调试信息...</div>
    </div>
    
    <div class="test-section">
        <h2>检查清单</h2>
        <ul>
            <li>✅ 按钮是否有 data-product-id 属性</li>
            <li>✅ 按钮是否有正确的 onclick 事件</li>
            <li>✅ JavaScript函数是否存在</li>
            <li>✅ CSRF令牌是否存在</li>
            <li>✅ 用户是否已登录</li>
        </ul>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testFavorite(button) {
            log('🔍 测试收藏按钮...');
            
            // 检查按钮属性
            const productId = button.getAttribute('data-product-id');
            log(`商品ID: ${productId}`);
            
            if (!productId) {
                log('❌ 错误: 缺少 data-product-id 属性');
                return;
            }
            
            // 检查CSRF令牌
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                log('✅ CSRF令牌存在');
            } else {
                log('❌ 警告: 缺少CSRF令牌');
            }
            
            // 模拟API调用
            log('📡 模拟收藏API调用...');
            setTimeout(() => {
                log('✅ 收藏功能测试完成');
            }, 1000);
        }

        function testCart(button) {
            log('🔍 测试购物车按钮...');
            
            // 检查按钮属性
            const productId = button.getAttribute('data-product-id');
            log(`商品ID: ${productId}`);
            
            if (!productId) {
                log('❌ 错误: 缺少 data-product-id 属性');
                return;
            }
            
            // 检查CSRF令牌
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                log('✅ CSRF令牌存在');
            } else {
                log('❌ 警告: 缺少CSRF令牌');
            }
            
            // 模拟API调用
            log('📡 模拟购物车API调用...');
            setTimeout(() => {
                log('✅ 购物车功能测试完成');
            }, 1000);
        }

        // 页面加载时的检查
        window.onload = function() {
            log('🚀 页面加载完成，开始检查...');
            
            // 检查是否有toggleFavorite函数
            if (typeof toggleFavorite === 'function') {
                log('✅ toggleFavorite 函数存在');
            } else {
                log('❌ toggleFavorite 函数不存在');
            }
            
            // 检查是否有addToCart函数
            if (typeof addToCart === 'function') {
                log('✅ addToCart 函数存在');
            } else {
                log('❌ addToCart 函数不存在');
            }
            
            // 检查是否有首页专用函数
            if (typeof addToFavoriteFromHome === 'function') {
                log('✅ addToFavoriteFromHome 函数存在');
            } else {
                log('⚠️ addToFavoriteFromHome 函数不存在（首页专用）');
            }
            
            if (typeof addToCartFromHome === 'function') {
                log('✅ addToCartFromHome 函数存在');
            } else {
                log('⚠️ addToCartFromHome 函数不存在（首页专用）');
            }
            
            log('🔧 检查完成，可以开始测试按钮功能');
        };
    </script>
</body>
</html>
