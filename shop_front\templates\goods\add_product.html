{% extends 'base.html' %}

{% block title %}添加商品 - 在线商城{% endblock %}

{% load static %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h1>添加新商品</h1>
    </div>
    
    <div class="add-product-form">
        <form method="post" enctype="multipart/form-data" action="{% url 'goods:add_product' %}">
            {% csrf_token %}
            
            <div class="form-group">
                <label for="name">商品名称</label>
                <input type="text" id="name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="category">商品分类</label>
                <select id="category" name="category" required>
                    <option value="">请选择分类</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}">{{ category.name }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="form-group">
                <label for="price">价格</label>
                <input type="number" id="price" name="price" step="0.01" required>
            </div>
            
            <div class="form-group">
                <label for="stock">库存</label>
                <input type="number" id="stock" name="stock" value="0">
            </div>
            
            <div class="form-group">
                <label for="main_image">主图</label>
                <input type="file" id="main_image" name="main_image" accept="image/*">
                <div id="imagePreview"></div>
            </div>
            
            <div class="form-group">
                <label for="product_images">商品图片</label>
                <input type="file" id="product_images" name="product_images" accept="image/*" multiple>
                <div id="multipleImagePreview"></div>
            </div>
            
            <div class="form-group">
                <label for="description">商品描述</label>
                <textarea id="description" name="description" rows="6"></textarea>
            </div>
            
            <div class="form-group">
                <label class="checkbox-label">
                    <input type="checkbox" id="is_hot" name="is_hot">
                    热门商品
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="is_new" name="is_new" checked>
                    新品上市
                </label>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="save-btn">添加商品</button>
                <a href="{% url 'goods:index' %}" class="cancel-btn">取消</a>
            </div>
        </form>
    </div>
</div>

<style>
.page-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.add-product-form {
    max-width: 800px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.checkbox-label {
    display: inline-block;
    margin-right: 20px;
}

.form-actions {
    margin-top: 30px;
    display: flex;
    gap: 15px;
}

.save-btn {
    background: #e4393c;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.cancel-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 16px;
    display: inline-block;
}

#imagePreview, #multipleImagePreview {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

#imagePreview img {
    max-width: 200px;
    max-height: 200px;
    border: 1px solid #ddd;
}

#multipleImagePreview .image-preview {
    width: 150px;
    height: 150px;
    border: 1px solid #ddd;
    position: relative;
}

#multipleImagePreview .image-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

#multipleImagePreview .image-preview .remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
</style>

<script>
document.getElementById('main_image').onchange = function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('imagePreview').innerHTML = 
                `<img src="${e.target.result}" alt="图片预览">`;
        }
        reader.readAsDataURL(file);
    }
}

document.getElementById('product_images').onchange = function(e) {
    const files = e.target.files;
    const preview = document.getElementById('multipleImagePreview');
    preview.innerHTML = '';
    
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const div = document.createElement('div');
            div.className = 'image-preview';
            div.innerHTML = `
                <img src="${e.target.result}" alt="图片预览">
                <span class="remove-btn">&times;</span>
            `;
            preview.appendChild(div);
            
            // 添加删除按钮事件
            div.querySelector('.remove-btn').addEventListener('click', function() {
                div.remove();
            });
        }
        
        reader.readAsDataURL(file);
    }
}
</script>
{% endblock %} 