from django.shortcuts import render, redirect
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.conf import settings
from .forms import UserRegisterForm, UserLoginForm, UserProfileForm, PasswordChangeForm
from order.models import Order, OrderItem
from .models import Address
from .serializers import AddressSerializer
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import SessionAuthentication, BasicAuthentication
import json
import os
import uuid
from PIL import Image

# Create your views here.

def register(request):
    """用户注册"""
    if request.method == 'POST':
        form = UserRegisterForm(request.POST, request.FILES)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, '注册成功！')
            return redirect('home:index')
    else:
        form = UserRegisterForm()
    return render(request, 'users/register.html', {'form': form})

def user_login(request):
    """用户登录（支持普通用户和管理员）"""
    if request.method == 'POST':
        form = UserLoginForm(data=request.POST)
        user_type = request.POST.get('user_type', 'normal')  # 获取用户选择的登录类型

        if form.is_valid():
            user = form.get_user()

            # 根据选择的登录类型进行权限验证
            if user_type == 'admin':
                # 管理员登录
                if not (user.is_staff or user.is_superuser):
                    messages.error(request, '您没有管理员权限！请选择普通用户登录。')
                    return render(request, 'users/login.html', {'form': form})

                # 管理员登录成功，跳转到前台首页
                login(request, user)
                messages.success(request, '管理员登录成功！')
                return redirect('home:index')

            else:
                # 普通用户登录
                if user.is_staff or user.is_superuser:
                    messages.error(request, '管理员账号请选择"管理员"登录类型！')
                    return render(request, 'users/login.html', {'form': form})

                # 普通用户登录成功
                login(request, user)
                messages.success(request, '登录成功！')
                next_url = request.GET.get('next', 'home:index')
                return redirect(next_url)
    else:
        form = UserLoginForm()
    return render(request, 'users/login.html', {'form': form})

def admin_login(request):
    """前台管理员登录"""
    if request.method == 'POST':
        form = UserLoginForm(data=request.POST)
        if form.is_valid():
            user = form.get_user()

            # 检查用户权限 - 只允许管理员登录
            if not (user.is_staff or user.is_superuser):
                messages.error(request, '此登录入口仅限管理员使用！')
                return render(request, 'users/admin_login.html', {'form': form})

            login(request, user)
            messages.success(request, '管理员登录成功！')
            # 管理员登录后跳转到前台首页
            return redirect('home:index')
    else:
        form = UserLoginForm()
    return render(request, 'users/admin_login.html', {'form': form})

@login_required
def user_logout(request):
    """用户退出"""
    logout(request)
    messages.success(request, '已安全退出！')
    return redirect('home:index')

@login_required
def profile(request):
    """个人资料"""
    if request.method == 'POST':
        form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, '个人资料更新成功！')
            return redirect('users:profile')
    else:
        form = UserProfileForm(instance=request.user)
    return render(request, 'users/profile.html', {'form': form})

@login_required
def user_center(request):
    """用户中心"""
    try:
        # 获取用户最近的订单
        recent_orders = Order.objects.filter(user=request.user).order_by('-created_time')[:5]
        
        # 获取各种状态的订单数量
        pending_orders_count = Order.objects.filter(user=request.user, status='pending').count()
        shipping_orders_count = Order.objects.filter(user=request.user, status='shipped').count()
        # 获取已收货但未评价的订单项数量
        review_orders_count = OrderItem.objects.filter(
            order__user=request.user,
            order__status='received',
            is_reviewed=False
        ).count()
        
    except Exception as e:
        # 如果出现异常（比如字段不存在），设置默认值
        recent_orders = []
        pending_orders_count = 0
        shipping_orders_count = 0
        review_orders_count = 0
        print(f"Error fetching orders: {str(e)}")
    
    # 获取收货地址数量
    address_count = Address.objects.filter(user=request.user).count()
    
    context = {
        'recent_orders': recent_orders,
        'user': request.user,
        'pending_orders_count': pending_orders_count,
        'shipping_orders_count': shipping_orders_count,
        'review_orders_count': review_orders_count,
        'address_count': address_count
    }
    return render(request, 'users/center.html', context)

@login_required
def user_orders(request):
    """用户订单列表"""
    orders = Order.objects.filter(user=request.user).order_by('-created_time')
    context = {
        'orders': orders
    }
    return render(request, 'users/orders.html', context)

@login_required
def membership(request):
    """会员中心"""
    return render(request, 'users/membership.html')

@login_required
def user_address(request):
    """用户地址管理"""
    addresses = Address.objects.filter(user=request.user).order_by('-is_default', '-id')
    context = {
        'addresses': addresses,
        'user': request.user
    }
    return render(request, 'users/address.html', context)

@login_required
def change_password(request):
    """修改密码"""
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            # 更新密码
            new_password = form.cleaned_data['new_password']
            request.user.set_password(new_password)
            request.user.save()
            
            # 发送密码修改成功的消息
            messages.success(request, '密码修改成功，请重新登录')
            
            # 退出登录
            logout(request)
            
            # 重定向到登录页面
            return redirect('users:login')
    else:
        form = PasswordChangeForm(request.user)
    
    return render(request, 'users/password.html', {'form': form})

@login_required
@csrf_exempt
def upload_avatar(request):
    """用户头像上传"""
    if request.method == 'POST':
        try:
            uploaded_file = request.FILES.get('avatar')
            if not uploaded_file:
                return JsonResponse({'success': False, 'msg': '请选择图片文件'})

            # 验证文件
            is_valid, error_msg = validate_image_file(uploaded_file)
            if not is_valid:
                return JsonResponse({'success': False, 'msg': error_msg})

            # 处理上传
            saved_path = handle_uploaded_image(uploaded_file, 'avatars/')
            if saved_path:
                # 更新用户头像
                request.user.avatar = saved_path
                request.user.save()

                return JsonResponse({
                    'success': True,
                    'msg': '头像上传成功',
                    'avatar_url': f"{settings.MEDIA_URL}{saved_path}"
                })
            else:
                return JsonResponse({'success': False, 'msg': '文件上传失败'})

        except Exception as e:
            return JsonResponse({'success': False, 'msg': f'上传错误: {str(e)}'})

    return JsonResponse({'success': False, 'msg': '请求方法错误'})

def validate_image_file(uploaded_file):
    """验证上传的图片文件"""
    try:
        # 检查文件大小
        max_size = 5 * 1024 * 1024  # 5MB
        if uploaded_file.size > max_size:
            return False, f"文件大小超过限制 (5MB)"

        # 检查文件扩展名
        file_name = uploaded_file.name
        file_ext = os.path.splitext(file_name)[1].lower()
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']

        if file_ext not in allowed_extensions:
            return False, f"不支持的文件格式，支持的格式: {', '.join(allowed_extensions)}"

        # 尝试打开图片验证格式
        try:
            uploaded_file.seek(0)
            with Image.open(uploaded_file) as img:
                img.verify()
            uploaded_file.seek(0)
        except Exception:
            return False, "文件不是有效的图片格式"

        return True, ""

    except Exception as e:
        return False, f"文件验证错误: {str(e)}"

def handle_uploaded_image(uploaded_file, upload_path='uploads/'):
    """处理上传的图片文件"""
    try:
        # 获取文件扩展名
        file_name = uploaded_file.name
        file_ext = os.path.splitext(file_name)[1].lower()

        # 检查文件扩展名
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        if file_ext not in allowed_extensions:
            file_ext = '.jpg'  # 默认扩展名

        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}{file_ext}"
        file_path = os.path.join(upload_path, unique_filename)

        # 保存文件
        saved_path = default_storage.save(file_path, uploaded_file)

        # 如果是图片，进行压缩处理
        if file_ext in ['.jpg', '.jpeg', '.png']:
            compress_image(saved_path)

        return saved_path

    except Exception as e:
        print(f"文件上传处理错误: {e}")
        return None

def compress_image(image_path, max_size=(400, 400), quality=85):
    """压缩图片"""
    try:
        full_path = os.path.join(settings.MEDIA_ROOT, image_path)

        if not os.path.exists(full_path):
            return

        with Image.open(full_path) as img:
            # 转换为RGB模式（如果是RGBA）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')

            # 计算新尺寸
            img.thumbnail(max_size, Image.Resampling.LANCZOS)

            # 保存压缩后的图片
            img.save(full_path, 'JPEG', quality=quality, optimize=True)

    except Exception as e:
        print(f"图片压缩错误: {e}")

@login_required
def image_upload_page(request):
    """图片上传页面"""
    return render(request, 'users/image_upload.html')

@login_required
@csrf_exempt
def upload_general_image(request):
    """通用图片上传接口"""
    if request.method == 'POST':
        try:
            uploaded_file = request.FILES.get('image')
            upload_type = request.POST.get('type', 'general')  # 上传类型

            if not uploaded_file:
                return JsonResponse({'success': False, 'msg': '请选择图片文件'})

            # 验证文件
            is_valid, error_msg = validate_image_file(uploaded_file)
            if not is_valid:
                return JsonResponse({'success': False, 'msg': error_msg})

            # 根据类型选择上传路径
            upload_paths = {
                'avatar': 'avatars/',
                'product': 'user_products/',
                'general': 'user_uploads/',
                'review': 'reviews/'
            }
            upload_path = upload_paths.get(upload_type, 'user_uploads/')

            # 处理上传
            saved_path = handle_uploaded_image(uploaded_file, upload_path)
            if saved_path:
                return JsonResponse({
                    'success': True,
                    'msg': '图片上传成功',
                    'image_url': f"{settings.MEDIA_URL}{saved_path}",
                    'image_path': saved_path
                })
            else:
                return JsonResponse({'success': False, 'msg': '文件上传失败'})

        except Exception as e:
            return JsonResponse({'success': False, 'msg': f'上传错误: {str(e)}'})

    return JsonResponse({'success': False, 'msg': '请求方法错误'})

class AddressViewSet(viewsets.ModelViewSet):
    """收货地址管理API"""
    serializer_class = AddressSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [SessionAuthentication, BasicAuthentication]

    def get_queryset(self):
        return Address.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def create(self, request, *args, **kwargs):
        # 添加调试日志
        print(f"收到地址创建请求，数据: {request.data}")

        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            self.perform_create(serializer)
            return Response({
                'success': True,
                'message': '地址添加成功',
                'data': serializer.data
            })

        # 添加详细的错误日志
        print(f"地址验证失败，错误: {serializer.errors}")
        return Response({
            'success': False,
            'message': '数据验证失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        if serializer.is_valid():
            self.perform_update(serializer)
            return Response({
                'success': True,
                'message': '地址更新成功',
                'data': serializer.data
            })
        return Response({
            'success': False,
            'message': '数据验证失败',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response({
            'success': True,
            'message': '地址删除成功'
        })

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """设置默认地址"""
        address = self.get_object()
        # 将其他地址设置为非默认
        Address.objects.filter(user=request.user, is_default=True).update(is_default=False)
        # 设置当前地址为默认
        address.is_default = True
        address.save()
        return Response({
            'success': True,
            'message': '默认地址设置成功'
        })