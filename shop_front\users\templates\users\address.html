{% extends 'base.html' %}
{% load static %}

{% block title %}收货地址管理 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 修改为红色主题样式 */
    .profile-container {
        max-width: 1200px;
        margin: 40px auto;
        display: flex;
        gap: 20px;
    }

    .sidebar {
        width: 250px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.1);
        padding: 20px;
        border: 1px solid rgba(220, 53, 69, 0.1);
    }

    .user-avatar {
        text-align: center;
        margin-bottom: 20px;
    }

    .user-avatar img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #dc3545;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
    }

    .user-name {
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 10px;
    }

    .sidebar-menu a {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #666;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        transform: translateY(-2px);
    }

    .sidebar-menu a.active {
        background: #dc3545;
        color: white;
    }

    .sidebar-menu i {
        margin-right: 10px;
        width: 20px;
        font-size: 16px;
        text-align: center;
    }

    .main-content {
        flex: 1;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 30px;
        position: relative;
        overflow: hidden;
    }

    .main-content:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }

    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .profile-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .profile-header h2 {
        font-size: 24px;
        color: #dc3545;
        margin-bottom: 0;
        font-weight: 700;
        display: flex;
        align-items: center;
    }

    .profile-header h2 i {
        margin-right: 10px;
        color: #dc3545;
        font-size: 24px;
    }

    .btn-add {
        background: linear-gradient(135deg, #28a745 0%, #5cb85c 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-add:hover {
        background: linear-gradient(135deg, #218838 0%, #28a745 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(40, 167, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    /* 地址卡片样式 */
    .address-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 30px;
    }

    .address-card {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        position: relative;
        transition: all 0.3s;
        border: 1px solid #eee;
        box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    }

    .address-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    }

    .address-card.default {
        border: 2px solid #dc3545;
    }

    .default-badge {
        position: absolute;
        top: -10px;
        right: 20px;
        background: #dc3545;
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }

    .address-content {
        margin-bottom: 15px;
    }

    .address-name {
        font-weight: 600;
        font-size: 18px;
        margin-bottom: 10px;
        color: #333;
    }

    .address-phone {
        color: #666;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 5px;
    }

    .address-detail {
        color: #666;
        line-height: 1.5;
    }

    .address-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 15px;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }

    .btn-edit {
        background: #f8f9fa;
        color: #495057;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 14px;
        transition: all 0.3s;
    }

    .btn-edit:hover {
        background: #e9ecef;
        color: #212529;
    }

    .btn-delete {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 14px;
        transition: all 0.3s;
    }

    .btn-delete:hover {
        background: #f1aeb5;
        color: #58151c;
    }

    .btn-default {
        background: #e2f3e5;
        color: #155724;
        border: 1px solid #c3e6cb;
        border-radius: 6px;
        padding: 6px 12px;
        font-size: 14px;
        transition: all 0.3s;
    }

    .btn-default:hover {
        background: #c3e6cb;
        color: #0b2e13;
    }

    .empty-address {
        text-align: center;
        padding: 50px 0;
    }

    .empty-address i {
        font-size: 60px;
        color: #dc3545;
        opacity: 0.5;
        margin-bottom: 20px;
    }

    .empty-address p {
        color: #666;
        margin-bottom: 20px;
}

/* 确认对话框样式 */
.modal-confirm {
    text-align: center;
    padding: 30px;
    background: #fff;
    border-radius: 12px;
}

.modal-confirm .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.confirm-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    border-radius: 50%;
    border: 3px solid #ff4444;
    background: #fff;
    position: relative;
    animation: iconShake 0.5s ease-in-out;
}

.confirm-icon i {
    font-size: 40px;
    color: #ff4444;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.confirm-title {
    color: #333;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
}

.confirm-message {
    color: #666;
    font-size: 16px;
    margin-bottom: 30px;
    padding: 0 20px;
}

.confirm-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.confirm-buttons button {
    min-width: 120px;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.confirm-buttons .btn-cancel {
    background: #f8f9fa;
    border: 1px solid #ddd;
    color: #666;
}

.confirm-buttons .btn-cancel:hover {
    background: #e9ecef;
    border-color: #c8c9ca;
}

.confirm-buttons .btn-delete {
    background: #ff4444;
    border: none;
    color: white;
}

.confirm-buttons .btn-delete:hover {
    background: #ff1111;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(255, 68, 68, 0.3);
}

@keyframes iconShake {
    0%, 100% { transform: rotate(0); }
    20%, 60% { transform: rotate(-10deg); }
    40%, 80% { transform: rotate(10deg); }
}

/* 消息提示样式 */
.message-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    width: 100%;
    max-width: 400px;
    text-align: center;
    pointer-events: none;
}

.message {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin: 10px;
    padding: 16px 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: messageSlideIn 0.3s ease-out;
    pointer-events: auto;
}

.message.success {
    border-left: 4px solid #28a745;
}

.message.error {
    border-left: 4px solid #dc3545;
}

.message.info {
    border-left: 4px solid #17a2b8;
}

.message.warning {
    border-left: 4px solid #ffc107;
}

.message i {
    margin-right: 12px;
    font-size: 20px;
}

.message.success i {
    color: #28a745;
}

.message.error i {
    color: #dc3545;
}

.message.info i {
    color: #17a2b8;
}

.message.warning i {
    color: #ffc107;
}

@keyframes messageSlideIn {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes messageSlideOut {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-20px);
        opacity: 0;
    }
}

    /* 响应式设计 */
    @media (max-width: 992px) {
        .profile-container {
            flex-direction: column;
        }

        .sidebar {
            width: 100%;
            margin-bottom: 20px;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- 消息提示容器 -->
{% if messages %}
<div class="message-container">
    {% for message in messages %}
    <div class="message {{ message.tags }}">
        {% if message.tags == 'success' %}
        <i class="fas fa-check-circle"></i>
        {% elif message.tags == 'error' %}
        <i class="fas fa-times-circle"></i>
        {% elif message.tags == 'info' %}
        <i class="fas fa-info-circle"></i>
        {% elif message.tags == 'warning' %}
        <i class="fas fa-exclamation-circle"></i>
        {% endif %}
        {{ message }}
    </div>
    {% endfor %}
</div>
{% endif %}

<div class="container py-5">
    <div class="profile-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-avatar">
                            <img src="{{ user.avatar.url }}" alt="{{ user.username }}" onerror="this.src='/media/avatars/default.png'">
                    </div>
            <div class="user-name">{{ user.username }}</div>
            <ul class="sidebar-menu">
                <li>
                    <a href="{% url 'users:center' %}">
                            <i class="fas fa-home"></i> 个人中心
                        </a>
                </li>
                <li>
                    <a href="{% url 'users:profile' %}">
                            <i class="fas fa-user"></i> 个人资料
                        </a>
                </li>
                <li>
                    <a href="{% url 'users:orders' %}">
                        <i class="fas fa-shopping-bag"></i> 我的订单
                    </a>
                </li>
                <li>
                    <a href="{% url 'reviews:my_reviews' %}">
                        <i class="fas fa-star"></i> 我的评价
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:address' %}" class="active">
                            <i class="fas fa-map-marker-alt"></i> 收货地址
                        </a>
                </li>
                <li>
                    <a href="javascript:void(0)" onclick="confirmLogout()" class="text-danger">
                        <i class="fas fa-sign-out-alt"></i> 注销账户
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:password' %}">
                            <i class="fas fa-lock"></i> 修改密码
                        </a>
                </li>
            </ul>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="profile-header">
                <h2><i class="fas fa-map-marker-alt"></i> 收货地址</h2>
                <button id="addAddressBtn" class="btn-add">
                    <i class="fas fa-plus"></i> 添加新地址
                        </button>
                    </div>

            {% if addresses %}
                    <div class="address-list">
                        {% for address in addresses %}
                <div class="address-card {% if address.is_default %}default{% endif %}" data-id="{{ address.id }}">
                            {% if address.is_default %}
                    <div class="default-badge">默认地址</div>
                            {% endif %}
                            <div class="address-content">
                        <div class="address-name">{{ address.receiver }}</div>
                        <div class="address-phone">
                            <i class="fas fa-phone"></i> {{ address.phone }}
                        </div>
                        <div class="address-detail">
                            {{ address.province }} {{ address.city }} {{ address.district }} {{ address.address }}
                        </div>
                    </div>
                    <div class="address-actions">
                        {% if not address.is_default %}
                        <button class="btn-default set-default-btn" data-id="{{ address.id }}">设为默认</button>
                        {% endif %}
                        <button class="btn-edit edit-address-btn" data-id="{{ address.id }}">编辑</button>
                        <button class="btn-delete delete-address-btn" data-id="{{ address.id }}">删除</button>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-address">
                <i class="fas fa-map-marker-alt"></i>
                <p>您还没有添加任何收货地址</p>
                <button id="emptyAddBtn" class="btn-add">
                    <i class="fas fa-plus"></i> 添加新地址
                </button>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- 地址表单模态框 -->
<div class="modal fade" id="addressModal" tabindex="-1" role="dialog" aria-labelledby="addressModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addressModalLabel">添加收货地址</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addressForm">
                    <input type="hidden" id="addressId" value="">
                    <div class="form-group">
                        <label for="receiver">收货人</label>
                        <input type="text" class="form-control" id="receiver" name="receiver" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">手机号码</label>
                        <input type="tel" class="form-control" id="phone" name="phone" required>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label for="province">省份</label>
                            <input type="text" class="form-control" id="province" name="province" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="city">城市</label>
                            <input type="text" class="form-control" id="city" name="city" required>
                        </div>
                        <div class="form-group col-md-4">
                            <label for="district">区/县</label>
                            <input type="text" class="form-control" id="district" name="district" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="detail">详细地址</label>
                        <textarea class="form-control" id="detail" name="detail" rows="3" required></textarea>
                    </div>
                    <div class="form-group form-check">
                        <input type="checkbox" class="form-check-input" id="isDefault" name="is_default">
                        <label class="form-check-label" for="isDefault">设为默认地址</label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveAddressBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content modal-confirm">
                <div class="confirm-icon">
                <i class="fas fa-trash-alt"></i>
                </div>
            <div class="confirm-title">确认删除</div>
            <div class="confirm-message">您确定要删除这个收货地址吗？此操作无法撤销。</div>
                <div class="confirm-buttons">
                <button type="button" class="btn-cancel" data-dismiss="modal">取消</button>
                <button type="button" class="btn-delete" id="confirmDeleteBtn">删除</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 自动隐藏消息
        const messages = document.querySelectorAll('.message');
        messages.forEach(message => {
            setTimeout(() => {
                message.style.animation = 'messageSlideOut 0.3s ease-out forwards';
                setTimeout(() => {
                    message.remove();
                }, 300);
            }, 3000);
        });

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
        const csrfToken = getCookie('csrftoken');

        // 添加地址按钮点击事件
        document.getElementById('addAddressBtn').addEventListener('click', function() {
            // 重置表单
            document.getElementById('addressForm').reset();
            document.getElementById('addressId').value = '';
            document.getElementById('addressModalLabel').textContent = '添加收货地址';
            $('#addressModal').modal('show');
        });

        // 空地址时的添加按钮
        const emptyAddBtn = document.getElementById('emptyAddBtn');
        if (emptyAddBtn) {
            emptyAddBtn.addEventListener('click', function() {
                document.getElementById('addressForm').reset();
                document.getElementById('addressId').value = '';
                document.getElementById('addressModalLabel').textContent = '添加收货地址';
                $('#addressModal').modal('show');
            });
        }

        // 编辑地址按钮点击事件
        const editBtns = document.querySelectorAll('.edit-address-btn');
        editBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const addressId = this.dataset.id;
                // 获取地址信息
                fetch(`/users/api/addresses/${addressId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // 填充表单
                        document.getElementById('addressId').value = data.id;
                        document.getElementById('receiver').value = data.receiver;
                        document.getElementById('phone').value = data.phone;
                        document.getElementById('province').value = data.province;
                        document.getElementById('city').value = data.city;
                        document.getElementById('district').value = data.district;
                        document.getElementById('detail').value = data.detail;
                        document.getElementById('isDefault').checked = data.is_default;
                        
                        document.getElementById('addressModalLabel').textContent = '编辑收货地址';
                        $('#addressModal').modal('show');
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('获取地址信息失败');
                    });
            });
        });

        // 保存地址
        document.getElementById('saveAddressBtn').addEventListener('click', function() {
            const form = document.getElementById('addressForm');
            if (!form.checkValidity()) {
                form.reportValidity();
        return;
    }

            const addressId = document.getElementById('addressId').value;
            const formData = {
                receiver: document.getElementById('receiver').value,
                phone: document.getElementById('phone').value,
                province: document.getElementById('province').value,
                city: document.getElementById('city').value,
                district: document.getElementById('district').value,
                detail: document.getElementById('detail').value,
                is_default: document.getElementById('isDefault').checked
            };

            const url = addressId ? `/users/api/addresses/${addressId}/` : '/users/api/addresses/';
            const method = addressId ? 'PUT' : 'POST';
    
    fetch(url, {
        method: method,
        headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(formData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络错误');
                }
                return response.json();
            })
    .then(data => {
                $('#addressModal').modal('hide');
                // 刷新页面显示最新地址
            location.reload();
    })
    .catch(error => {
                console.error('Error:', error);
                alert('保存地址失败: ' + error.message);
            });
        });

        // 删除地址按钮点击事件
        let addressIdToDelete = null;
        const deleteBtns = document.querySelectorAll('.delete-address-btn');
        deleteBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                addressIdToDelete = this.dataset.id;
                $('#deleteModal').modal('show');
    });
});

        // 确认删除
        document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
            if (!addressIdToDelete) return;

            fetch(`/users/api/addresses/${addressIdToDelete}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': csrfToken
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络错误');
                }
                $('#deleteModal').modal('hide');
                // 刷新页面
                location.reload();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('删除地址失败: ' + error.message);
            });
        });

        // 设为默认地址
        const defaultBtns = document.querySelectorAll('.set-default-btn');
        defaultBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const addressId = this.dataset.id;
                
                fetch(`/users/api/addresses/${addressId}/set_default/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('网络错误');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        // 刷新页面
                        location.reload();
                    } else {
                        alert(data.message || '操作失败');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('设置默认地址失败: ' + error.message);
                });
            });
    });

    // 确认注销账户
    window.confirmLogout = function() {
        if (confirm('确定要注销当前账户吗？注销后需要重新登录。')) {
            // 发送注销请求
            fetch('{% url "account:logout" %}', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                if (response.ok) {
                    // 注销成功，跳转到首页
                    alert('注销成功！');
                    window.location.href = '{% url "goods:index" %}';
                } else {
                    alert('注销失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('注销失败，请重试');
            });
        }
    };
});
</script>
{% endblock %}
