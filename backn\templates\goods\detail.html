{% extends 'base.html' %}

{% block title %}{{ product.name }} - 商品详情 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}商品详情{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <a href="{% url 'goods:list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
    <a href="{% url 'goods:edit' product.id %}" class="btn btn-primary">
        <i class="fas fa-edit me-1"></i>编辑商品
    </a>
    <button type="button" class="btn btn-outline-warning toggle-status" 
            data-product-id="{{ product.id }}" 
            data-current-status="{{ product.is_active }}">
        {% if product.is_active %}
            <i class="fas fa-ban me-1"></i>下架商品
        {% else %}
            <i class="fas fa-check me-1"></i>上架商品
        {% endif %}
    </button>
    <button type="button" class="btn btn-outline-danger delete-product" 
            data-product-id="{{ product.id }}" 
            data-product-name="{{ product.name }}">
        <i class="fas fa-trash me-1"></i>删除商品
    </button>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 商品基本信息 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-box me-2"></i>商品信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        {% if product.main_image %}
                            <img src="{{ product.main_image.url }}" alt="{{ product.name }}" 
                                 class="img-fluid rounded shadow-sm">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="height: 300px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-8">
                        <h3 class="mb-3">{{ product.name }}</h3>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>商品分类：</strong>
                            </div>
                            <div class="col-sm-9">
                                <span class="badge bg-primary">{{ product.category.name }}</span>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>商品价格：</strong>
                            </div>
                            <div class="col-sm-9">
                                <span class="h4 text-success">¥{{ product.price }}</span>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>库存数量：</strong>
                            </div>
                            <div class="col-sm-9">
                                {% if product.stock > 0 %}
                                    <span class="text-success">{{ product.stock }} 件</span>
                                {% else %}
                                    <span class="text-danger">缺货</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>销售数量：</strong>
                            </div>
                            <div class="col-sm-9">
                                <span class="text-info">{{ product.sales }} 件</span>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>商品状态：</strong>
                            </div>
                            <div class="col-sm-9">
                                {% if product.is_active %}
                                    <span class="badge bg-success">已上架</span>
                                {% else %}
                                    <span class="badge bg-secondary">已下架</span>
                                {% endif %}
                                
                                {% if product.is_hot %}
                                    <span class="badge bg-danger ms-1">热门</span>
                                {% endif %}
                                
                                {% if product.is_new %}
                                    <span class="badge bg-info ms-1">新品</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>创建时间：</strong>
                            </div>
                            <div class="col-sm-9">
                                {{ product.created_time|date:"Y年m月d日 H:i" }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>更新时间：</strong>
                            </div>
                            <div class="col-sm-9">
                                {{ product.updated_time|date:"Y年m月d日 H:i" }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6><i class="fas fa-align-left me-2"></i>商品描述</h6>
                    <div class="bg-light p-3 rounded">
                        {{ product.description|linebreaks }}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 商品图片 -->
        {% if images %}
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images me-2"></i>商品图片
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for image in images %}
                    <div class="col-md-3 mb-3">
                        <div class="position-relative">
                            <img src="{{ image.image.url }}" alt="{{ image.alt_text }}" 
                                 class="img-fluid rounded shadow-sm">
                            <div class="position-absolute top-0 end-0 p-1">
                                <button type="button" class="btn btn-sm btn-danger delete-image" 
                                        data-image-id="{{ image.id }}">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        {% if image.alt_text %}
                        <small class="text-muted">{{ image.alt_text }}</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
    
    <!-- 侧边栏信息 -->
    <div class="col-md-4">
        <!-- 快速操作 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'goods:edit' product.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-edit me-2"></i>编辑商品
                    </a>
                    <button type="button" class="btn btn-outline-success" id="updateStock">
                        <i class="fas fa-boxes me-2"></i>更新库存
                    </button>
                    <button type="button" class="btn btn-outline-info" id="toggleHot">
                        <i class="fas fa-fire me-2"></i>
                        {% if product.is_hot %}取消热门{% else %}设为热门{% endif %}
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 销售统计 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>销售统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary mb-1">{{ product.sales }}</h5>
                            <small class="text-muted">总销量</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-1">¥{{ product.price|floatformat:2 }}</h5>
                        <small class="text-muted">单价</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-info mb-1">{{ product.stock }}</h5>
                            <small class="text-muted">库存</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-warning mb-1">¥{% widthratio product.sales 1 product.price %}</h5>
                        <small class="text-muted">总销售额</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 相关链接 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>相关链接
                </h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{% url 'goods:category_list' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-tags me-2"></i>分类管理
                    </a>
                    <a href="{% url 'goods:list' %}?category={{ product.category.id }}" class="list-group-item list-group-item-action">
                        <i class="fas fa-list me-2"></i>同分类商品
                    </a>
                    <a href="{% url 'orders:list' %}" class="list-group-item list-group-item-action">
                        <i class="fas fa-shopping-bag me-2"></i>相关订单
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 切换商品状态
    $('.toggle-status').click(function() {
        const productId = $(this).data('product-id');
        const currentStatus = $(this).data('current-status');
        
        if (confirm(currentStatus ? '确定要下架此商品吗？' : '确定要上架此商品吗？')) {
            $.post('{% url "goods:toggle_status" 0 %}'.replace('0', productId), {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .fail(function() {
                alert('操作失败，请重试');
            });
        }
    });
    
    // 删除商品
    $('.delete-product').click(function() {
        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        
        if (confirm('确定要删除商品 "' + productName + '" 吗？此操作不可恢复！')) {
            $.post('{% url "goods:delete" 0 %}'.replace('0', productId), {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    window.location.href = '{% url "goods:list" %}';
                } else {
                    alert('删除失败，请重试');
                }
            })
            .fail(function() {
                alert('删除失败，请重试');
            });
        }
    });
});
</script>
{% endblock %}
