<!-- 顶部导航栏 -->
<div class="top-navbar">
  <div class="container">
    <div class="navbar-content">
      <div class="navbar-left">
        <a href="{% url 'home:index' %}" class="logo-link">
          MARS BUY
        </a>
      </div>
      
      <div class="navbar-middle">
        <div class="nav-categories">
          {% include 'includes/category_nav_dropdown.html' %}
        </div>
        
        <div class="nav-links">
          <a href="{% url 'home:index' %}" class="nav-link">
            <i class="fas fa-home"></i>
            <span>首页</span>
          </a>
          <a href="{% url 'goods:category' 309 %}" class="nav-link">
            <i class="fas fa-mobile-alt"></i>
            <span>数码</span>
          </a>
          <a href="{% url 'goods:category' 303 %}" class="nav-link">
            <i class="fas fa-home"></i>
            <span>家居</span>
          </a>
          <a href="{% url 'goods:category' 318 %}" class="nav-link">
            <i class="fas fa-utensils"></i>
            <span>食品</span>
          </a>
          <a href="{% url 'goods:list' %}" class="nav-link">
            <i class="fas fa-tags"></i>
            <span>全部商品</span>
          </a>
        </div>
      </div>
      
      <div class="navbar-right">
        <div class="nav-search">
          <form action="{% url 'goods:search' %}" method="get" class="search-form">
            <input type="text" name="keyword" placeholder="搜索商品" class="search-input">
            <button type="submit" class="search-btn">
              <i class="fas fa-search"></i>
            </button>
          </form>
        </div>
        
        <div class="nav-actions">
          <a href="{% url 'order:cart' %}" class="action-link">
            <i class="fas fa-shopping-cart"></i>
            <span>购物车</span>
          </a>
          <a href="{% url 'users:center' %}" class="action-link">
            <i class="fas fa-user"></i>
            <span>我的</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
/* 顶部导航栏样式 */
.top-navbar {
  background-color: #dc3545;
  color: white;
  padding: 10px 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.navbar-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-left {
  flex: 0 0 150px;
}

.logo-link {
  color: white;
  font-size: 22px;
  font-weight: bold;
  text-decoration: none;
  transition: all 0.3s ease;
}

.logo-link:hover {
  transform: scale(1.05);
  text-shadow: 0 0 10px rgba(255,255,255,0.5);
}

.navbar-middle {
  flex: 1;
  display: flex;
  align-items: center;
  margin: 0 20px;
}

.nav-categories {
  width: 120px;
  margin-right: 20px;
}

.nav-links {
  display: flex;
  gap: 15px;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.nav-link i {
  margin-right: 5px;
  font-size: 16px;
}

.nav-link:hover {
  background-color: rgba(255,255,255,0.2);
  transform: translateY(-2px);
}

.navbar-right {
  flex: 0 0 350px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 15px;
}

.nav-search {
  flex: 1;
  max-width: 220px;
}

.search-form {
  display: flex;
  position: relative;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  padding-right: 40px;
  border: none;
  border-radius: 4px;
  background-color: rgba(255,255,255,0.9);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  background-color: white;
  box-shadow: 0 0 0 3px rgba(255,255,255,0.3);
}

.search-btn {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  width: 40px;
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  transition: all 0.3s ease;
}

.search-btn:hover {
  color: #b21e2f;
  transform: scale(1.1);
}

.nav-actions {
  display: flex;
  gap: 15px;
}

.action-link {
  color: white;
  text-decoration: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  transition: all 0.3s ease;
}

.action-link i {
  font-size: 18px;
  margin-bottom: 3px;
}

.action-link:hover {
  transform: translateY(-2px);
  text-shadow: 0 0 5px rgba(255,255,255,0.5);
}

/* 导航中的分类下拉菜单样式覆盖 */
.top-navbar .category-nav-dropdown {
  border-radius: 4px;
  overflow: hidden;
  background-color: rgba(255,255,255,0.2);
}

.top-navbar .category-title {
  padding: 8px 12px;
  font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 992px) {
  .navbar-left {
    flex: 0 0 120px;
  }
  
  .logo-link {
    font-size: 18px;
  }
  
  .nav-categories {
    width: 100px;
    margin-right: 10px;
  }
  
  .nav-link {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .navbar-right {
    flex: 0 0 300px;
  }
  
  .nav-search {
    max-width: 180px;
  }
}

@media (max-width: 768px) {
  .navbar-middle {
    margin: 0 10px;
  }
  
  .nav-categories {
    width: 90px;
  }
  
  .nav-links {
    gap: 8px;
  }
  
  .nav-link span {
    display: none;
  }
  
  .nav-link i {
    margin-right: 0;
    font-size: 18px;
  }
  
  .navbar-right {
    flex: 0 0 220px;
  }
  
  .nav-search {
    max-width: 130px;
  }
}

@media (max-width: 576px) {
  .navbar-left {
    flex: 0 0 80px;
  }
  
  .logo-link {
    font-size: 16px;
  }
  
  .navbar-right {
    flex: 0 0 150px;
  }
  
  .nav-search {
    max-width: 100px;
  }
  
  .action-link span {
    display: none;
  }
}
</style> 