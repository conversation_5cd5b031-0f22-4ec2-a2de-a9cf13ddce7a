<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类页面按钮功能测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dc3545;
        }
        
        .test-header h1 {
            color: #dc3545;
            margin: 0;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .test-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            margin: 15px 0;
        }
        
        .test-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-btn.primary {
            background: #dc3545;
            color: white;
        }
        
        .test-btn.primary:hover {
            background: #c82333;
        }
        
        .test-btn.secondary {
            background: #6c757d;
            color: white;
        }
        
        .test-btn.secondary:hover {
            background: #5a6268;
        }
        
        .test-result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .test-result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .test-result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .test-result.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: white;
        }
        
        .product-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }
        
        .action-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #f8f9fa;
            border-color: #dc3545;
            color: #dc3545;
        }
        
        .action-btn.active {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.online {
            background: #28a745;
        }
        
        .status-indicator.offline {
            background: #dc3545;
        }
        
        .status-indicator.unknown {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-vial"></i> 分类页面按钮功能测试</h1>
            <p>测试分类页面中爱心和购物车按钮的功能是否正常工作</p>
        </div>

        <!-- 服务器状态检查 -->
        <div class="test-section">
            <h2><i class="fas fa-server"></i> 服务器状态检查</h2>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="checkServerStatus()">
                    <i class="fas fa-heartbeat"></i> 检查前台服务器
                </button>
                <button class="test-btn secondary" onclick="checkBackendStatus()">
                    <i class="fas fa-cogs"></i> 检查后台服务器
                </button>
            </div>
            <div id="server-status" class="test-result info">等待检查服务器状态...</div>
        </div>

        <!-- URL测试 -->
        <div class="test-section">
            <h2><i class="fas fa-link"></i> URL访问测试</h2>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="testCategoryPage()">
                    <i class="fas fa-external-link-alt"></i> 测试分类页面
                </button>
                <button class="test-btn secondary" onclick="testLoginPage()">
                    <i class="fas fa-sign-in-alt"></i> 测试登录页面
                </button>
            </div>
            <div id="url-test" class="test-result info">等待测试URL访问...</div>
        </div>

        <!-- 模拟商品卡片 -->
        <div class="test-section">
            <h2><i class="fas fa-shopping-bag"></i> 模拟商品按钮测试</h2>
            <div class="product-card">
                <h3>测试商品 - MacBook Pro 16英寸</h3>
                <p>价格: ¥15999.00</p>
                <div class="product-actions">
                    <button class="action-btn" data-product-id="96" onclick="testFavorite(this)" title="收藏">
                        <i class="far fa-heart"></i> 收藏
                    </button>
                    <button class="action-btn" data-product-id="96" onclick="testAddToCart(this)" title="加入购物车">
                        <i class="fas fa-shopping-cart"></i> 购物车
                    </button>
                </div>
            </div>
            <div id="button-test" class="test-result info">点击上方按钮测试功能...</div>
        </div>

        <!-- 功能检查 -->
        <div class="test-section">
            <h2><i class="fas fa-check-circle"></i> 功能完整性检查</h2>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="checkAllFunctions()">
                    <i class="fas fa-tasks"></i> 全面检查
                </button>
                <button class="test-btn secondary" onclick="clearResults()">
                    <i class="fas fa-trash"></i> 清空结果
                </button>
            </div>
            <div id="function-check" class="test-result info">等待功能检查...</div>
        </div>
    </div>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            return `[${timestamp}] ${message}\n`;
        }

        // 检查前台服务器状态
        async function checkServerStatus() {
            const resultDiv = document.getElementById('server-status');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = '正在检查前台服务器状态...';

            try {
                const response = await fetch('http://127.0.0.1:8001/', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = log('✅ 前台服务器 (8001端口) 状态正常', 'success');
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = log(`❌ 前台服务器连接失败: ${error.message}`, 'error');
            }
        }

        // 检查后台服务器状态
        async function checkBackendStatus() {
            const resultDiv = document.getElementById('server-status');
            const currentText = resultDiv.textContent;
            
            try {
                const response = await fetch('http://127.0.0.1:8003/', {
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = currentText + log('✅ 后台服务器 (8003端口) 状态正常', 'success');
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.textContent = currentText + log(`❌ 后台服务器连接失败: ${error.message}`, 'error');
            }
        }

        // 测试分类页面
        function testCategoryPage() {
            const resultDiv = document.getElementById('url-test');
            resultDiv.className = 'test-result info';
            resultDiv.textContent = log('正在测试分类页面访问...', 'info');

            // 打开分类页面
            const categoryUrl = 'http://127.0.0.1:8001/goods/category/324/';
            window.open(categoryUrl, '_blank');
            
            resultDiv.className = 'test-result success';
            resultDiv.textContent = log(`✅ 已打开分类页面: ${categoryUrl}`, 'success');
        }

        // 测试登录页面
        function testLoginPage() {
            const resultDiv = document.getElementById('url-test');
            const currentText = resultDiv.textContent;
            
            // 打开登录页面
            const loginUrl = 'http://127.0.0.1:8001/account/login/';
            window.open(loginUrl, '_blank');
            
            resultDiv.className = 'test-result success';
            resultDiv.textContent = currentText + log(`✅ 已打开登录页面: ${loginUrl}`, 'success');
        }

        // 测试收藏功能
        function testFavorite(button) {
            const resultDiv = document.getElementById('button-test');
            const productId = button.getAttribute('data-product-id');
            
            resultDiv.className = 'test-result info';
            resultDiv.textContent = log(`正在测试收藏功能 (商品ID: ${productId})...`, 'info');

            // 模拟点击效果
            button.classList.add('active');
            const icon = button.querySelector('i');
            icon.classList.remove('far');
            icon.classList.add('fas');

            setTimeout(() => {
                resultDiv.className = 'test-result success';
                resultDiv.textContent = log(`✅ 收藏功能测试完成 - 按钮状态已更新`, 'success');
            }, 1000);
        }

        // 测试购物车功能
        function testAddToCart(button) {
            const resultDiv = document.getElementById('button-test');
            const currentText = resultDiv.textContent;
            const productId = button.getAttribute('data-product-id');
            
            resultDiv.textContent = currentText + log(`正在测试购物车功能 (商品ID: ${productId})...`, 'info');

            // 模拟加载状态
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = '<i class="fas fa-check"></i> 已添加';
                button.style.background = '#28a745';
                button.style.color = 'white';
                
                resultDiv.className = 'test-result success';
                resultDiv.textContent = currentText + log(`✅ 购物车功能测试完成 - 商品已添加`, 'success');

                // 恢复按钮状态
                setTimeout(() => {
                    button.innerHTML = originalHTML;
                    button.disabled = false;
                    button.style.background = '';
                    button.style.color = '';
                }, 2000);
            }, 1500);
        }

        // 全面功能检查
        function checkAllFunctions() {
            const resultDiv = document.getElementById('function-check');
            resultDiv.className = 'test-result info';
            
            let checkResults = '';
            checkResults += log('🔍 开始全面功能检查...', 'info');
            
            // 检查必要的元素
            const checks = [
                {
                    name: 'Font Awesome图标库',
                    test: () => document.querySelector('link[href*="font-awesome"]') !== null,
                },
                {
                    name: '收藏按钮',
                    test: () => document.querySelectorAll('[onclick*="testFavorite"]').length > 0,
                },
                {
                    name: '购物车按钮', 
                    test: () => document.querySelectorAll('[onclick*="testAddToCart"]').length > 0,
                },
                {
                    name: 'JavaScript功能',
                    test: () => typeof testFavorite === 'function' && typeof testAddToCart === 'function',
                }
            ];

            checks.forEach(check => {
                const result = check.test();
                const status = result ? '✅' : '❌';
                checkResults += log(`${status} ${check.name}: ${result ? '正常' : '异常'}`, result ? 'success' : 'error');
            });

            checkResults += log('📋 检查完成！请手动测试实际的分类页面功能。', 'info');
            checkResults += log('🔗 建议测试步骤:', 'info');
            checkResults += log('1. 访问 http://127.0.0.1:8001/goods/category/324/', 'info');
            checkResults += log('2. 未登录状态下点击爱心和购物车按钮', 'info');
            checkResults += log('3. 确认是否正确跳转到登录页面', 'info');
            checkResults += log('4. 登录后再次测试按钮功能', 'info');

            resultDiv.className = 'test-result success';
            resultDiv.textContent = checkResults;
        }

        // 清空结果
        function clearResults() {
            const resultDivs = document.querySelectorAll('.test-result');
            resultDivs.forEach(div => {
                div.className = 'test-result info';
                div.textContent = '等待测试...';
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 分类页面按钮功能测试页面已加载');
            
            // 自动检查基本功能
            setTimeout(() => {
                checkAllFunctions();
            }, 1000);
        });
    </script>
</body>
</html>
