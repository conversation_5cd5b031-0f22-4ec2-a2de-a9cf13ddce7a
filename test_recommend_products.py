#!/usr/bin/env python
"""
测试推荐商品显示逻辑
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop/shop_front')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from goods.models import Product

def test_recommend_products():
    """测试推荐商品选择逻辑"""
    print("🔍 测试推荐商品选择逻辑...")

    # 获取所有商品按库存排序
    all_products = Product.objects.filter(is_active=True).order_by('-stock')
    print(f"📊 数据库中共有 {all_products.count()} 个活跃商品")

    if all_products.count() == 0:
        print("❌ 没有找到任何活跃商品")
        return

    print("\n📦 所有商品按库存排序（前10个）：")
    for i, product in enumerate(all_products[:10], 1):
        print(f"{i:2d}. {product.name:<30} - 库存:{product.stock:>4} 价格:¥{product.price}")

    # 获取推荐商品（库存最多的5个）
    recommend_products = Product.objects.filter(is_active=True).order_by('-stock')[:5]

    print(f"\n👍 为你推荐（库存最多的5个）：")
    for i, product in enumerate(recommend_products, 1):
        print(f"{i}. {product.name}")
        print(f"   库存: {product.stock}")
        print(f"   价格: ¥{product.price}")
        print(f"   销量: {product.sales}")
        print(f"   分类: {product.category.name if product.category else '无分类'}")
        print()

    # 获取热门商品（按销量排序）
    hot_products = Product.objects.filter(is_hot=True, is_active=True).order_by('-sales')[:5]

    print(f"\n🔥 热门商品（按销量排序的5个）：")
    for i, product in enumerate(hot_products, 1):
        print(f"{i}. {product.name}")
        print(f"   销量: {product.sales}")
        print(f"   库存: {product.stock}")
        print(f"   价格: ¥{product.price}")
        print(f"   是否热门: {product.is_hot}")
        print(f"   分类: {product.category.name if product.category else '无分类'}")
        print()

if __name__ == '__main__':
    test_recommend_products()
