#!/usr/bin/env python3
"""
测试评价页面商品链接功能
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.urls import reverse
from django.test import Client
from django.contrib.auth import get_user_model
from reviews.models import Review
from goods.models import Product

User = get_user_model()

def test_review_page_links():
    """测试评价页面的商品链接"""
    print("🧪 测试评价页面商品链接功能")
    print("=" * 60)
    
    # 1. 检查URL配置
    print("\n1. 检查URL配置...")
    try:
        my_reviews_url = reverse('reviews:my_reviews')
        print(f"   ✅ 我的评价页面URL: {my_reviews_url}")
        
        # 检查商品详情URL
        if Product.objects.exists():
            product = Product.objects.first()
            detail_url = reverse('goods:detail', kwargs={'pk': product.id})
            print(f"   ✅ 商品详情页面URL: {detail_url}")
        else:
            print("   ⚠️ 没有商品数据，无法测试商品详情URL")
            
    except Exception as e:
        print(f"   ❌ URL配置错误: {e}")
        return False
    
    # 2. 检查评价数据
    print("\n2. 检查评价数据...")
    reviews_count = Review.objects.count()
    print(f"   📊 评价总数: {reviews_count}")
    
    if reviews_count > 0:
        review = Review.objects.first()
        print(f"   📝 示例评价: {review.product.name}")
        print(f"   ⭐ 评分: {review.score}星")
        print(f"   👤 用户: {review.user.username}")
    else:
        print("   ⚠️ 没有评价数据")
    
    # 3. 测试页面访问
    print("\n3. 测试页面访问...")
    client = Client()
    
    # 创建测试用户
    try:
        user = User.objects.filter(username='testuser').first()
        if not user:
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
            print("   ✅ 创建测试用户")
        
        # 登录
        client.login(username='testuser', password='testpass123')
        print("   ✅ 用户登录成功")
        
        # 访问评价页面
        response = client.get(my_reviews_url)
        print(f"   📄 评价页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 评价页面访问成功")
            
            # 检查页面内容
            content = response.content.decode('utf-8')
            if 'product-image-link' in content:
                print("   ✅ 商品图片链接已添加")
            else:
                print("   ❌ 商品图片链接未找到")
                
            if 'goods:detail' in content:
                print("   ✅ 商品详情链接已配置")
            else:
                print("   ❌ 商品详情链接未配置")
        else:
            print(f"   ❌ 评价页面访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 页面测试失败: {e}")
    
    # 4. 显示访问链接
    print("\n4. 访问链接...")
    print(f"   🌐 我的评价页面: http://127.0.0.1:8001{my_reviews_url}")
    print(f"   🔑 登录页面: http://127.0.0.1:8001/account/login/")
    
    return True

def create_test_review():
    """创建测试评价数据"""
    print("\n🔧 创建测试评价数据...")
    
    try:
        # 获取用户和商品
        user = User.objects.filter(username='testuser').first()
        if not user:
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
        
        product = Product.objects.first()
        if not product:
            print("   ❌ 没有商品数据，无法创建评价")
            return False
        
        # 检查是否已有评价
        existing_review = Review.objects.filter(user=user, product=product).first()
        if existing_review:
            print(f"   ✅ 已存在评价: {product.name}")
            return True
        
        # 创建评价
        review = Review.objects.create(
            user=user,
            product=product,
            score=5,
            content="这是一个测试评价，用于验证商品链接功能。商品质量很好，值得推荐！",
            is_verified=True
        )
        
        print(f"   ✅ 创建评价成功: {product.name}")
        return True
        
    except Exception as e:
        print(f"   ❌ 创建评价失败: {e}")
        return False

def main():
    """主函数"""
    print("🔗 评价页面商品链接功能测试")
    print("=" * 60)
    
    # 创建测试数据
    create_test_review()
    
    # 测试功能
    test_review_page_links()
    
    print("\n✨ 测试完成！")
    print("现在您可以:")
    print("1. 访问 http://127.0.0.1:8001/reviews/my-reviews/")
    print("2. 使用用户名 'testuser' 和密码 'testpass123' 登录")
    print("3. 点击商品图片或商品名称跳转到商品详情页面")

if __name__ == '__main__':
    main()
