from django.contrib import admin
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from .models import Category, Product, ProductImage
from .forms import ProductForm, ProductImageForm, CategoryForm
import os

class ProductImageInline(admin.TabularInline):
    """商品图片内联编辑"""
    model = ProductImage
    extra = 1
    fields = ('image', 'image_preview', 'sort_order')
    readonly_fields = ('image_preview',)
    
    def image_preview(self, obj):
        """图片预览"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.image.url
            )
        return "无图片"
    image_preview.short_description = "图片预览"

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    """分类管理"""
    form = CategoryForm
    list_display = ('name', 'parent', 'icon_preview', 'sort_order', 'is_active', 'created_time')
    list_filter = ('is_active', 'parent', 'created_time')
    search_fields = ('name', 'description')
    list_editable = ('sort_order', 'is_active')
    ordering = ('sort_order', 'name')
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'parent', 'description')
        }),
        ('图标设置', {
            'fields': ('icon', 'external_icon', 'icon_preview'),
            'description': '可以使用"图标"字段上传，或使用"上传图标（支持任意路径）"从电脑任意位置选择图片'
        }),
        ('显示设置', {
            'fields': ('sort_order', 'is_active')
        }),
    )
    readonly_fields = ('icon_preview',)
    
    def icon_preview(self, obj):
        """图标预览"""
        if obj.icon:
            return format_html(
                '<img src="{}" style="max-width: 50px; max-height: 50px;" />',
                obj.icon.url
            )
        return "无图标"
    icon_preview.short_description = "图标预览"

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    """商品管理"""
    form = ProductForm
    list_display = ('name', 'category', 'price', 'stock', 'sales', 'image_preview', 'is_hot', 'is_new', 'is_active')
    list_filter = ('category', 'is_hot', 'is_new', 'is_active', 'created_time')
    search_fields = ('name', 'description')
    list_editable = ('price', 'stock', 'is_hot', 'is_new', 'is_active')
    ordering = ('-created_time',)
    inlines = [ProductImageInline]
    
    fieldsets = (
        ('基本信息', {
            'fields': ('name', 'category', 'description')
        }),
        ('价格库存', {
            'fields': ('price', 'stock', 'sales')
        }),
        ('主图设置', {
            'fields': ('main_image', 'external_image', 'main_image_preview'),
            'description': '可以使用"主图"字段上传，或使用"上传图片（支持任意路径）"从电脑任意位置选择图片。系统会自动处理文件路径和重命名。'
        }),
        ('商品属性', {
            'fields': ('is_hot', 'is_new', 'is_active')
        }),
    )
    readonly_fields = ('main_image_preview', 'sales')
    
    def image_preview(self, obj):
        """列表页图片预览"""
        if obj.main_image:
            return format_html(
                '<img src="{}" style="max-width: 50px; max-height: 50px;" />',
                obj.main_image.url
            )
        return "无图片"
    image_preview.short_description = "主图"
    
    def main_image_preview(self, obj):
        """详情页图片预览"""
        if obj.main_image:
            return format_html(
                '<img src="{}" style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; padding: 5px;" />',
                obj.main_image.url
            )
        return "无图片"
    main_image_preview.short_description = "主图预览"
    
    def save_model(self, request, obj, form, change):
        """保存模型时的自定义处理"""
        super().save_model(request, obj, form, change)
        
        # 如果有上传新图片，可以在这里做额外处理
        if obj.main_image:
            # 确保图片文件存在
            if not os.path.exists(obj.main_image.path):
                # 如果文件不存在，可以设置默认图片或者记录日志
                pass

@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    """商品图片管理"""
    list_display = ('product', 'image_preview', 'sort_order', 'created_time')
    list_filter = ('product__category', 'created_time')
    search_fields = ('product__name',)
    list_editable = ('sort_order',)
    ordering = ('product', 'sort_order')
    
    fields = ('product', 'image', 'image_preview', 'sort_order')
    readonly_fields = ('image_preview',)
    
    def image_preview(self, obj):
        """图片预览"""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.image.url
            )
        return "无图片"
    image_preview.short_description = "图片预览"

# 自定义管理站点标题
admin.site.site_header = 'MARS BUY 商品管理系统'
admin.site.site_title = 'MARS BUY 管理'
admin.site.index_title = '商品管理'
