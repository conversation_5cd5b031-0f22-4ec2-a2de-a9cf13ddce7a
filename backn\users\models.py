from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils.translation import gettext_lazy as _

class User(AbstractUser):
    """自定义用户模型"""
    avatar = models.ImageField(upload_to='avatars/', default='avatars/default.png', verbose_name=_('头像'))
    phone = models.CharField(max_length=11, blank=True, verbose_name=_('手机号'), db_index=True)
    is_vip = models.BooleanField(default=False, verbose_name=_('是否VIP'))
    balance = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('账户余额'))

    class Meta:
        verbose_name = _('用户')
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['username', 'phone']),
            models.Index(fields=['is_vip']),
        ]

    def __str__(self):
        return self.username
