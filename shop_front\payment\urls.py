from django.urls import path
from . import views

app_name = 'payment'

urlpatterns = [
    # 支付页面
    path('pay/<str:order_number>/', views.payment_page, name='pay'),
    path('pay-simple/<str:order_number>/', views.payment_page_simple, name='pay_simple'),
    path('success/<str:order_number>/', views.payment_success, name='success'),

    # 支付密码验证
    path('verify-password/', views.verify_payment_password, name='verify_password'),
    path('simulate-success/<str:order_number>/', views.simulate_payment_success, name='simulate_success'),

    # 测试页面
    path('test-password/', views.test_password_page, name='test_password'),
    path('simple-test/', views.simple_test_page, name='simple_test'),
    path('debug-test/', views.debug_test_page, name='debug_test'),

    # 支付宝支付
    path('alipay/pay/<str:order_number>/', views.alipay_pay, name='alipay_pay'),
    path('alipay/return/', views.alipay_return, name='alipay_return'),
    path('alipay/notify/', views.alipay_notify, name='alipay_notify'),
]