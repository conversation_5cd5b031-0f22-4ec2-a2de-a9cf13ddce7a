{% extends 'base.html' %}

{% block title %}支付成功 - MARS BUY{% endblock %}

{% load static %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .success-container {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 60px 40px;
        margin: 40px 0;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        text-align: center;
    }

    .success-icon {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 30px;
        animation: successPulse 2s ease-in-out infinite;
    }

    .success-icon i {
        font-size: 60px;
        color: white;
    }

    @keyframes successPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .success-title {
        font-size: 36px;
        font-weight: 700;
        color: #28a745;
        margin-bottom: 15px;
    }

    .success-subtitle {
        font-size: 18px;
        color: #666;
        margin-bottom: 40px;
        line-height: 1.6;
    }

    .order-summary {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 30px;
        margin: 30px 0;
        border-left: 5px solid #28a745;
    }

    .order-summary h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 20px;
        font-weight: 600;
        text-align: left;
    }

    .summary-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        text-align: left;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px dashed #ddd;
    }

    .summary-item:last-child {
        border-bottom: none;
    }

    .summary-label {
        font-weight: 600;
        color: #555;
    }

    .summary-value {
        color: #333;
        font-weight: 500;
    }

    .summary-amount {
        color: #28a745 !important;
        font-size: 24px;
        font-weight: 700;
    }

    .action-buttons {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-top: 40px;
        flex-wrap: wrap;
    }

    .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        min-width: 160px;
        justify-content: center;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(40,167,69,0.3);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(40,167,69,0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline {
        background: transparent;
        color: #6c757d;
        border: 2px solid #6c757d;
        box-shadow: none;
    }

    .btn-outline:hover {
        background: #6c757d;
        color: white;
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(108,117,125,0.3);
        text-decoration: none;
    }

    .next-steps {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 15px;
        padding: 25px;
        margin-top: 30px;
        text-align: left;
    }

    .next-steps h4 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .next-steps ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .next-steps li {
        padding: 8px 0;
        color: #666;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .next-steps li i {
        color: #28a745;
        width: 16px;
    }

    @media (max-width: 768px) {
        .success-container {
            padding: 40px 20px;
            margin: 20px 10px;
        }
        
        .success-title {
            font-size: 28px;
        }
        
        .action-buttons {
            flex-direction: column;
            align-items: center;
        }
        
        .btn {
            width: 100%;
            max-width: 300px;
        }
        
        .summary-details {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="success-container">
        <!-- 成功图标 -->
        <div class="success-icon">
            <i class="fas fa-check"></i>
        </div>

        <!-- 成功标题 -->
        <h1 class="success-title">支付成功！</h1>
        <p class="success-subtitle">
            恭喜您，订单支付已完成！<br>
            我们将尽快为您安排发货，请耐心等待。
        </p>

        <!-- 订单摘要 -->
        <div class="order-summary">
            <h3><i class="fas fa-receipt"></i> 支付详情</h3>
            <div class="summary-details">
                <div class="summary-item">
                    <span class="summary-label">订单号：</span>
                    <span class="summary-value">{{ order.order_number }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">支付时间：</span>
                    <span class="summary-value">{{ order.paid_time|date:"Y-m-d H:i:s"|default:"刚刚" }}</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">支付方式：</span>
                    <span class="summary-value">支付宝</span>
                </div>
                <div class="summary-item">
                    <span class="summary-label">支付金额：</span>
                    <span class="summary-value summary-amount">¥{{ order.pay_amount }}</span>
                </div>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <a href="{% url 'order:detail' order.order_number %}" class="btn btn-success">
                <i class="fas fa-eye"></i>
                查看订单
            </a>
            <a href="{% url 'users:orders' %}" class="btn btn-primary">
                <i class="fas fa-list"></i>
                我的订单
            </a>
            <a href="{% url 'home:index' %}" class="btn btn-outline">
                <i class="fas fa-home"></i>
                继续购物
            </a>
        </div>

        <!-- 后续步骤 -->
        <div class="next-steps">
            <h4>
                <i class="fas fa-info-circle"></i>
                接下来会发生什么？
            </h4>
            <ul>
                <li>
                    <i class="fas fa-check-circle"></i>
                    我们已收到您的付款，订单正在处理中
                </li>
                <li>
                    <i class="fas fa-box"></i>
                    商品将在1-2个工作日内发货
                </li>
                <li>
                    <i class="fas fa-truck"></i>
                    发货后您将收到物流跟踪信息
                </li>
                <li>
                    <i class="fas fa-star"></i>
                    收货后别忘了给商品评价哦
                </li>
            </ul>
        </div>
    </div>
</div>

<script>
// 页面加载完成后的动画效果
document.addEventListener('DOMContentLoaded', function() {
    // 添加页面进入动画
    const container = document.querySelector('.success-container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(30px)';
    
    setTimeout(() => {
        container.style.transition = 'all 0.6s ease-out';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);

    // 5秒后自动跳转提示
    setTimeout(() => {
        const autoRedirect = confirm('是否跳转到订单详情页面？');
        if (autoRedirect) {
            window.location.href = "{% url 'order:detail' order.order_number %}";
        }
    }, 5000);
});
</script>
{% endblock %}
