from django.core.management.base import BaseCommand
from django.db import transaction
from goods.models import Category


class Command(BaseCommand):
    help = '创建新的分类结构'

    def handle(self, *args, **options):
        self.stdout.write('开始创建新的分类结构...')
        
        # 新的分类结构
        categories_data = [
            {
                'name': '时尚穿搭',
                'children': [
                    '男装', '女装', '童装', '鞋类', '箱包', '服饰配件'
                ]
            },
            {
                'name': '美妆护肤',
                'children': [
                    '面部护理', '彩妆', '身体护理', '美发产品', '香水香氛'
                ]
            },
            {
                'name': '家居日用',
                'children': [
                    '家纺床品', '厨房用品', '卫浴清洁', '家居装饰', '收纳用品'
                ]
            },
            {
                'name': '数码科技',
                'children': [
                    '手机及配件', '电脑办公设备', '智能穿戴', '摄影摄像器材'
                ]
            },
            {
                'name': '家用电器',
                'children': [
                    '大家电（冰箱、空调等）', '厨房小家电', '生活小家电'
                ]
            },
            {
                'name': '食品生鲜',
                'children': [
                    '休闲零食', '粮油副食', '新鲜果蔬', '肉禽蛋奶', '酒水饮料'
                ]
            },
            {
                'name': '母婴亲子',
                'children': [
                    '母婴用品', '儿童玩具', '童装童鞋', '早教学习产品'
                ]
            },
            {
                'name': '运动户外',
                'children': [
                    '运动服饰', '健身器材', '户外装备', '球类运动用品'
                ]
            },
            {
                'name': '汽车相关',
                'children': [
                    '汽车装饰', '汽车保养', '汽车电子', '车载用品'
                ]
            },
            {
                'name': '生活服务',
                'children': [
                    '虚拟商品（充值、会员等）', '家政服务', '本地生活团购'
                ]
            }
        ]
        
        try:
            with transaction.atomic():
                # 清理现有分类
                Category.objects.all().delete()
                self.stdout.write('清理了现有分类')
                
                created_count = 0
                
                for category_data in categories_data:
                    # 创建父分类
                    parent_category = Category.objects.create(
                        name=category_data['name'],
                        is_active=True,
                        sort_order=created_count + 1
                    )
                    created_count += 1
                    self.stdout.write(f'创建父分类: {parent_category.name}')
                    
                    # 创建子分类
                    for i, child_name in enumerate(category_data['children']):
                        child_category = Category.objects.create(
                            name=child_name,
                            parent=parent_category,
                            is_active=True,
                            sort_order=i + 1
                        )
                        created_count += 1
                        self.stdout.write(f'  创建子分类: {child_category.name}')
                
                self.stdout.write(self.style.SUCCESS(f'成功创建了 {created_count} 个分类！'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'创建分类时出错: {e}'))
