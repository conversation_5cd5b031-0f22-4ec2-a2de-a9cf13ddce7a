<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分类管理 - MARS BUY 后台管理系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#dc2626',     // 红色主题
                        secondary: '#ef4444',   // 浅红色
                        accent: '#f59e0b',      // 橙色
                        dark: '#1e293b',        // 深色
                        light: '#f8fafc'       // 浅色
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                    },
                },
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .category-card {
                @apply bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-gray-100 hover:border-red-200;
            }
            .category-title {
                @apply text-lg font-semibold text-dark flex items-center justify-between cursor-pointer p-4 hover:bg-red-50 rounded-t-xl transition-colors;
            }
            .subcategory-item {
                @apply py-3 px-4 hover:bg-red-50 transition-colors duration-200 cursor-pointer border-b border-gray-50 last:border-b-0 flex items-center justify-between;
            }
            .filter-btn {
                @apply px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border border-gray-200 hover:border-primary hover:text-primary;
            }
            .filter-btn-active {
                @apply bg-primary text-white border-primary;
            }
            .action-btn {
                @apply px-3 py-1.5 rounded-lg text-xs font-medium transition-all duration-200 border;
            }
            .btn-edit {
                @apply bg-green-500 text-white border-green-500 hover:bg-green-600;
            }
            .btn-delete {
                @apply bg-red-500 text-white border-red-500 hover:bg-red-600;
            }
            .btn-toggle {
                @apply bg-blue-500 text-white border-blue-500 hover:bg-blue-600;
            }
            .stats-badge {
                @apply bg-primary text-white px-3 py-1 rounded-full text-xs font-medium;
            }
        }
    </style>
</head>
<body class="bg-gray-50 text-dark">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <a href="{% url 'admin_panel:dashboard' %}" class="text-2xl font-bold text-primary">MARS BUY</a>
                <span class="text-gray-400">|</span>
                <span class="text-gray-600">分类管理</span>
            </div>
            
            <div class="flex items-center space-x-4">
                <a href="{% url 'goods:category_add' %}" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                    <i class="fa fa-plus mr-1"></i> 添加分类
                </a>
                <a href="{% url 'admin_panel:dashboard' %}" class="text-gray-600 hover:text-primary transition-colors">
                    <i class="fa fa-arrow-left mr-1"></i> 返回首页
                </a>
            </div>
        </div>
    </header>

    <main class="container mx-auto px-4 py-6">
        <!-- 页面标题 -->
        <div class="mb-6">
            <h1 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark">商品分类管理</h1>
            <p class="text-gray-500 mt-1">管理您的商品分类，支持层级结构和批量操作</p>
        </div>

        <!-- 筛选与排序区域 -->
        <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
                <div class="flex flex-wrap gap-2">
                    <button id="select-all" class="filter-btn filter-btn-active">
                        <i class="fa fa-check-square-o mr-1"></i> 全选
                    </button>
                    <button class="filter-btn" onclick="filterByStatus('active')">
                        <i class="fa fa-check mr-1"></i> 启用
                    </button>
                    <button class="filter-btn" onclick="filterByStatus('inactive')">
                        <i class="fa fa-ban mr-1"></i> 禁用
                    </button>
                    <button class="filter-btn" onclick="filterByType('parent')">
                        <i class="fa fa-folder mr-1"></i> 主分类
                    </button>
                </div>
                
                <div class="flex items-center gap-2">
                    <span class="text-sm text-gray-500">排序方式:</span>
                    <div class="flex rounded-lg overflow-hidden border border-gray-200">
                        <button class="px-3 py-2 text-sm bg-primary text-white" onclick="sortCategories('name')">A-Z</button>
                        <button class="px-3 py-2 text-sm hover:bg-gray-100" onclick="sortCategories('-name')">Z-A</button>
                        <button class="px-3 py-2 text-sm hover:bg-gray-100" onclick="sortCategories('created_time')">时间</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 分类卡片区域 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" id="categories-container">
            {% for category in parent_categories %}
            <div class="category-card" data-category-type="parent" data-status="{% if category.is_active %}active{% else %}inactive{% endif %}">
                <div class="category-title" onclick="toggleCategory('category-{{ category.id }}')">
                    <div class="flex items-center">
                        {% if category.name == '时尚穿搭' %}
                            <i class="fa fa-tshirt text-primary mr-3"></i>
                        {% elif category.name == '美妆护肤' %}
                            <i class="fa fa-paint-brush text-pink-500 mr-3"></i>
                        {% elif category.name == '家居日用' %}
                            <i class="fa fa-home text-amber-600 mr-3"></i>
                        {% elif category.name == '数码科技' %}
                            <i class="fa fa-laptop text-blue-700 mr-3"></i>
                        {% elif category.name == '家用电器' %}
                            <i class="fa fa-plug text-teal-600 mr-3"></i>
                        {% elif category.name == '食品生鲜' %}
                            <i class="fa fa-cutlery text-red-500 mr-3"></i>
                        {% elif category.name == '母婴亲子' %}
                            <i class="fa fa-child text-purple-500 mr-3"></i>
                        {% elif category.name == '运动户外' %}
                            <i class="fa fa-futbol-o text-green-600 mr-3"></i>
                        {% elif category.name == '汽车相关' %}
                            <i class="fa fa-car text-indigo-600 mr-3"></i>
                        {% elif category.name == '生活服务' %}
                            <i class="fa fa-handshake-o text-amber-700 mr-3"></i>
                        {% else %}
                            <i class="fa fa-folder text-primary mr-3"></i>
                        {% endif %}
                        <span>{{ category.name }}</span>
                        {% if not category.is_active %}
                            <span class="ml-2 px-2 py-1 bg-gray-200 text-gray-600 text-xs rounded">禁用</span>
                        {% endif %}
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="stats-badge">{{ category.children.count }} 子分类</span>
                        <i id="category-{{ category.id }}-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
                    </div>
                </div>
                <div id="category-{{ category.id }}" class="p-2">
                    {% for child in category.children.all %}
                    <div class="subcategory-item" data-category-type="child" data-status="{% if child.is_active %}active{% else %}inactive{% endif %}">
                        <div class="flex items-center">
                            <i class="fa fa-tag text-gray-400 mr-2"></i>
                            <span>{{ child.name }}</span>
                            {% if not child.is_active %}
                                <span class="ml-2 px-2 py-1 bg-gray-200 text-gray-600 text-xs rounded">禁用</span>
                            {% endif %}
                        </div>
                        <div class="flex items-center space-x-1">
                            <a href="{% url 'goods:category_edit' child.id %}" class="action-btn btn-edit" title="编辑">
                                <i class="fa fa-edit"></i>
                            </a>
                            <button onclick="toggleCategoryStatus({{ child.id }}, {{ child.is_active|yesno:'true,false' }})" 
                                    class="action-btn btn-toggle" title="{% if child.is_active %}禁用{% else %}启用{% endif %}">
                                <i class="fa fa-{% if child.is_active %}ban{% else %}check{% endif %}"></i>
                            </button>
                            <button onclick="deleteCategory({{ child.id }}, '{{ child.name }}')" 
                                    class="action-btn btn-delete" title="删除">
                                <i class="fa fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- 主分类操作按钮 -->
                    <div class="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center">
                        <div class="flex space-x-1">
                            <a href="{% url 'goods:category_edit' category.id %}" class="action-btn btn-edit">
                                <i class="fa fa-edit mr-1"></i> 编辑主分类
                            </a>
                            <button onclick="toggleCategoryStatus({{ category.id }}, {{ category.is_active|yesno:'true,false' }})" 
                                    class="action-btn btn-toggle">
                                <i class="fa fa-{% if category.is_active %}ban{% else %}check{% endif %} mr-1"></i> 
                                {% if category.is_active %}禁用{% else %}启用{% endif %}
                            </button>
                        </div>
                        <button onclick="deleteCategory({{ category.id }}, '{{ category.name }}')" 
                                class="action-btn btn-delete">
                            <i class="fa fa-trash mr-1"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </main>

    <script>
        // 分类折叠/展开功能
        function toggleCategory(id) {
            const category = document.getElementById(id);
            const icon = document.getElementById(`${id}-icon`);
            
            if (category.style.display === 'none') {
                category.style.display = 'block';
                icon.classList.add('rotate-180');
            } else {
                category.style.display = 'none';
                icon.classList.remove('rotate-180');
            }
        }

        // 全选按钮功能
        document.getElementById('select-all').addEventListener('click', function() {
            const isSelected = this.classList.contains('filter-btn-active');
            
            if (isSelected) {
                // 取消全选
                this.classList.remove('filter-btn-active');
                this.innerHTML = '<i class="fa fa-square-o mr-1"></i> 全选';
                
                // 隐藏所有分类
                document.querySelectorAll('[id^="category-"]').forEach(div => {
                    if (div.id.includes('-icon')) return;
                    div.style.display = 'none';
                });
                document.querySelectorAll('[id$="-icon"]').forEach(icon => {
                    icon.classList.remove('rotate-180');
                });
            } else {
                // 全选
                this.classList.add('filter-btn-active');
                this.innerHTML = '<i class="fa fa-check-square-o mr-1"></i> 全选';
                
                // 显示所有分类
                document.querySelectorAll('[id^="category-"]').forEach(div => {
                    if (div.id.includes('-icon')) return;
                    div.style.display = 'block';
                });
                document.querySelectorAll('[id$="-icon"]').forEach(icon => {
                    icon.classList.add('rotate-180');
                });
            }
        });

        // 默认展开所有分类
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('[id$="-icon"]').forEach(icon => {
                icon.classList.add('rotate-180');
            });
        });

        // 切换分类状态
        function toggleCategoryStatus(categoryId, currentStatus) {
            if (confirm(currentStatus ? '确定要禁用此分类吗？' : '确定要启用此分类吗？')) {
                fetch(`/goods/categories/toggle-status/${categoryId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('操作失败，请重试');
                    }
                })
                .catch(error => {
                    alert('操作失败，请重试');
                });
            }
        }

        // 删除分类
        function deleteCategory(categoryId, categoryName) {
            if (confirm('确定要删除分类 "' + categoryName + '" 吗？此操作不可恢复！')) {
                fetch(`/goods/categories/delete/${categoryId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': '{{ csrf_token }}',
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('删除失败：' + (data.message || '请重试'));
                    }
                })
                .catch(error => {
                    alert('删除失败，请重试');
                });
            }
        }

        // 筛选功能
        function filterByStatus(status) {
            const cards = document.querySelectorAll('.category-card');
            cards.forEach(card => {
                if (status === 'all' || card.dataset.status === status) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function filterByType(type) {
            const cards = document.querySelectorAll('.category-card');
            cards.forEach(card => {
                if (type === 'all' || card.dataset.categoryType === type) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // 排序功能
        function sortCategories(sortBy) {
            // 这里可以添加排序逻辑
            window.location.href = `?sort=${sortBy}`;
        }
    </script>
</body>
</html>
