from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from account.models import Favorite
from goods.models import Product, Category

# Create your views here.

def test_buttons(request):
    """测试收藏和购物车按钮功能的页面"""
    return render(request, 'test_buttons.html')

def login_view(request):
    """普通用户登录 - 重定向到users应用的登录页面"""
    next_url = request.GET.get('next', '')
    if next_url:
        return redirect(f'/users/login/?next={next_url}')
    return redirect('users:login')

def admin_login_view(request):
    """管理员登录"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        user = authenticate(request, username=username, password=password)
        
        if user is not None and user.is_staff:
            login(request, user)
            messages.success(request, '管理员登录成功！')
            return redirect('home:index')
        else:
            messages.error(request, '用户名或密码错误，或者没有管理员权限！')
    
    return render(request, 'account/admin_login.html', {'is_admin_login': True})

def logout_view(request):
    """退出登录 - 重定向到users应用的退出登录视图"""
    return redirect('users:logout')

@login_required
def favorite_list(request):
    """用户收藏夹列表"""
    favorites = Favorite.objects.filter(user=request.user).select_related('product')
    
    # 分页
    paginator = Paginator(favorites, 12)  # 每页显示12个商品
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)
    
    return render(request, 'account/favorite_list.html', {
        'page_obj': page_obj,
        'favorites': page_obj.object_list,
        'total_favorites': favorites.count()
    })

@login_required
@require_POST
def add_to_favorite(request, product_id):
    """添加商品到收藏夹"""
    product = get_object_or_404(Product, id=product_id)
    
    # 检查商品是否已经在收藏夹中
    favorite, created = Favorite.objects.get_or_create(user=request.user, product=product)

    if created:
        message = f'已将"{product.name}"添加到收藏夹'
        status = 'success'
    else:
        message = f'"{product.name}"已经在收藏夹中'
        status = 'info'
    
    # 如果是AJAX请求，返回JSON响应
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'status': status,
            'message': message
        })
    
    messages.success(request, message)
    # 重定向回之前的页面
    return redirect(request.META.get('HTTP_REFERER', 'goods:detail', args=[product_id]))

@login_required
@require_POST
def remove_from_favorite(request, product_id):
    """从收藏夹中移除商品"""
    favorite = get_object_or_404(Favorite, user=request.user, product_id=product_id)
    product_name = favorite.product.name
    favorite.delete()
    
    # 如果是AJAX请求，返回JSON响应
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return JsonResponse({
            'status': 'success',
            'message': f'已将"{product_name}"从收藏夹中移除'
        })
    
    messages.success(request, f'已将"{product_name}"从收藏夹中移除')
    # 重定向回之前的页面，如果是从收藏夹页面发起的请求，则重定向回收藏夹页面
    referer = request.META.get('HTTP_REFERER', '')
    if 'favorite' in referer:
        return redirect('account:favorite_list')
    return redirect(referer or 'home:index')

# 管理员功能视图
@user_passes_test(lambda u: u.is_staff)
def admin_dashboard(request):
    """管理员仪表板"""
    # 获取统计数据
    total_products = Product.objects.count()
    active_products = Product.objects.filter(is_active=True).count()
    total_categories = Category.objects.count()

    context = {
        'total_products': total_products,
        'active_products': active_products,
        'total_categories': total_categories,
    }
    return render(request, 'account/admin_dashboard.html', context)

@user_passes_test(lambda u: u.is_staff)
def product_list(request):
    """管理员商品列表"""
    products = Product.objects.select_related('category').order_by('-created_time')

    # 分页
    paginator = Paginator(products, 20)  # 每页显示20个商品
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'products': page_obj,
        'page_obj': page_obj,
    }
    return render(request, 'account/product_list.html', context)

@user_passes_test(lambda u: u.is_staff)
def product_add(request):
    """管理员添加商品"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            name = request.POST.get('name')
            price = request.POST.get('price')
            stock = request.POST.get('stock', 0)
            description = request.POST.get('description')
            category_id = request.POST.get('category')
            is_hot = request.POST.get('is_hot') == 'on'
            is_new = request.POST.get('is_new') == 'on'

            # 创建商品
            category = get_object_or_404(Category, id=category_id)
            product = Product.objects.create(
                name=name,
                price=price,
                stock=int(stock) if stock else 0,
                description=description,
                category=category,
                is_hot=is_hot,
                is_new=is_new
            )

            # 处理主图
            if 'main_image' in request.FILES:
                product.main_image = request.FILES['main_image']
                product.save()

            messages.success(request, '商品添加成功！')
            return redirect('account:product_list')
        except Exception as e:
            messages.error(request, f'添加商品失败：{str(e)}')

    # GET请求，显示添加商品表单
    categories = Category.objects.filter(is_active=True)
    return render(request, 'account/product_add.html', {
        'categories': categories
    })

@user_passes_test(lambda u: u.is_staff)
def product_edit(request, product_id):
    """管理员编辑商品"""
    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        try:
            # 更新商品信息
            product.name = request.POST.get('name')
            product.price = request.POST.get('price')
            stock = request.POST.get('stock')
            if stock:
                product.stock = int(stock)
            product.description = request.POST.get('description')
            category_id = request.POST.get('category')
            if category_id:
                product.category = get_object_or_404(Category, id=category_id)
            product.is_hot = request.POST.get('is_hot') == 'on'
            product.is_new = request.POST.get('is_new') == 'on'

            # 处理主图
            if 'main_image' in request.FILES:
                product.main_image = request.FILES['main_image']

            product.save()

            messages.success(request, '商品更新成功！')
            return redirect('account:product_list')
        except Exception as e:
            messages.error(request, f'更新商品失败：{str(e)}')

    # GET请求，显示编辑商品表单
    categories = Category.objects.filter(is_active=True)
    return render(request, 'account/product_edit.html', {
        'product': product,
        'categories': categories
    })
