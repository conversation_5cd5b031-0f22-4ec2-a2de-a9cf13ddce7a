{% extends 'base.html' %}
{% load static %}

{% block title %}订单详情 - MARS BUY{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/order.css' %}">
{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<div class="container">
    <div class="breadcrumb">
        <a href="{% url 'home:index' %}">首页</a>
        <span>&gt;</span>
        <a href="{% url 'users:center' %}">个人中心</a>
        <span>&gt;</span>
        <a href="{% url 'order:list' %}">我的订单</a>
        <span>&gt;</span>
        <span>订单详情</span>
    </div>
</div>

<!-- 订单详情主体 -->
<div class="container">
    <div class="order-detail-container">
        <div class="detail-header">
            <h3>订单详情</h3>
        </div>
        
        <!-- 订单状态 -->
        <div class="order-status-section">
            <div class="status-info">
                <div class="status-icon">
                    {% if order.status == 'pending' %}
                    <i class="fas fa-clock"></i>
                    {% elif order.status == 'paid' %}
                    <i class="fas fa-check-circle"></i>
                    {% elif order.status == 'shipped' %}
                    <i class="fas fa-shipping-fast"></i>
                    {% elif order.status == 'received' %}
                    <i class="fas fa-check-double"></i>
                    {% elif order.status == 'cancelled' %}
                    <i class="fas fa-times-circle"></i>
                    {% elif order.status == 'refunded' %}
                    <i class="fas fa-undo"></i>
                    {% endif %}
                </div>
                <div class="status-text">
                    {% if order.status == 'pending' %}
                    <h4>等待付款</h4>
                    <p>请在24小时内完成支付，超时订单将自动取消</p>
                    {% elif order.status == 'paid' %}
                    <h4>已付款，等待发货</h4>
                    <p>商家正在处理您的订单，请耐心等待</p>
                    {% elif order.status == 'shipped' %}
                    <h4>已发货，等待收货</h4>
                    <p>商品已发出，请注意查收</p>
                    {% elif order.status == 'received' %}
                    <h4>交易完成</h4>
                    <p>感谢您的购买，欢迎再次光临</p>
                    {% elif order.status == 'cancelled' %}
                    <h4>交易已取消</h4>
                    <p>订单已取消，如有疑问请联系客服</p>
                    {% elif order.status == 'refunded' %}
                    <h4>已退款</h4>
                    <p>退款已完成，如有疑问请联系客服</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="order-progress">
                <ul class="progress-steps clearfix">
                    <li class="step {% if order.status != '' %}active{% endif %}">
                        <div class="step-icon">1</div>
                        <div class="step-info">
                            <p class="step-name">提交订单</p>
                            <p class="step-time">{{ order.created_time|date:"Y-m-d H:i" }}</p>
                        </div>
                        <div class="step-line"></div>
                    </li>
                    <li class="step {% if order.status == 'paid' or order.status == 'shipped' or order.status == 'received' %}active{% endif %}">
                        <div class="step-icon">2</div>
                        <div class="step-info">
                            <p class="step-name">付款成功</p>
                            <p class="step-time">{% if order.paid_time %}{{ order.paid_time|date:"Y-m-d H:i" }}{% endif %}</p>
                        </div>
                        <div class="step-line"></div>
                    </li>
                    <li class="step {% if order.status == 'shipped' or order.status == 'received' %}active{% endif %}">
                        <div class="step-icon">3</div>
                        <div class="step-info">
                            <p class="step-name">商品发货</p>
                            <p class="step-time">{% if order.shipped_time %}{{ order.shipped_time|date:"Y-m-d H:i" }}{% endif %}</p>
                        </div>
                        <div class="step-line"></div>
                    </li>
                    <li class="step {% if order.status == 'received' %}active{% endif %}">
                        <div class="step-icon">4</div>
                        <div class="step-info">
                            <p class="step-name">交易完成</p>
                            <p class="step-time">{% if order.received_time %}{{ order.received_time|date:"Y-m-d H:i" }}{% endif %}</p>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- 收货信息 -->
        <div class="detail-section">
            <div class="section-header">
                <h4>收货信息</h4>
            </div>
            <div class="section-content address-info">
                <div class="info-item">
                    <span class="item-label">收货人：</span>
                    <span class="item-value">{{ order.receiver }}</span>
                </div>
                <div class="info-item">
                    <span class="item-label">联系电话：</span>
                    <span class="item-value">{{ order.receiver_mobile }}</span>
                </div>
                <div class="info-item">
                    <span class="item-label">收货地址：</span>
                    <span class="item-value">{{ order.address }}</span>
                </div>
            </div>
        </div>
        
        <!-- 订单信息 -->
        <div class="detail-section">
            <div class="section-header">
                <h4>订单信息</h4>
            </div>
            <div class="section-content order-info">
                <div class="info-item">
                    <span class="item-label">订单编号：</span>
                    <span class="item-value">{{ order.order_number }}</span>
                </div>
                <div class="info-item">
                    <span class="item-label">创建时间：</span>
                    <span class="item-value">{{ order.created_time|date:"Y-m-d H:i:s" }}</span>
                </div>
                <div class="info-item">
                    <span class="item-label">支付方式：</span>
                    <span class="item-value">
                        {% if order.pay_method == 1 %}
                        支付宝
                        {% elif order.pay_method == 2 %}
                        微信支付
                        {% elif order.pay_method == 3 %}
                        银行卡支付
                        {% else %}
                        未选择
                        {% endif %}
                    </span>
                </div>
                {% if order.pay_time %}
                <div class="info-item">
                    <span class="item-label">支付时间：</span>
                    <span class="item-value">{{ order.pay_time|date:"Y-m-d H:i:s" }}</span>
                </div>
                {% endif %}
                {% if order.remark %}
                <div class="info-item">
                    <span class="item-label">订单备注：</span>
                    <span class="item-value">{{ order.remark }}</span>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 商品信息 -->
        <div class="detail-section">
            <div class="section-header">
                <h4>商品信息</h4>
            </div>
            <div class="section-content goods-list">
                <div class="goods-header clearfix">
                    <div class="col-goods">商品信息</div>
                    <div class="col-price">单价</div>
                    <div class="col-num">数量</div>
                    <div class="col-total">小计</div>
                </div>
                
                {% for item in order.items.all %}
                <div class="goods-item clearfix">
                    <div class="col-goods">
                        <div class="goods-img">
                            <a href="{% url 'goods:detail' item.product.id %}">
                                <img src="{% if item.product.image %}{{ item.product.image.url }}{% elif item.product.main_image %}{{ item.product.main_image.url }}{% else %}{% static 'images/no-image.png' %}{% endif %}" alt="{{ item.product.name }}">
                            </a>
                        </div>
                        <div class="goods-info">
                            <div class="goods-name">
                                <a href="{% url 'goods:detail' item.product.id %}">{{ item.product.name }}</a>
                            </div>
                            {% if item.sku_text %}
                            <div class="goods-sku">{{ item.sku_text }}</div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-price">¥{{ item.price }}</div>
                    <div class="col-num">{{ item.quantity }}</div>
                    <div class="col-total">¥{{ item.total_price }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <!-- 支付信息 -->
        <div class="detail-section payment-section">
            <div class="section-header">
                <h4>支付信息</h4>
            </div>
            <div class="section-content payment-info">
                <div class="payment-item">
                    <span class="payment-label">商品总价：</span>
                    <span class="payment-value">¥{{ order.total_amount }}</span>
                </div>
                <div class="payment-item">
                    <span class="payment-label">运费：</span>
                    <span class="payment-value">¥{{ order.freight|default:"0.00" }}</span>
                </div>
                {% if order.discount_amount %}
                <div class="payment-item">
                    <span class="payment-label">优惠金额：</span>
                    <span class="payment-value">-¥{{ order.discount_amount }}</span>
                </div>
                {% endif %}
                <div class="payment-item payment-total">
                    <span class="payment-label">实付款：</span>
                    <span class="payment-value">¥{{ order.pay_amount }}</span>
                </div>
            </div>
        </div>
        
        <!-- 订单操作 -->
        <div class="detail-section">
            <div class="order-actions">
                {% if order.status == 'pending' %}
                <a href="{% url 'order:create' %}?order_id={{ order.id }}" class="btn-pay">去付款</a>
                <a href="javascript:;" class="btn-cancel" data-id="{{ order.id }}">取消订单</a>
                {% elif order.status == 'shipped' %}
                <a href="javascript:;" class="btn-confirm" data-id="{{ order.id }}">确认收货</a>
                <a href="javascript:;" class="btn-logistics" data-id="{{ order.id }}">查看物流</a>
                {% elif order.status == 'received' %}
                <a href="{% url 'order:detail' order.id %}" class="btn-review">去评价</a>
                <a href="javascript:;" class="btn-buy-again" data-id="{{ order.id }}">再次购买</a>
                {% endif %}
                <a href="{% url 'order:list' %}" class="btn-back">返回订单列表</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 取消订单
        const cancelBtn = document.querySelector('.btn-cancel');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', function() {
                if (confirm('确定要取消该订单吗？')) {
                    const orderId = this.dataset.id;
                    
                    fetch(`/api/order/cancel/${orderId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            location.reload();
                        } else {
                            alert(data.msg);
                        }
                    });
                }
            });
        }
        
        // 确认收货
        const confirmBtn = document.querySelector('.btn-confirm');
        if (confirmBtn) {
            confirmBtn.addEventListener('click', function() {
                if (confirm('确认已收到商品？')) {
                    const orderId = this.dataset.id;
                    
                    fetch(`/api/order/confirm/${orderId}/`, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': getCookie('csrftoken')
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            location.reload();
                        } else {
                            alert(data.msg);
                        }
                    });
                }
            });
        }
        
        // 再次购买
        const rebuyBtn = document.querySelector('.btn-buy-again');
        if (rebuyBtn) {
            rebuyBtn.addEventListener('click', function() {
                const orderId = this.dataset.id;
                
                fetch(`/api/order/rebuy/${orderId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        window.location.href = '{% url "order:cart" %}';
                    } else {
                        alert(data.msg);
                    }
                });
            });
        }
        
        // 获取Cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });
</script>
{% endblock %} 