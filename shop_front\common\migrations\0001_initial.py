# Generated by Django 5.0.3 on 2025-06-06 14:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='配置键')),
                ('value', models.TextField(verbose_name='配置值')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='配置描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
                'ordering': ['key'],
                'indexes': [models.Index(fields=['key'], name='common_syst_key_733e35_idx'), models.Index(fields=['is_active'], name='common_syst_is_acti_498ecb_idx')],
            },
        ),
        migrations.CreateModel(
            name='SharedCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('name', models.CharField(max_length=100, verbose_name='分类名称')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='分类编码')),
                ('level', models.PositiveIntegerField(default=1, verbose_name='分类层级')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children', to='common.sharedcategory', verbose_name='父分类')),
            ],
            options={
                'verbose_name': '商品分类',
                'verbose_name_plural': '商品分类',
                'ordering': ['sort_order', 'code'],
                'indexes': [models.Index(fields=['code'], name='common_shar_code_8dbc8a_idx'), models.Index(fields=['parent', 'is_active'], name='common_shar_parent__185e8c_idx')],
            },
        ),
    ]
