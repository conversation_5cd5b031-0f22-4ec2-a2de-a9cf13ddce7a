{% extends 'base.html' %}

{% block title %}订单支付 - MARS BUY{% endblock %}

{% load static %}

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .payment-container {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 40px;
        margin: 20px 0;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
    }

    .payment-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .payment-title {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .payment-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 0;
    }

    .order-info {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border-left: 5px solid #dc3545;
    }

    .order-info h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 20px;
        font-weight: 600;
    }

    .order-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
    }

    .order-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px dashed #ddd;
    }

    .order-detail-item:last-child {
        border-bottom: none;
    }

    .order-detail-label {
        font-weight: 600;
        color: #555;
    }

    .order-detail-value {
        color: #333;
        font-weight: 500;
    }

    .order-amount {
        color: #dc3545 !important;
        font-size: 24px;
        font-weight: 700;
    }

    .payment-methods {
        margin-bottom: 30px;
    }

    .payment-methods h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 20px;
        font-weight: 600;
    }

    .payment-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    .payment-option {
        background: white;
        border: 2px solid #e0e0e0;
        border-radius: 15px;
        padding: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .payment-option:hover {
        border-color: #dc3545;
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(220,53,69,0.15);
    }

    .payment-option.selected {
        border-color: #dc3545;
        background: linear-gradient(135deg, rgba(220,53,69,0.05) 0%, rgba(231,76,60,0.05) 100%);
    }

    .payment-option-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .payment-icon {
        width: 50px;
        height: 50px;
        margin-right: 15px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
    }

    .alipay-icon {
        background: linear-gradient(135deg, #1677ff 0%, #00a6fb 100%);
    }

    .wechat-icon {
        background: linear-gradient(135deg, #07c160 0%, #00d976 100%);
    }

    .bank-icon {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }

    .payment-name {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .payment-desc {
        font-size: 14px;
        color: #666;
        line-height: 1.4;
    }

    .payment-actions {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-top: 40px;
    }

    .btn {
        padding: 15px 40px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        box-shadow: 0 8px 25px rgba(108,117,125,0.3);
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(108,117,125,0.4);
        color: white;
        text-decoration: none;
    }

    .security-info {
        background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
        border-radius: 15px;
        padding: 20px;
        margin-top: 30px;
        text-align: center;
    }

    .security-info i {
        font-size: 24px;
        color: #2196f3;
        margin-bottom: 10px;
    }

    .security-info p {
        color: #666;
        margin: 0;
        font-size: 14px;
        line-height: 1.6;
    }

    /* 支付密码样式 */
    .password-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 30px;
        margin: 30px 0;
        border-left: 5px solid #28a745;
    }

    .password-input-container {
        display: flex;
        justify-content: center;
        margin: 20px 0;
        position: relative;
    }

    .password-display {
        display: flex;
        gap: 15px;
        background: white;
        padding: 15px 20px;
        border-radius: 10px;
        border: 2px solid #e0e0e0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .password-dot {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #ddd;
        background: #f8f9fa;
        transition: all 0.3s ease;
        position: relative;
    }

    .password-dot.filled {
        background: #28a745;
        border-color: #28a745;
    }

    .password-dot.filled::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 8px;
        height: 8px;
        background: white;
        border-radius: 50%;
    }

    .number-keyboard {
        max-width: 300px;
        margin: 20px auto;
    }

    .keyboard-row {
        display: flex;
        gap: 10px;
        margin-bottom: 10px;
        justify-content: center;
    }

    .num-btn {
        width: 80px;
        height: 60px;
        border: 2px solid #e0e0e0;
        background: white;
        border-radius: 10px;
        font-size: 20px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .num-btn:hover {
        background: #f8f9fa;
        border-color: #28a745;
        transform: translateY(-2px);
    }

    .num-btn:active {
        transform: translateY(0);
        background: #e9ecef;
    }

    .cancel-btn {
        background: #6c757d !important;
        color: white !important;
        font-size: 14px !important;
    }

    .cancel-btn:hover {
        background: #5a6268 !important;
    }

    .delete-btn {
        background: #dc3545 !important;
        color: white !important;
    }

    .delete-btn:hover {
        background: #c82333 !important;
    }

    .password-actions {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .password-actions .btn {
        min-width: 200px;
    }

    @media (max-width: 768px) {
        .payment-container {
            padding: 20px;
            margin: 10px;
        }

        .payment-options {
            grid-template-columns: 1fr;
        }

        .payment-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }

        .number-keyboard {
            max-width: 280px;
        }

        .num-btn {
            width: 70px;
            height: 50px;
            font-size: 18px;
        }

        .keyboard-row {
            gap: 8px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="payment-container">
        <div class="payment-header">
            <h1 class="payment-title">
                <i class="fas fa-credit-card"></i>
                订单支付
            </h1>
            <p class="payment-subtitle">请选择支付方式完成订单支付</p>
        </div>

        <!-- 订单信息 -->
        <div class="order-info">
            <h3><i class="fas fa-receipt"></i> 订单信息</h3>
            <div class="order-details">
                <div class="order-detail-item">
                    <span class="order-detail-label">订单号：</span>
                    <span class="order-detail-value">{{ order.order_number }}</span>
                </div>
                <div class="order-detail-item">
                    <span class="order-detail-label">创建时间：</span>
                    <span class="order-detail-value">{{ order.created_time|date:"Y-m-d H:i:s" }}</span>
                </div>
                <div class="order-detail-item">
                    <span class="order-detail-label">商品数量：</span>
                    <span class="order-detail-value">{{ order.items.count }} 件</span>
                </div>
                <div class="order-detail-item">
                    <span class="order-detail-label">应付金额：</span>
                    <span class="order-detail-value order-amount">¥{{ order.pay_amount }}</span>
                </div>
            </div>
        </div>

        <!-- 支付方式选择 -->
        <div class="payment-methods">
            <h3><i class="fas fa-wallet"></i> 选择支付方式</h3>
            <div class="payment-options">
                <!-- 支付宝支付 -->
                <div class="payment-option" data-method="alipay">
                    <div class="payment-option-header">
                        <div class="payment-icon alipay-icon">
                            <i class="fab fa-alipay"></i>
                        </div>
                        <div>
                            <div class="payment-name">支付宝支付</div>
                            <div class="payment-desc">使用支付宝账户安全快捷支付</div>
                        </div>
                    </div>
                </div>

                <!-- 微信支付 -->
                <div class="payment-option" data-method="wechat">
                    <div class="payment-option-header">
                        <div class="payment-icon wechat-icon">
                            <i class="fab fa-weixin"></i>
                        </div>
                        <div>
                            <div class="payment-name">微信支付</div>
                            <div class="payment-desc">使用微信账户便捷支付</div>
                        </div>
                    </div>
                </div>

                <!-- 银行卡支付 -->
                <div class="payment-option" data-method="bank">
                    <div class="payment-option-header">
                        <div class="payment-icon bank-icon">
                            <i class="fas fa-university"></i>
                        </div>
                        <div>
                            <div class="payment-name">银行卡支付</div>
                            <div class="payment-desc">支持各大银行储蓄卡和信用卡</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支付密码输入区域 -->
        <div id="passwordSection" class="password-section" style="display: none;">
            <h3 class="section-title">
                <i class="fas fa-shield-alt"></i>
                输入支付密码
            </h3>
            <div class="password-input-container">
                <div class="password-display">
                    <span class="password-dot" data-index="0"></span>
                    <span class="password-dot" data-index="1"></span>
                    <span class="password-dot" data-index="2"></span>
                    <span class="password-dot" data-index="3"></span>
                    <span class="password-dot" data-index="4"></span>
                    <span class="password-dot" data-index="5"></span>
                </div>
                <input type="password" id="paymentPassword" maxlength="6" style="opacity: 0; position: absolute; z-index: -1;">
            </div>
            <div class="number-keyboard">
                <div class="keyboard-row">
                    <button class="num-btn" data-num="1">1</button>
                    <button class="num-btn" data-num="2">2</button>
                    <button class="num-btn" data-num="3">3</button>
                </div>
                <div class="keyboard-row">
                    <button class="num-btn" data-num="4">4</button>
                    <button class="num-btn" data-num="5">5</button>
                    <button class="num-btn" data-num="6">6</button>
                </div>
                <div class="keyboard-row">
                    <button class="num-btn" data-num="7">7</button>
                    <button class="num-btn" data-num="8">8</button>
                    <button class="num-btn" data-num="9">9</button>
                </div>
                <div class="keyboard-row">
                    <button class="num-btn cancel-btn">取消</button>
                    <button class="num-btn" data-num="0">0</button>
                    <button class="num-btn delete-btn">
                        <i class="fas fa-backspace"></i>
                    </button>
                </div>
            </div>
            <div class="password-actions">
                <button id="confirmPayBtn" class="btn btn-primary" disabled>
                    <i class="fas fa-check"></i>
                    确认支付 ¥{{ order.pay_amount }}
                </button>
            </div>
        </div>

        <!-- 支付按钮 -->
        <div class="payment-actions">
            <a href="{% url 'order:detail' order.order_number %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                返回订单
            </a>
            <button id="payBtn" class="btn btn-primary" disabled>
                <i class="fas fa-lock"></i>
                立即支付 ¥{{ order.pay_amount }}
            </button>
        </div>

        <!-- 安全提示 -->
        <div class="security-info">
            <i class="fas fa-shield-alt"></i>
            <p>您的支付信息将通过SSL加密传输，MARS BUY承诺保护您的隐私和资金安全</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentOptions = document.querySelectorAll('.payment-option');
    const payBtn = document.getElementById('payBtn');
    const passwordSection = document.getElementById('passwordSection');
    const confirmPayBtn = document.getElementById('confirmPayBtn');
    const passwordInput = document.getElementById('paymentPassword');
    const passwordDots = document.querySelectorAll('.password-dot');
    const numBtns = document.querySelectorAll('.num-btn');

    let selectedMethod = null;
    let currentPassword = '';

    // 支付方式选择
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            // 移除其他选中状态
            paymentOptions.forEach(opt => opt.classList.remove('selected'));
            // 添加选中状态
            this.classList.add('selected');
            selectedMethod = this.dataset.method;

            // 启用支付按钮
            payBtn.disabled = false;
            payBtn.innerHTML = `<i class="fas fa-lock"></i> 立即支付 ¥{{ order.pay_amount }}`;
        });
    });

    // 支付按钮点击 - 显示密码输入
    payBtn.addEventListener('click', function() {
        if (!selectedMethod) {
            showSystemMessage('请选择支付方式', '支付提示', 'warning');
            return;
        }

        // 显示支付密码输入区域
        passwordSection.style.display = 'block';
        this.style.display = 'none';

        // 滚动到密码输入区域
        passwordSection.scrollIntoView({ behavior: 'smooth' });

        // 重置密码输入
        currentPassword = '';
        updatePasswordDisplay();
    });

    // 数字键盘事件
    numBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const num = this.dataset.num;

            if (this.classList.contains('cancel-btn')) {
                // 取消支付
                passwordSection.style.display = 'none';
                payBtn.style.display = 'inline-flex';
                currentPassword = '';
                updatePasswordDisplay();
            } else if (this.classList.contains('delete-btn')) {
                // 删除最后一位
                if (currentPassword.length > 0) {
                    currentPassword = currentPassword.slice(0, -1);
                    updatePasswordDisplay();
                }
            } else if (num && currentPassword.length < 6) {
                // 输入数字
                currentPassword += num;
                updatePasswordDisplay();

                // 如果输入满6位，自动提交
                if (currentPassword.length === 6) {
                    setTimeout(() => {
                        processPayment();
                    }, 500);
                }
            }
        });
    });

    // 更新密码显示
    function updatePasswordDisplay() {
        passwordDots.forEach((dot, index) => {
            if (index < currentPassword.length) {
                dot.classList.add('filled');
            } else {
                dot.classList.remove('filled');
            }
        });

        // 更新确认按钮状态
        confirmPayBtn.disabled = currentPassword.length !== 6;
    }

    // 确认支付按钮
    confirmPayBtn.addEventListener('click', function() {
        if (currentPassword.length === 6) {
            processPayment();
        }
    });

    // 处理支付
    async function processPayment() {
        // 显示支付处理中
        confirmPayBtn.disabled = true;
        confirmPayBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证密码中...';

        // 验证支付密码
        const isValidPassword = await validatePaymentPassword(currentPassword);

        if (isValidPassword) {
            // 密码验证成功，继续支付流程
            console.log('密码验证成功，开始支付流程');
            confirmPayBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 支付处理中...';

            // 根据选择的支付方式处理
            console.log('选择的支付方式:', selectedMethod);

            switch(selectedMethod) {
                case 'alipay':
                    console.log('跳转到支付宝支付');
                    // 跳转到支付宝支付
                    window.location.href = "{% url 'payment:alipay_pay' order.order_number %}";
                    break;
                case 'wechat':
                    console.log('跳转到微信支付成功页面');
                    // 模拟微信支付成功
                    window.location.href = "{% url 'payment:simulate_success' order.order_number %}";
                    break;
                case 'bank':
                    console.log('跳转到银行卡支付成功页面');
                    // 模拟银行卡支付成功
                    window.location.href = "{% url 'payment:simulate_success' order.order_number %}";
                    break;
                default:
                    console.log('支付方式错误:', selectedMethod);
                    showSystemMessage('支付方式错误', '支付错误', 'error');
                    resetPaymentForm();
            }
        } else {
            // 密码错误
            console.log('密码验证失败');
            showPasswordError();
        }
    }

    // 验证支付密码
    async function validatePaymentPassword(password) {
        console.log('开始验证密码:', password);
        console.log('订单号:', '{{ order.order_number }}');

        try {
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            console.log('CSRF Token元素:', csrfToken);
            console.log('CSRF Token值:', csrfToken ? csrfToken.value : 'null');

            const requestData = {
                password: password,
                order_number: '{{ order.order_number }}'
            };
            console.log('请求数据:', requestData);

            const response = await fetch('/payment/verify-password/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken ? csrfToken.value : ''
                },
                body: JSON.stringify(requestData)
            });

            console.log('响应状态:', response.status);
            console.log('响应头:', response.headers);

            const data = await response.json();
            console.log('响应数据:', data);

            return data.success;
        } catch (error) {
            console.error('密码验证错误:', error);
            showSystemMessage('密码验证失败: ' + error.message, '验证失败', 'error');
            return false;
        }
    }

    // 显示密码错误
    function showPasswordError() {
        // 密码框震动效果
        passwordSection.style.animation = 'shake 0.5s';
        setTimeout(() => {
            passwordSection.style.animation = '';
        }, 500);

        // 清空密码
        currentPassword = '';
        updatePasswordDisplay();

        // 显示错误提示
        showSystemMessage('支付密码错误，请重新输入', '验证失败', 'error');
    }

    // 模拟支付成功
    function simulatePaymentSuccess() {
        // 显示成功消息
        showSystemMessage('支付成功！', '支付结果', 'success');

        // 跳转到支付成功页面
        window.location.href = "{% url 'payment:success' order.order_number %}";
    }

    // 重置支付表单
    function resetPaymentForm() {
        passwordSection.style.display = 'none';
        payBtn.style.display = 'inline-flex';
        payBtn.disabled = false;
        payBtn.innerHTML = `<i class="fas fa-lock"></i> 立即支付 ¥{{ order.pay_amount }}`;
        currentPassword = '';
        updatePasswordDisplay();
        confirmPayBtn.disabled = true;
        confirmPayBtn.innerHTML = '<i class="fas fa-check"></i> 确认支付 ¥{{ order.pay_amount }}';
    }
});

// 添加震动动画
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
