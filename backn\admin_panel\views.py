from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.hashers import make_password
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
import json
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from django.core.paginator import Paginator

from users.models import User
from goods.models import Product, Category
from orders.models import Order, OrderItem
from common.models import Settings
from .models import AdminLog
from common.utils import get_client_ip, is_admin
from .forms import AdminRegisterForm

# ==================== 用户管理功能 ====================

@login_required
@user_passes_test(lambda u: u.is_staff)
def admin_profile(request):
    """管理员个人中心"""
    user = request.user

    # 获取管理员的操作统计
    from .models import AdminLog

    # 最近30天的操作日志
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_logs = AdminLog.objects.filter(
        admin=user,
        created_time__gte=thirty_days_ago
    ).order_by('-created_time')[:10]

    # 统计信息
    stats = {
        'total_logs': AdminLog.objects.filter(admin=user).count(),
        'recent_logs_count': recent_logs.count(),
        'login_count': AdminLog.objects.filter(admin=user, action='登录').count(),
        'last_login': user.last_login,
    }

    return render(request, 'admin_panel/admin_profile.html', {
        'user': user,
        'recent_logs': recent_logs,
        'stats': stats
    })


@login_required
@user_passes_test(lambda u: u.is_staff)
def update_admin_profile(request):
    """更新管理员个人信息"""
    if request.method == 'POST':
        try:
            user = request.user

            # 更新基本信息
            user.first_name = request.POST.get('first_name', '').strip()
            user.email = request.POST.get('email', '').strip()
            phone = request.POST.get('phone', '').strip()

            # 验证邮箱格式
            if user.email:
                from django.core.validators import validate_email
                try:
                    validate_email(user.email)
                except:
                    return JsonResponse({
                        'success': False,
                        'message': '邮箱格式不正确'
                    })

            # 检查邮箱是否已被其他用户使用
            if user.email and User.objects.filter(email=user.email).exclude(id=user.id).exists():
                return JsonResponse({
                    'success': False,
                    'message': '该邮箱已被其他用户使用'
                })

            # 更新手机号（如果用户模型有phone字段）
            if hasattr(user, 'phone'):
                user.phone = phone

            user.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=user,
                action='修改个人信息',
                description=f'更新了个人信息',
                ip_address=get_client_ip(request)
            )

            return JsonResponse({
                'success': True,
                'message': '个人信息更新成功'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'更新失败：{str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': '请求方法不正确'
    })


@login_required
@user_passes_test(lambda u: u.is_staff)
def change_admin_password(request):
    """修改管理员密码"""
    if request.method == 'POST':
        try:
            user = request.user
            old_password = request.POST.get('old_password', '')
            new_password = request.POST.get('new_password', '')
            confirm_password = request.POST.get('confirm_password', '')

            # 验证原密码
            if not user.check_password(old_password):
                return JsonResponse({
                    'success': False,
                    'message': '原密码不正确'
                })

            # 验证新密码
            if len(new_password) < 6:
                return JsonResponse({
                    'success': False,
                    'message': '新密码长度不能少于6位'
                })

            # 验证确认密码
            if new_password != confirm_password:
                return JsonResponse({
                    'success': False,
                    'message': '两次输入的密码不一致'
                })

            # 更新密码
            user.set_password(new_password)
            user.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=user,
                action='修改密码',
                description='修改了登录密码',
                ip_address=get_client_ip(request)
            )

            return JsonResponse({
                'success': True,
                'message': '密码修改成功，请重新登录'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'密码修改失败：{str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': '请求方法不正确'
    })


@login_required
@user_passes_test(lambda u: u.is_staff)
def upload_admin_avatar(request):
    """上传管理员头像"""
    if request.method == 'POST':
        try:
            if 'avatar' not in request.FILES:
                return JsonResponse({
                    'success': False,
                    'message': '请选择头像文件'
                })

            avatar_file = request.FILES['avatar']

            # 验证文件类型
            allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
            if avatar_file.content_type not in allowed_types:
                return JsonResponse({
                    'success': False,
                    'message': '只支持 JPEG、PNG、GIF、WebP 格式的图片'
                })

            # 验证文件大小（5MB）
            if avatar_file.size > 5 * 1024 * 1024:
                return JsonResponse({
                    'success': False,
                    'message': '头像文件大小不能超过5MB'
                })

            # 保存头像
            user = request.user
            if hasattr(user, 'avatar'):
                user.avatar = avatar_file
                user.save()

                # 记录操作日志
                AdminLog.objects.create(
                    admin=user,
                    action='更新头像',
                    description='上传了新的头像',
                    ip_address=get_client_ip(request)
                )

                return JsonResponse({
                    'success': True,
                    'message': '头像上传成功',
                    'avatar_url': user.avatar.url if user.avatar else None
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': '用户模型不支持头像功能'
                })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'头像上传失败：{str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': '请求方法不正确'
    })


@login_required
@user_passes_test(lambda u: u.is_staff)
def admin_profile_logout(request):
    """管理员个人中心注销"""
    if request.method == 'POST':
        try:
            # 记录注销日志
            AdminLog.objects.create(
                admin=request.user,
                action='注销账户',
                description=f'管理员 {request.user.username} 从个人中心注销',
                ip_address=get_client_ip(request)
            )

            logout(request)

            return JsonResponse({
                'success': True,
                'message': '注销成功'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'注销失败：{str(e)}'
            })

    return JsonResponse({
        'success': False,
        'message': '请求方法不正确'
    })







def admin_login(request):
    """管理员登录"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')

        user = authenticate(request, username=username, password=password)
        if user:
            # 检查用户是否为管理员
            if user.is_staff or user.is_superuser:
                login(request, user)
                # 记录登录日志
                AdminLog.objects.create(
                    admin=user,
                    action='登录',
                    change_message=f'管理员 {username} 登录系统',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                return redirect('admin_panel:dashboard')
            else:
                messages.error(request, '普通用户请使用前台用户登录入口！')
        else:
            messages.error(request, '用户名或密码错误')

    return render(request, 'admin_panel/login.html')

def admin_register(request):
    """管理员注册"""
    if request.method == 'POST':
        form = AdminRegisterForm(request.POST, request.FILES)
        if form.is_valid():
            user = form.save()

            # 记录注册日志
            AdminLog.objects.create(
                admin=user,  # 注册成功的管理员
                action='管理员注册',
                change_message=f'新管理员 {user.username} 注册成功',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, '管理员账号注册成功！您现在可以登录使用后台管理系统。')
            return redirect('admin_panel:login')
    else:
        form = AdminRegisterForm()

    return render(request, 'admin_panel/register.html', {'form': form})

def admin_logout(request):
    """管理员退出"""
    if request.user.is_authenticated:
        # 记录退出日志
        AdminLog.objects.create(
            admin=request.user,
            action='退出',
            change_message=f'管理员 {request.user.username} 退出系统',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )
    logout(request)
    return redirect('admin_panel:login')

@login_required
@user_passes_test(is_admin)
def dashboard(request):
    """管理员首页"""
    # 获取统计数据
    today = timezone.now().date()
    yesterday = today - timedelta(days=1)
    last_30_days = today - timedelta(days=30)

    # 今日数据
    today_orders = Order.objects.filter(created_time__date=today)
    today_sales = today_orders.aggregate(total=Sum('final_amount'))['total'] or 0
    today_order_count = today_orders.count()

    # 昨日数据
    yesterday_orders = Order.objects.filter(created_time__date=yesterday)
    yesterday_sales = yesterday_orders.aggregate(total=Sum('final_amount'))['total'] or 0
    yesterday_order_count = yesterday_orders.count()

    # 总体数据
    total_users = User.objects.count()
    total_products = Product.objects.count()
    total_orders = Order.objects.count()
    total_sales = Order.objects.filter(status='paid').aggregate(total=Sum('final_amount'))['total'] or 0

    # 最近30天销售趋势
    sales_data = []
    for i in range(30):
        date = today - timedelta(days=i)
        daily_sales = Order.objects.filter(
            created_time__date=date,
            status='paid'
        ).aggregate(total=Sum('final_amount'))['total'] or 0
        sales_data.append({
            'date': date.strftime('%m-%d'),
            'sales': float(daily_sales)
        })
    sales_data.reverse()

    # 热门商品
    hot_products = Product.objects.filter(is_active=True).order_by('-sales')[:5]

    # 最新订单
    recent_orders = Order.objects.select_related('user').order_by('-created_time')[:10]

    context = {
        'today_sales': today_sales,
        'today_order_count': today_order_count,
        'yesterday_sales': yesterday_sales,
        'yesterday_order_count': yesterday_order_count,
        'total_users': total_users,
        'total_products': total_products,
        'total_orders': total_orders,
        'total_sales': total_sales,
        'sales_data': json.dumps(sales_data),
        'hot_products': hot_products,
        'recent_orders': recent_orders,
    }

    return render(request, 'admin_panel/dashboard.html', context)

@login_required
@user_passes_test(is_admin)
def statistics(request):
    """数据统计页面"""
    # 用户统计
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    vip_users = User.objects.filter(is_vip=True).count()

    # 商品统计
    total_products = Product.objects.count()
    active_products = Product.objects.filter(is_active=True).count()
    hot_products = Product.objects.filter(is_hot=True).count()

    # 订单统计
    total_orders = Order.objects.count()
    paid_orders = Order.objects.filter(status='paid').count()
    pending_orders = Order.objects.filter(status='pending').count()

    # 销售统计
    total_sales = Order.objects.filter(status='paid').aggregate(
        total=Sum('final_amount'))['total'] or 0

    context = {
        'total_users': total_users,
        'active_users': active_users,
        'vip_users': vip_users,
        'total_products': total_products,
        'active_products': active_products,
        'hot_products': hot_products,
        'total_orders': total_orders,
        'paid_orders': paid_orders,
        'pending_orders': pending_orders,
        'total_sales': total_sales,
    }

    return render(request, 'admin_panel/statistics.html', context)

@login_required
@user_passes_test(is_admin)
def sales_chart(request):
    """销售图表数据API"""
    days = int(request.GET.get('days', 30))
    today = timezone.now().date()

    sales_data = []
    for i in range(days):
        date = today - timedelta(days=i)
        daily_sales = Order.objects.filter(
            created_time__date=date,
            status='paid'
        ).aggregate(total=Sum('final_amount'))['total'] or 0

        daily_orders = Order.objects.filter(
            created_time__date=date
        ).count()

        sales_data.append({
            'date': date.strftime('%Y-%m-%d'),
            'sales': float(daily_sales),
            'orders': daily_orders
        })

    sales_data.reverse()
    return JsonResponse({'data': sales_data})

@login_required
@user_passes_test(is_admin)
def system_settings(request):
    """系统设置页面"""
    try:
        settings = Settings.objects.first()
        if not settings:
            settings = Settings.objects.create()
    except:
        settings = None

    return render(request, 'admin_panel/settings.html', {'settings': settings})

@login_required
@user_passes_test(is_admin)
def update_settings(request):
    """更新系统设置"""
    if request.method == 'POST':
        try:
            settings, created = Settings.objects.get_or_create(id=1)

            # 更新基本设置
            settings.site_name = request.POST.get('site_name', '')
            settings.site_description = request.POST.get('site_description', '')
            settings.contact_email = request.POST.get('contact_email', '')
            settings.contact_phone = request.POST.get('contact_phone', '')

            # 更新邮件设置
            settings.smtp_host = request.POST.get('smtp_host', '')
            settings.smtp_port = int(request.POST.get('smtp_port', 25))
            settings.smtp_user = request.POST.get('smtp_user', '')
            settings.smtp_password = request.POST.get('smtp_password', '')
            settings.smtp_ssl = request.POST.get('smtp_ssl') == 'on'

            # 更新其他设置
            settings.order_timeout = int(request.POST.get('order_timeout', 30))
            settings.maintenance_mode = request.POST.get('maintenance_mode') == 'on'
            settings.maintenance_message = request.POST.get('maintenance_message', '')

            settings.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=request.user,
                action='更新系统设置',
                change_message='更新了系统配置信息',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, '系统设置更新成功')
        except Exception as e:
            messages.error(request, f'更新失败：{str(e)}')

    return redirect('admin_panel:settings')

@login_required
@user_passes_test(is_admin)
def admin_logs(request):
    """管理员操作日志"""
    logs = AdminLog.objects.select_related('admin').order_by('-created_time')

    # 分页
    paginator = Paginator(logs, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'admin_panel/logs.html', {'page_obj': page_obj})










