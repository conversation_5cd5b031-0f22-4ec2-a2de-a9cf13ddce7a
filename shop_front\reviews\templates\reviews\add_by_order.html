{% extends 'base.html' %}
{% load static %}

{% block title %}订单评价 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    .review-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #f8f9fa;
        min-height: 100vh;
    }

    .review-header {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .review-title {
        font-size: 24px;
        font-weight: bold;
        color: #333;
        margin-bottom: 10px;
    }

    .order-info {
        color: #666;
        font-size: 14px;
    }

    .product-review-card {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .product-info {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .product-image {
        width: 80px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
        margin-right: 15px;
    }

    .product-details h4 {
        margin: 0 0 5px 0;
        font-size: 16px;
        color: #333;
    }

    .product-price {
        color: #e74c3c;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .product-quantity {
        color: #666;
        font-size: 14px;
    }

    .rating-section {
        margin-bottom: 20px;
    }

    .rating-label {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
    }

    .star-rating {
        display: flex;
        gap: 5px;
        margin-bottom: 10px;
    }

    .star {
        font-size: 24px;
        color: #ddd;
        cursor: pointer;
        transition: color 0.2s;
    }

    .star.active,
    .star:hover {
        color: #ffc107;
    }

    .content-section {
        margin-bottom: 20px;
    }

    .content-label {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
    }

    .review-textarea {
        width: 100%;
        min-height: 100px;
        padding: 12px;
        border: 1px solid #ddd;
        border-radius: 6px;
        resize: vertical;
        font-family: inherit;
    }

    .review-textarea:focus {
        outline: none;
        border-color: #e74c3c;
        box-shadow: 0 0 0 2px rgba(231, 76, 60, 0.1);
    }

    .image-upload-section {
        margin-bottom: 20px;
    }

    .image-upload-label {
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
    }

    .image-upload-area {
        border: 2px dashed #ddd;
        border-radius: 6px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: border-color 0.2s;
    }

    .image-upload-area:hover {
        border-color: #e74c3c;
    }

    .image-upload-area.dragover {
        border-color: #e74c3c;
        background-color: rgba(231, 76, 60, 0.05);
    }

    .upload-icon {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 10px;
    }

    .upload-text {
        color: #666;
        margin-bottom: 5px;
    }

    .upload-hint {
        color: #999;
        font-size: 12px;
    }

    .image-preview {
        display: flex;
        gap: 10px;
        margin-top: 10px;
        flex-wrap: wrap;
    }

    .preview-item {
        position: relative;
        width: 80px;
        height: 80px;
    }

    .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 6px;
        border: 1px solid #ddd;
    }

    .remove-image {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #e74c3c;
        color: white;
        border: none;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .anonymous-section {
        margin-bottom: 20px;
    }

    .anonymous-checkbox {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .anonymous-checkbox input[type="checkbox"] {
        width: 16px;
        height: 16px;
    }

    .submit-section {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }

    .submit-btn {
        background: #e74c3c;
        color: white;
        border: none;
        padding: 12px 40px;
        border-radius: 6px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .submit-btn:hover {
        background: #c0392b;
    }

    .submit-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
    }

    .cancel-btn {
        background: #6c757d;
        color: white;
        border: none;
        padding: 12px 40px;
        border-radius: 6px;
        font-size: 16px;
        cursor: pointer;
        margin-right: 15px;
        text-decoration: none;
        display: inline-block;
        transition: background-color 0.2s;
    }

    .cancel-btn:hover {
        background: #5a6268;
        color: white;
        text-decoration: none;
    }

    .already-reviewed {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        padding: 15px;
        text-align: center;
        color: #6c757d;
    }
</style>
{% endblock %}

{% block content %}
<div class="review-container">
    <div class="review-header">
        <div class="review-title">
            <i class="fas fa-star"></i> 订单评价
        </div>
        <div class="order-info">
            订单号：{{ order.order_number }} | 下单时间：{{ order.created_time|date:"Y-m-d H:i" }}
        </div>
    </div>

    <form id="reviewForm" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        
        {% for item in order_items %}
        <div class="product-review-card" data-product-id="{{ item.product.id }}">
            <div class="product-info">
                <img src="{% if item.product.main_image %}{{ item.product.main_image.url }}{% else %}{% static 'images/default-product.jpg' %}{% endif %}" 
                     alt="{{ item.product.name }}" class="product-image">
                <div class="product-details">
                    <h4>{{ item.product.name }}</h4>
                    <div class="product-price">¥{{ item.price }}</div>
                    <div class="product-quantity">数量：{{ item.quantity }}</div>
                </div>
            </div>

            {% if item.is_reviewed %}
            <div class="already-reviewed">
                <i class="fas fa-check-circle"></i>
                该商品已评价
            </div>
            {% else %}
            <div class="rating-section">
                <div class="rating-label">商品评分</div>
                <div class="star-rating" data-product="{{ item.product.id }}">
                    <span class="star" data-rating="1">★</span>
                    <span class="star" data-rating="2">★</span>
                    <span class="star" data-rating="3">★</span>
                    <span class="star" data-rating="4">★</span>
                    <span class="star" data-rating="5">★</span>
                </div>
                <input type="hidden" name="score_{{ item.product.id }}" id="score_{{ item.product.id }}" value="">
            </div>

            <div class="content-section">
                <div class="content-label">评价内容</div>
                <textarea name="content_{{ item.product.id }}" 
                         class="review-textarea" 
                         placeholder="请分享您对这件商品的使用感受..."></textarea>
            </div>

            <div class="image-upload-section">
                <div class="image-upload-label">上传图片（可选，最多5张）</div>
                <div class="image-upload-area" onclick="document.getElementById('images_{{ item.product.id }}').click()">
                    <div class="upload-icon">
                        <i class="fas fa-cloud-upload-alt"></i>
                    </div>
                    <div class="upload-text">点击上传图片</div>
                    <div class="upload-hint">支持 JPG、PNG 格式，单张图片不超过 5MB</div>
                </div>
                <input type="file" 
                       id="images_{{ item.product.id }}" 
                       name="images_{{ item.product.id }}" 
                       multiple 
                       accept="image/*" 
                       style="display: none;"
                       onchange="previewImages(this, {{ item.product.id }})">
                <div class="image-preview" id="preview_{{ item.product.id }}"></div>
            </div>

            <div class="anonymous-section">
                <label class="anonymous-checkbox">
                    <input type="checkbox" name="anonymous_{{ item.product.id }}">
                    <span>匿名评价</span>
                </label>
            </div>
            {% endif %}
        </div>
        {% endfor %}

        <div class="submit-section">
            <a href="{% url 'order:list' %}" class="cancel-btn">
                <i class="fas fa-arrow-left"></i> 返回订单
            </a>
            <button type="submit" class="submit-btn" id="submitBtn">
                <i class="fas fa-paper-plane"></i> 提交评价
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 星级评分功能
    const starRatings = document.querySelectorAll('.star-rating');
    
    starRatings.forEach(rating => {
        const stars = rating.querySelectorAll('.star');
        const productId = rating.dataset.product;
        const hiddenInput = document.getElementById(`score_${productId}`);
        
        stars.forEach((star, index) => {
            star.addEventListener('click', function() {
                const ratingValue = index + 1;
                hiddenInput.value = ratingValue;
                
                // 更新星星显示
                stars.forEach((s, i) => {
                    if (i < ratingValue) {
                        s.classList.add('active');
                    } else {
                        s.classList.remove('active');
                    }
                });
            });
            
            star.addEventListener('mouseover', function() {
                const ratingValue = index + 1;
                stars.forEach((s, i) => {
                    if (i < ratingValue) {
                        s.style.color = '#ffc107';
                    } else {
                        s.style.color = '#ddd';
                    }
                });
            });
        });
        
        rating.addEventListener('mouseleave', function() {
            const currentRating = parseInt(hiddenInput.value) || 0;
            stars.forEach((s, i) => {
                if (i < currentRating) {
                    s.style.color = '#ffc107';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });
    
    // 表单提交
    document.getElementById('reviewForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = document.getElementById('submitBtn');
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
        
        const formData = new FormData(this);
        
        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('评价提交成功！');
                // 跳转到我的评价页面
                if (data.redirect_url) {
                    window.location.href = data.redirect_url;
                } else {
                    window.location.href = '{% url "reviews:my_reviews" %}';
                }
            } else {
                alert(data.error || '提交失败，请重试');
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> 提交评价';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('提交失败，请重试');
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> 提交评价';
        });
    });
});

// 图片预览功能
function previewImages(input, productId) {
    const previewContainer = document.getElementById(`preview_${productId}`);
    previewContainer.innerHTML = '';
    
    if (input.files) {
        Array.from(input.files).slice(0, 5).forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" class="preview-image" alt="预览图片">
                    <button type="button" class="remove-image" onclick="removeImage(this, ${productId}, ${index})">×</button>
                `;
                previewContainer.appendChild(previewItem);
            };
            reader.readAsDataURL(file);
        });
    }
}

// 移除图片
function removeImage(button, productId, index) {
    const input = document.getElementById(`images_${productId}`);
    const dt = new DataTransfer();
    
    Array.from(input.files).forEach((file, i) => {
        if (i !== index) {
            dt.items.add(file);
        }
    });
    
    input.files = dt.files;
    button.parentElement.remove();
}
</script>
{% endblock %}
