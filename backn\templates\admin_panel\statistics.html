{% extends 'base.html' %}

{% block title %}数据统计 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}数据统计{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <button type="button" class="btn btn-outline-primary" onclick="refreshData()">
        <i class="fas fa-sync-alt me-1"></i>刷新数据
    </button>
    <button type="button" class="btn btn-outline-success" onclick="exportReport()">
        <i class="fas fa-download me-1"></i>导出报告
    </button>
</div>
{% endblock %}

{% block content %}
<!-- 总体统计 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            总用户数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_users }}
                        </div>
                        <div class="text-xs text-muted">
                            活跃: {{ active_users }} | VIP: {{ vip_users }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            总商品数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_products }}
                        </div>
                        <div class="text-xs text-muted">
                            上架: {{ active_products }} | 热门: {{ hot_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            总订单数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_orders }}
                        </div>
                        <div class="text-xs text-muted">
                            已付款: {{ paid_orders }} | 待付款: {{ pending_orders }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            总销售额
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            ¥{{ total_sales|floatformat:2 }}
                        </div>
                        <div class="text-xs text-muted">
                            已完成订单销售额
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row">
    <!-- 销售趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">销售趋势分析</h6>
                <div class="btn-group btn-group-sm">
                    <button type="button" class="btn btn-outline-primary active" onclick="loadSalesChart(7)">7天</button>
                    <button type="button" class="btn btn-outline-primary" onclick="loadSalesChart(30)">30天</button>
                    <button type="button" class="btn btn-outline-primary" onclick="loadSalesChart(90)">90天</button>
                </div>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 订单状态分布 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">订单状态分布</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="orderStatusChart"></canvas>
                </div>
                <div class="mt-4 text-center small">
                    <span class="mr-2">
                        <i class="fas fa-circle text-primary"></i> 待付款
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-success"></i> 已付款
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-info"></i> 已发货
                    </span>
                    <span class="mr-2">
                        <i class="fas fa-circle text-warning"></i> 已完成
                    </span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 详细统计表格 -->
<div class="row">
    <!-- 用户统计 -->
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">用户统计详情</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>统计项</th>
                                <th>数量</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>总用户数</td>
                                <td>{{ total_users }}</td>
                                <td>100%</td>
                            </tr>
                            <tr>
                                <td>活跃用户</td>
                                <td>{{ active_users }}</td>
                                <td>{% widthratio active_users total_users 100 %}%</td>
                            </tr>
                            <tr>
                                <td>VIP用户</td>
                                <td>{{ vip_users }}</td>
                                <td>{% widthratio vip_users total_users 100 %}%</td>
                            </tr>
                            <tr>
                                <td>禁用用户</td>
                                <td>{{ total_users|add:"-"|add:active_users }}</td>
                                <td>{% widthratio total_users|add:"-"|add:active_users total_users 100 %}%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 商品统计 -->
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">商品统计详情</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>统计项</th>
                                <th>数量</th>
                                <th>占比</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>总商品数</td>
                                <td>{{ total_products }}</td>
                                <td>100%</td>
                            </tr>
                            <tr>
                                <td>已上架</td>
                                <td>{{ active_products }}</td>
                                <td>{% widthratio active_products total_products 100 %}%</td>
                            </tr>
                            <tr>
                                <td>热门商品</td>
                                <td>{{ hot_products }}</td>
                                <td>{% widthratio hot_products total_products 100 %}%</td>
                            </tr>
                            <tr>
                                <td>已下架</td>
                                <td>{{ total_products|add:"-"|add:active_products }}</td>
                                <td>{% widthratio total_products|add:"-"|add:active_products total_products 100 %}%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 初始化图表
let salesChart, orderStatusChart;

$(document).ready(function() {
    // 加载销售趋势图
    loadSalesChart(7);
    
    // 加载订单状态分布图
    loadOrderStatusChart();
});

function loadSalesChart(days) {
    // 更新按钮状态
    $('.btn-group .btn').removeClass('active');
    $(`button[onclick="loadSalesChart(${days})"]`).addClass('active');
    
    // 获取数据
    $.get('{% url "admin_panel:sales_chart" %}', {days: days})
    .done(function(response) {
        const ctx = document.getElementById('salesChart').getContext('2d');
        
        if (salesChart) {
            salesChart.destroy();
        }
        
        salesChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: response.data.map(item => item.date),
                datasets: [{
                    label: '销售额',
                    data: response.data.map(item => item.sales),
                    borderColor: '#dc3545',
                    backgroundColor: 'rgba(220, 53, 69, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }, {
                    label: '订单数',
                    data: response.data.map(item => item.orders),
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    borderWidth: 2,
                    fill: false,
                    tension: 0.4,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + value.toFixed(2);
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        display: true
                    }
                }
            }
        });
    });
}

function loadOrderStatusChart() {
    const ctx = document.getElementById('orderStatusChart').getContext('2d');
    
    orderStatusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['待付款', '已付款', '已发货', '已完成'],
            datasets: [{
                data: [{{ pending_orders }}, {{ paid_orders }}, 0, 0],
                backgroundColor: ['#007bff', '#28a745', '#17a2b8', '#ffc107'],
                hoverBackgroundColor: ['#0056b3', '#1e7e34', '#117a8b', '#e0a800'],
                hoverBorderColor: "rgba(234, 236, 244, 1)",
            }],
        },
        options: {
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            cutout: '80%',
        },
    });
}

function refreshData() {
    location.reload();
}

function exportReport() {
    alert('导出功能待实现');
}
</script>
{% endblock %}
