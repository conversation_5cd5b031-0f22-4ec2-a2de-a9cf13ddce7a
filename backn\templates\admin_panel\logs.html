{% extends 'base.html' %}

{% block title %}操作日志 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}操作日志{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
        <i class="fas fa-filter me-1"></i>筛选
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="?action=登录">登录日志</a></li>
        <li><a class="dropdown-item" href="?action=添加">添加操作</a></li>
        <li><a class="dropdown-item" href="?action=编辑">编辑操作</a></li>
        <li><a class="dropdown-item" href="?action=删除">删除操作</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'admin_panel:logs' %}">全部日志</a></li>
    </ul>
</div>
<button type="button" class="btn btn-outline-danger ms-2" onclick="clearOldLogs()">
    <i class="fas fa-trash me-1"></i>清理旧日志
</button>
{% endblock %}

{% block content %}
<!-- 统计信息 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ page_obj.paginator.count }}</h5>
                <p class="card-text">总日志数</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">今日</h5>
                <p class="card-text">操作记录</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">本周</h5>
                <p class="card-text">操作记录</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">本月</h5>
                <p class="card-text">操作记录</p>
            </div>
        </div>
    </div>
</div>

<!-- 日志列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="120">操作时间</th>
                        <th width="100">管理员</th>
                        <th width="100">操作类型</th>
                        <th width="120">操作对象</th>
                        <th>操作描述</th>
                        <th width="120">IP地址</th>
                        <th width="80">详情</th>
                    </tr>
                </thead>
                <tbody>
                    {% for log in page_obj %}
                    <tr>
                        <td>
                            <div class="small">{{ log.created_time|date:"m-d H:i" }}</div>
                            <div class="text-muted" style="font-size: 11px;">{{ log.created_time|date:"Y" }}</div>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-2" 
                                     style="width: 30px; height: 30px;">
                                    <i class="fas fa-user text-white" style="font-size: 12px;"></i>
                                </div>
                                <div>
                                    <div class="fw-bold small">{{ log.admin.username }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if log.action == '登录' %}
                                <span class="badge bg-success">{{ log.action }}</span>
                            {% elif log.action == '退出' %}
                                <span class="badge bg-secondary">{{ log.action }}</span>
                            {% elif '添加' in log.action %}
                                <span class="badge bg-primary">{{ log.action }}</span>
                            {% elif '编辑' in log.action or '更新' in log.action %}
                                <span class="badge bg-warning">{{ log.action }}</span>
                            {% elif '删除' in log.action %}
                                <span class="badge bg-danger">{{ log.action }}</span>
                            {% else %}
                                <span class="badge bg-info">{{ log.action }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if log.content_type and log.object_repr %}
                                <div class="small">
                                    <strong>{{ log.content_type }}</strong>
                                </div>
                                <div class="text-muted" style="font-size: 11px;">
                                    {{ log.object_repr|truncatechars:20 }}
                                </div>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="small">{{ log.change_message|truncatechars:50 }}</div>
                        </td>
                        <td>
                            <div class="small font-monospace">{{ log.ip_address|default:"-" }}</div>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-primary view-details" 
                                    data-log-id="{{ log.id }}"
                                    data-admin="{{ log.admin.username }}"
                                    data-action="{{ log.action }}"
                                    data-time="{{ log.created_time|date:'Y-m-d H:i:s' }}"
                                    data-content-type="{{ log.content_type|default:'' }}"
                                    data-object-repr="{{ log.object_repr|default:'' }}"
                                    data-change-message="{{ log.change_message }}"
                                    data-ip="{{ log.ip_address|default:'' }}"
                                    data-user-agent="{{ log.user_agent|default:'' }}">
                                <i class="fas fa-eye"></i>
                            </button>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-history fa-3x mb-3"></i>
                            <p>暂无操作日志</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
{% if page_obj.has_other_pages %}
<nav aria-label="日志列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1">首页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
            </li>
        {% endif %}
        
        <li class="page-item active">
            <span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span>
        </li>
        
        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- 详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">操作日志详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td width="80"><strong>管理员：</strong></td>
                                <td id="detailAdmin"></td>
                            </tr>
                            <tr>
                                <td><strong>操作类型：</strong></td>
                                <td id="detailAction"></td>
                            </tr>
                            <tr>
                                <td><strong>操作时间：</strong></td>
                                <td id="detailTime"></td>
                            </tr>
                            <tr>
                                <td><strong>IP地址：</strong></td>
                                <td id="detailIp"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td width="80"><strong>对象类型：</strong></td>
                                <td id="detailContentType"></td>
                            </tr>
                            <tr>
                                <td><strong>对象描述：</strong></td>
                                <td id="detailObjectRepr"></td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <h6>操作描述：</h6>
                        <div class="bg-light p-3 rounded">
                            <div id="detailChangeMessage"></div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>用户代理：</h6>
                        <div class="bg-light p-2 rounded">
                            <small class="font-monospace" id="detailUserAgent"></small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 查看详情
    $('.view-details').click(function() {
        const data = $(this).data();
        
        $('#detailAdmin').text(data.admin);
        $('#detailAction').text(data.action);
        $('#detailTime').text(data.time);
        $('#detailIp').text(data.ip || '-');
        $('#detailContentType').text(data.contentType || '-');
        $('#detailObjectRepr').text(data.objectRepr || '-');
        $('#detailChangeMessage').text(data.changeMessage);
        $('#detailUserAgent').text(data.userAgent || '-');
        
        $('#logDetailModal').modal('show');
    });
});

function clearOldLogs() {
    if (confirm('确定要清理30天前的操作日志吗？此操作不可恢复！')) {
        alert('清理功能待实现');
    }
}
</script>
{% endblock %}
