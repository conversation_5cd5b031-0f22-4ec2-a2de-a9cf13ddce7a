#!/usr/bin/env python3
"""
测试管理员个人中心功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backn.settings')
django.setup()

from users.models import User
from backn.admin_panel.models import AdminLog
from django.contrib.auth.hashers import make_password

def test_admin_profile_features():
    """测试管理员个人中心功能"""
    print("🧪 测试管理员个人中心功能...")
    print("=" * 60)
    
    # 1. 检查管理员用户
    admin_users = User.objects.filter(is_staff=True)
    print(f"📊 管理员用户总数: {admin_users.count()}")
    
    if admin_users.exists():
        admin = admin_users.first()
        print(f"   测试管理员: {admin.username}")
        print(f"   邮箱: {admin.email or '未设置'}")
        print(f"   姓名: {admin.first_name or '未设置'}")
        print(f"   手机号: {getattr(admin, 'phone', '未设置') or '未设置'}")
        print(f"   注册时间: {admin.date_joined}")
        print(f"   最后登录: {admin.last_login or '从未登录'}")
        
        # 检查操作日志
        logs = AdminLog.objects.filter(admin=admin)
        print(f"   操作日志数: {logs.count()}")
        
        return admin
    else:
        print("❌ 没有找到管理员用户")
        return None

def test_profile_update():
    """测试个人信息更新"""
    print(f"\n🔧 测试个人信息更新...")
    print("=" * 60)
    
    # 获取或创建测试管理员
    admin, created = User.objects.get_or_create(
        username='test_admin_profile',
        defaults={
            'email': '<EMAIL>',
            'first_name': '测试管理员',
            'password': make_password('TestPassword123!'),
            'is_staff': True,
            'is_active': True
        }
    )
    
    if created:
        print(f"✅ 创建测试管理员: {admin.username}")
    else:
        print(f"✅ 使用现有管理员: {admin.username}")
    
    try:
        # 更新个人信息
        original_name = admin.first_name
        original_email = admin.email
        
        admin.first_name = '更新后的管理员'
        admin.email = '<EMAIL>'
        if hasattr(admin, 'phone'):
            admin.phone = '13800138000'
        admin.save()
        
        print(f"✅ 更新个人信息成功")
        print(f"   姓名: {original_name} -> {admin.first_name}")
        print(f"   邮箱: {original_email} -> {admin.email}")
        
        # 验证更新
        updated_admin = User.objects.get(id=admin.id)
        assert updated_admin.first_name == '更新后的管理员'
        assert updated_admin.email == '<EMAIL>'
        print(f"✅ 验证更新成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 个人信息更新失败: {e}")
        return False

def test_password_change():
    """测试密码修改"""
    print(f"\n🔐 测试密码修改...")
    print("=" * 60)
    
    try:
        # 获取测试管理员
        admin = User.objects.get(username='test_admin_profile')
        
        # 保存原密码哈希
        old_password_hash = admin.password
        
        # 修改密码
        new_password = 'NewAdminPassword456!'
        admin.set_password(new_password)
        admin.save()
        
        print(f"✅ 密码修改成功")
        
        # 验证密码已更改
        updated_admin = User.objects.get(id=admin.id)
        assert updated_admin.password != old_password_hash
        print(f"✅ 验证密码哈希已更改")
        
        # 验证新密码可以认证
        from django.contrib.auth import authenticate
        auth_admin = authenticate(username='test_admin_profile', password=new_password)
        assert auth_admin is not None
        print(f"✅ 验证新密码认证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 密码修改失败: {e}")
        return False

def test_admin_logs():
    """测试管理员操作日志"""
    print(f"\n📝 测试管理员操作日志...")
    print("=" * 60)
    
    try:
        # 获取测试管理员
        admin = User.objects.get(username='test_admin_profile')
        
        # 创建测试日志
        log = AdminLog.objects.create(
            admin=admin,
            action='测试操作',
            description='这是一个测试操作日志',
            ip_address='127.0.0.1'
        )
        
        print(f"✅ 创建操作日志成功: {log.action}")
        
        # 查询日志
        logs = AdminLog.objects.filter(admin=admin)
        print(f"✅ 管理员操作日志数: {logs.count()}")
        
        # 显示最近的日志
        recent_logs = logs.order_by('-created_time')[:5]
        for log in recent_logs:
            print(f"   {log.created_time.strftime('%Y-%m-%d %H:%M:%S')} - {log.action}")
        
        return True
        
    except Exception as e:
        print(f"❌ 操作日志测试失败: {e}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    print(f"\n🧹 清理测试数据...")
    print("=" * 60)
    
    try:
        # 删除测试管理员
        if User.objects.filter(username='test_admin_profile').exists():
            User.objects.filter(username='test_admin_profile').delete()
            print(f"✅ 删除测试管理员")
        
        # 删除测试日志
        AdminLog.objects.filter(action='测试操作').delete()
        print(f"✅ 删除测试日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        return False

def show_profile_urls():
    """显示个人中心相关URL"""
    print(f"\n🌐 管理员个人中心相关URL:")
    print("=" * 60)
    print("个人中心:")
    print("  个人中心首页: http://127.0.0.1:8003/profile/")
    print("  修改个人信息: http://127.0.0.1:8003/profile/update/")
    print("  修改密码: http://127.0.0.1:8003/profile/change-password/")
    print("  上传头像: http://127.0.0.1:8003/profile/upload-avatar/")
    print()
    print("其他页面:")
    print("  后台首页: http://127.0.0.1:8003/dashboard/")
    print("  管理员登录: http://127.0.0.1:8003/login/")
    print("  操作日志: http://127.0.0.1:8003/logs/")

def show_test_instructions():
    """显示测试说明"""
    print(f"\n📋 手动测试说明:")
    print("=" * 60)
    print("个人中心功能测试:")
    print("1. 启动后台服务器: cd backn && python manage.py runserver 127.0.0.1:8003")
    print("2. 登录后台管理: http://127.0.0.1:8003/login/")
    print("3. 点击侧边栏'个人中心': http://127.0.0.1:8003/profile/")
    print("4. 测试修改个人信息功能")
    print("5. 测试修改密码功能")
    print("6. 测试上传头像功能")
    print("7. 查看操作统计和最近操作记录")
    print()
    print("功能验证:")
    print("- 个人信息显示正确")
    print("- 修改信息后页面更新")
    print("- 密码修改后需要重新登录")
    print("- 头像上传后立即显示")
    print("- 操作日志正确记录")

if __name__ == "__main__":
    print("🚀 开始测试管理员个人中心功能...")
    print("=" * 80)
    
    try:
        # 测试基本功能
        admin = test_admin_profile_features()
        
        # 测试个人信息更新
        profile_test = test_profile_update()
        
        # 测试密码修改
        password_test = test_password_change()
        
        # 测试操作日志
        log_test = test_admin_logs()
        
        # 清理测试数据
        cleanup_test = cleanup_test_data()
        
        # 显示URL信息
        show_profile_urls()
        
        # 显示测试说明
        show_test_instructions()
        
        # 测试结果总结
        print(f"\n🎯 测试结果总结:")
        print("=" * 60)
        print(f"基本功能测试: {'✅ 通过' if admin else '❌ 失败'}")
        print(f"个人信息更新: {'✅ 通过' if profile_test else '❌ 失败'}")
        print(f"密码修改测试: {'✅ 通过' if password_test else '❌ 失败'}")
        print(f"操作日志测试: {'✅ 通过' if log_test else '❌ 失败'}")
        print(f"数据清理测试: {'✅ 通过' if cleanup_test else '❌ 失败'}")
        
        if all([admin, profile_test, password_test, log_test, cleanup_test]):
            print(f"\n🎉 所有测试通过！管理员个人中心功能正常工作。")
        else:
            print(f"\n⚠️ 部分测试失败，请检查相关功能。")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
