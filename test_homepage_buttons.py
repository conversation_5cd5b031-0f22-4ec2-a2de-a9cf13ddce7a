#!/usr/bin/env python3
"""
测试首页红色按钮功能
"""

import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def test_homepage_buttons():
    base_url = "http://127.0.0.1:8001"
    session = requests.Session()
    
    print("🧪 测试首页红色按钮功能...")
    print("=" * 60)
    
    # 1. 先登录
    print("1. 用户登录...")
    try:
        login_url = urljoin(base_url, "/users/login/")
        response = session.get(login_url)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
            
            if csrf_input:
                csrf_token = csrf_input.get('value')
                
                login_data = {
                    'username': 'testuser',
                    'password': '123456',
                    'user_type': 'normal',
                    'csrfmiddlewaretoken': csrf_token
                }
                
                login_response = session.post(login_url, data=login_data)
                if login_response.status_code == 302:
                    print("   ✅ 登录成功")
                else:
                    print("   ❌ 登录失败")
                    return
            else:
                print("   ❌ 无法获取CSRF令牌")
                return
        else:
            print(f"   ❌ 无法访问登录页面: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return
    
    # 2. 访问首页
    print("\n2. 访问首页...")
    try:
        response = session.get(base_url)
        if response.status_code == 200:
            print("   ✅ 首页访问成功")
            
            # 解析页面，查找商品
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找商品卡片
            product_cards = soup.find_all('div', class_='product-card')
            if product_cards:
                print(f"   找到 {len(product_cards)} 个商品卡片")
                
                # 获取第一个商品的ID
                first_card = product_cards[0]
                product_id = first_card.get('data-product-id')
                
                if product_id:
                    print(f"   商品ID: {product_id}")
                    
                    # 测试收藏API
                    print("\n3. 测试收藏API...")
                    test_favorite_api(session, base_url, product_id)
                    
                    # 测试购物车API
                    print("\n4. 测试购物车API...")
                    test_cart_api(session, base_url, product_id)
                else:
                    print("   ❌ 未找到商品ID")
            else:
                print("   ❌ 未找到商品卡片")
        else:
            print(f"   ❌ 首页访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 首页访问异常: {e}")

def test_favorite_api(session, base_url, product_id):
    """测试收藏API"""
    try:
        # 获取CSRF令牌
        csrf_token = None
        for cookie in session.cookies:
            if cookie.name == 'csrftoken':
                csrf_token = cookie.value
                break
        
        if csrf_token:
            # 测试添加收藏
            add_url = urljoin(base_url, f"/account/favorites/add/{product_id}/")
            
            headers = {
                'X-CSRFToken': csrf_token,
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            response = session.post(add_url, headers=headers)
            print(f"   收藏API状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   收藏API响应: {data}")
                    
                    if data.get('status') in ['success', 'info']:
                        print("   ✅ 收藏API格式正确")
                    else:
                        print("   ⚠️  收藏API格式异常")
                except json.JSONDecodeError:
                    print(f"   ❌ 收藏API返回非JSON: {response.text[:100]}")
            else:
                print(f"   ❌ 收藏API请求失败")
        else:
            print("   ❌ 无法获取CSRF令牌")
    except Exception as e:
        print(f"   ❌ 收藏API测试异常: {e}")

def test_cart_api(session, base_url, product_id):
    """测试购物车API"""
    try:
        # 获取CSRF令牌
        csrf_token = None
        for cookie in session.cookies:
            if cookie.name == 'csrftoken':
                csrf_token = cookie.value
                break
        
        if csrf_token:
            # 测试添加到购物车
            cart_url = urljoin(base_url, "/order/cart/add/")
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrf_token
            }
            
            data = f'product_id={product_id}&quantity=1'
            
            response = session.post(cart_url, data=data, headers=headers)
            print(f"   购物车API状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   购物车API响应: {data}")
                    
                    if data.get('code') == 200:
                        print("   ✅ 购物车API格式正确")
                    else:
                        print("   ⚠️  购物车API格式异常")
                except json.JSONDecodeError:
                    print(f"   ❌ 购物车API返回非JSON: {response.text[:100]}")
            else:
                print(f"   ❌ 购物车API请求失败")
        else:
            print("   ❌ 无法获取CSRF令牌")
    except Exception as e:
        print(f"   ❌ 购物车API测试异常: {e}")

def test_api_formats():
    """测试API响应格式"""
    print("\n5. API响应格式说明...")
    print("   收藏API应返回: {'status': 'success', 'message': '...'}")
    print("   购物车API应返回: {'code': 200, 'msg': '...'}")
    print("   首页JavaScript已修复为兼容收藏API格式")

if __name__ == "__main__":
    test_homepage_buttons()
    test_api_formats()
