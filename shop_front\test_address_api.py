#!/usr/bin/env python3
"""
测试地址API功能
"""

import os
import sys
import django
import requests
import json

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.contrib.auth import get_user_model
from django.test import Client
from users.models import Address

User = get_user_model()

def test_address_api():
    """测试地址API"""
    print("🧪 测试地址API功能")
    print("=" * 60)
    
    # 1. 创建测试用户
    print("\n1. 准备测试用户...")
    try:
        user = User.objects.filter(username='testuser').first()
        if not user:
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
            print("   ✅ 创建测试用户成功")
        else:
            print("   ✅ 测试用户已存在")
    except Exception as e:
        print(f"   ❌ 创建用户失败: {e}")
        return
    
    # 2. 测试API端点
    print("\n2. 测试API端点...")
    client = Client()
    
    # 登录
    login_success = client.login(username='testuser', password='testpass123')
    if login_success:
        print("   ✅ 用户登录成功")
    else:
        print("   ❌ 用户登录失败")
        return
    
    # 测试获取地址列表
    print("\n3. 测试获取地址列表...")
    response = client.get('/users/api/addresses/')
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200:
        data = response.json()
        print(f"   ✅ 获取地址列表成功，数量: {len(data)}")
    else:
        print(f"   ❌ 获取地址列表失败")
    
    # 测试添加地址
    print("\n4. 测试添加地址...")
    address_data = {
        'receiver': '张三',
        'phone': '13800138000',
        'province': '北京市',
        'city': '北京市',
        'district': '朝阳区',
        'detail': '三里屯街道1号',
        'is_default': True
    }
    
    print(f"   发送数据: {address_data}")
    response = client.post('/users/api/addresses/', 
                          data=json.dumps(address_data),
                          content_type='application/json')
    
    print(f"   状态码: {response.status_code}")
    if response.status_code == 200 or response.status_code == 201:
        data = response.json()
        print(f"   ✅ 添加地址成功: {data}")
    else:
        try:
            error_data = response.json()
            print(f"   ❌ 添加地址失败: {error_data}")
        except:
            print(f"   ❌ 添加地址失败，响应内容: {response.content}")
    
    # 5. 检查数据库中的地址
    print("\n5. 检查数据库中的地址...")
    addresses = Address.objects.filter(user=user)
    print(f"   数据库中地址数量: {addresses.count()}")
    for addr in addresses:
        print(f"   地址: {addr.receiver} - {addr.phone} - {addr.province}{addr.city}{addr.district}{addr.address}")

def test_with_requests():
    """使用requests库测试API"""
    print("\n🌐 使用requests库测试API...")
    
    # 创建session
    session = requests.Session()
    
    # 先获取CSRF token
    response = session.get('http://127.0.0.1:8001/users/login/')
    if response.status_code == 200:
        print("   ✅ 获取登录页面成功")
        
        # 从响应中提取CSRF token
        csrf_token = None
        for line in response.text.split('\n'):
            if 'csrfmiddlewaretoken' in line:
                start = line.find('value="') + 7
                end = line.find('"', start)
                csrf_token = line[start:end]
                break
        
        if csrf_token:
            print(f"   ✅ 获取CSRF token: {csrf_token[:20]}...")
            
            # 登录
            login_data = {
                'username': 'testuser',
                'password': 'testpass123',
                'csrfmiddlewaretoken': csrf_token
            }
            
            response = session.post('http://127.0.0.1:8001/users/login/', data=login_data)
            if response.status_code == 200 or response.status_code == 302:
                print("   ✅ 登录成功")
                
                # 测试添加地址API
                address_data = {
                    'receiver': '李四',
                    'phone': '13900139000',
                    'province': '上海市',
                    'city': '上海市',
                    'district': '浦东新区',
                    'detail': '陆家嘴金融中心1号',
                    'is_default': False
                }
                
                headers = {
                    'X-CSRFToken': csrf_token,
                    'Content-Type': 'application/json'
                }
                
                response = session.post('http://127.0.0.1:8001/users/api/addresses/', 
                                      json=address_data, headers=headers)
                
                print(f"   API响应状态码: {response.status_code}")
                try:
                    result = response.json()
                    print(f"   API响应内容: {result}")
                except:
                    print(f"   API响应内容: {response.text}")
            else:
                print(f"   ❌ 登录失败: {response.status_code}")
        else:
            print("   ❌ 未找到CSRF token")
    else:
        print(f"   ❌ 获取登录页面失败: {response.status_code}")

def main():
    """主函数"""
    print("🔧 地址API测试工具")
    print("=" * 60)
    
    # Django内部测试
    test_address_api()
    
    # HTTP请求测试
    test_with_requests()
    
    print("\n✨ 测试完成！")
    print("如果发现问题，请检查:")
    print("1. 服务器是否正在运行 (http://127.0.0.1:8001)")
    print("2. 用户是否已登录")
    print("3. API路径是否正确 (/users/api/addresses/)")
    print("4. 请求数据格式是否正确")

if __name__ == '__main__':
    main()
