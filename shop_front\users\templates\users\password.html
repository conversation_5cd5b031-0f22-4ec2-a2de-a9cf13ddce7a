{% extends 'base.html' %}
{% load static %}

{% block title %}修改密码 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 修改为红色主题样式 */
    .profile-container {
        max-width: 1200px;
        margin: 40px auto;
        display: flex;
        gap: 20px;
    }

    .sidebar {
        width: 250px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.1);
        padding: 20px;
        border: 1px solid rgba(220, 53, 69, 0.1);
    }

    .user-avatar {
        text-align: center;
        margin-bottom: 20px;
    }

    .user-avatar img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #dc3545;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
    }

    .user-name {
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 10px;
    }

    .sidebar-menu a {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #666;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        transform: translateY(-2px);
    }

    .sidebar-menu a.active {
        background: #dc3545;
        color: white;
    }

    .sidebar-menu i {
        margin-right: 10px;
        width: 20px;
        font-size: 16px;
        text-align: center;
    }

    .main-content {
        flex: 1;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 30px;
        position: relative;
        overflow: hidden;
    }

    .main-content:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }

    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .profile-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .profile-header h2 {
        font-size: 24px;
        color: #dc3545;
        margin-bottom: 0;
        font-weight: 700;
        display: flex;
        align-items: center;
    }

    .profile-header h2 i {
        margin-right: 10px;
        color: #dc3545;
        font-size: 24px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #555;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        height: 45px;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #dc3545;
        outline: none;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    }

    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }

    .errorlist {
        list-style: none;
        padding: 0;
        margin: 0.5rem 0;
        color: #dc3545;
        font-size: 0.875rem;
    }

    .password-form {
        max-width: 500px;
        margin: 0 auto;
    }

    .btn-submit {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
        min-width: 120px;
    }

    .btn-submit:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
    }

    .form-buttons {
        margin-top: 2rem;
        text-align: center;
    }

    /* 消息提示样式 */
    .message-container {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 9999;
        width: 100%;
        max-width: 400px;
        text-align: center;
        pointer-events: none;
    }

    .message {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        margin: 10px;
        padding: 16px 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        animation: messageSlideIn 0.3s ease-out;
        pointer-events: auto;
    }

    .message.success {
        border-left: 4px solid #28a745;
    }

    .message.error {
        border-left: 4px solid #dc3545;
    }

    .message.info {
        border-left: 4px solid #17a2b8;
    }

    .message.warning {
        border-left: 4px solid #ffc107;
    }

    .message i {
        margin-right: 12px;
        font-size: 20px;
    }

    .message.success i {
        color: #28a745;
    }

    .message.error i {
        color: #dc3545;
    }

    .message.info i {
        color: #17a2b8;
    }

    .message.warning i {
        color: #ffc107;
    }

    @keyframes messageSlideIn {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    @keyframes messageSlideOut {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(-20px);
            opacity: 0;
        }
    }

    /* 响应式设计 */
    @media (max-width: 992px) {
        .profile-container {
            flex-direction: column;
        }

        .sidebar {
            width: 100%;
            margin-bottom: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 消息提示容器 -->
{% if messages %}
<div class="message-container">
    {% for message in messages %}
    <div class="message {{ message.tags }}">
        {% if message.tags == 'success' %}
        <i class="fas fa-check-circle"></i>
        {% elif message.tags == 'error' %}
        <i class="fas fa-times-circle"></i>
        {% elif message.tags == 'info' %}
        <i class="fas fa-info-circle"></i>
        {% elif message.tags == 'warning' %}
        <i class="fas fa-exclamation-circle"></i>
        {% endif %}
        {{ message }}
    </div>
    {% endfor %}
</div>
{% endif %}

<div class="container py-5">
    <div class="profile-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-avatar">
                <img src="{{ user.avatar.url }}" alt="{{ user.username }}" onerror="this.src='/media/avatars/default.png'">
            </div>
            <div class="user-name">{{ user.username }}</div>
            <ul class="sidebar-menu">
                <li>
                    <a href="{% url 'users:center' %}">
                        <i class="fas fa-home"></i> 个人中心
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:profile' %}">
                        <i class="fas fa-user"></i> 个人资料
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:orders' %}">
                        <i class="fas fa-shopping-bag"></i> 我的订单
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:address' %}">
                        <i class="fas fa-map-marker-alt"></i> 收货地址
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:password' %}" class="active">
                        <i class="fas fa-lock"></i> 修改密码
                    </a>
                </li>
            </ul>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="profile-header">
                <h2><i class="fas fa-lock"></i> 修改密码</h2>
            </div>

            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {{ form.non_field_errors }}
            </div>
            {% endif %}

            <form method="post" class="password-form">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="{{ form.old_password.id_for_label }}">{{ form.old_password.label }}</label>
                    {{ form.old_password }}
                    {% if form.old_password.errors %}
                    <ul class="errorlist">
                        {% for error in form.old_password.errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.new_password.id_for_label }}">{{ form.new_password.label }}</label>
                    {{ form.new_password }}
                    {% if form.new_password.help_text %}
                    <div class="help-text">{{ form.new_password.help_text }}</div>
                    {% endif %}
                    {% if form.new_password.errors %}
                    <ul class="errorlist">
                        {% for error in form.new_password.errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.confirm_password.id_for_label }}">{{ form.confirm_password.label }}</label>
                    {{ form.confirm_password }}
                    {% if form.confirm_password.errors %}
                    <ul class="errorlist">
                        {% for error in form.confirm_password.errors %}
                        <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>

                <div class="form-buttons">
                    <button type="submit" class="btn-submit">确认修改</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动隐藏消息
    const messages = document.querySelectorAll('.message');
    messages.forEach(message => {
        setTimeout(() => {
            message.style.animation = 'messageSlideOut 0.3s ease-out forwards';
            setTimeout(() => {
                message.remove();
            }, 300);
        }, 3000);
    });
});
</script>
{% endblock %}
