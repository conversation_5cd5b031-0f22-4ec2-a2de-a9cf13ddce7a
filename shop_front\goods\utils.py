"""
商品相关工具函数
"""
import os
import shutil
from django.conf import settings
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from PIL import Image
import uuid

def handle_uploaded_image(uploaded_file, upload_path='products/'):
    """
    处理上传的图片文件
    支持从任意路径上传图片，包括shop文件夹以外的图片
    
    Args:
        uploaded_file: 上传的文件对象
        upload_path: 上传路径前缀
    
    Returns:
        str: 保存后的文件路径
    """
    try:
        # 获取文件扩展名
        file_name = uploaded_file.name
        file_ext = os.path.splitext(file_name)[1].lower()
        
        # 检查文件扩展名
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        if file_ext not in allowed_extensions:
            file_ext = '.jpg'  # 默认扩展名
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}{file_ext}"
        file_path = os.path.join(upload_path, unique_filename)
        
        # 保存文件
        saved_path = default_storage.save(file_path, uploaded_file)
        
        # 如果是图片，进行压缩处理
        if file_ext in ['.jpg', '.jpeg', '.png']:
            compress_image(saved_path)
        
        return saved_path
        
    except Exception as e:
        print(f"文件上传处理错误: {e}")
        return None

def compress_image(image_path, max_size=(800, 800), quality=85):
    """
    压缩图片
    
    Args:
        image_path: 图片路径
        max_size: 最大尺寸 (width, height)
        quality: 压缩质量 (1-100)
    """
    try:
        full_path = os.path.join(settings.MEDIA_ROOT, image_path)
        
        if not os.path.exists(full_path):
            return
        
        with Image.open(full_path) as img:
            # 转换为RGB模式（如果是RGBA）
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 计算新尺寸
            img.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 保存压缩后的图片
            img.save(full_path, 'JPEG', quality=quality, optimize=True)
            
    except Exception as e:
        print(f"图片压缩错误: {e}")

def copy_external_image(source_path, upload_path='products/'):
    """
    从外部路径复制图片到媒体目录
    
    Args:
        source_path: 源文件路径
        upload_path: 目标上传路径
    
    Returns:
        str: 保存后的文件路径
    """
    try:
        if not os.path.exists(source_path):
            return None
        
        # 获取文件扩展名
        file_ext = os.path.splitext(source_path)[1].lower()
        
        # 检查是否为图片文件
        allowed_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
        if file_ext not in allowed_extensions:
            return None
        
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}{file_ext}"
        target_path = os.path.join(settings.MEDIA_ROOT, upload_path, unique_filename)
        
        # 确保目标目录存在
        os.makedirs(os.path.dirname(target_path), exist_ok=True)
        
        # 复制文件
        shutil.copy2(source_path, target_path)
        
        # 返回相对路径
        relative_path = os.path.join(upload_path, unique_filename)
        
        # 压缩图片
        compress_image(relative_path)
        
        return relative_path
        
    except Exception as e:
        print(f"外部图片复制错误: {e}")
        return None

def validate_image_file(uploaded_file):
    """
    验证上传的图片文件
    
    Args:
        uploaded_file: 上传的文件对象
    
    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        # 检查文件大小
        max_size = getattr(settings, 'MAX_IMAGE_SIZE', 5 * 1024 * 1024)  # 5MB
        if uploaded_file.size > max_size:
            return False, f"文件大小超过限制 ({max_size // (1024*1024)}MB)"
        
        # 检查文件扩展名
        file_name = uploaded_file.name
        file_ext = os.path.splitext(file_name)[1].lower()
        allowed_extensions = getattr(settings, 'ALLOWED_IMAGE_EXTENSIONS', 
                                   ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'])
        
        if file_ext not in allowed_extensions:
            return False, f"不支持的文件格式，支持的格式: {', '.join(allowed_extensions)}"
        
        # 尝试打开图片验证格式
        try:
            uploaded_file.seek(0)
            with Image.open(uploaded_file) as img:
                img.verify()
            uploaded_file.seek(0)
        except Exception:
            return False, "文件不是有效的图片格式"
        
        return True, ""
        
    except Exception as e:
        return False, f"文件验证错误: {str(e)}"

def get_image_info(image_path):
    """
    获取图片信息
    
    Args:
        image_path: 图片路径
    
    Returns:
        dict: 图片信息
    """
    try:
        full_path = os.path.join(settings.MEDIA_ROOT, image_path)
        
        if not os.path.exists(full_path):
            return None
        
        with Image.open(full_path) as img:
            return {
                'width': img.width,
                'height': img.height,
                'format': img.format,
                'mode': img.mode,
                'size': os.path.getsize(full_path)
            }
            
    except Exception as e:
        print(f"获取图片信息错误: {e}")
        return None

def create_thumbnail(image_path, size=(150, 150)):
    """
    创建缩略图
    
    Args:
        image_path: 原图片路径
        size: 缩略图尺寸
    
    Returns:
        str: 缩略图路径
    """
    try:
        full_path = os.path.join(settings.MEDIA_ROOT, image_path)
        
        if not os.path.exists(full_path):
            return None
        
        # 生成缩略图文件名
        path_parts = os.path.splitext(image_path)
        thumbnail_path = f"{path_parts[0]}_thumb{path_parts[1]}"
        thumbnail_full_path = os.path.join(settings.MEDIA_ROOT, thumbnail_path)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(thumbnail_full_path), exist_ok=True)
        
        with Image.open(full_path) as img:
            # 转换为RGB模式
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # 创建缩略图
            img.thumbnail(size, Image.Resampling.LANCZOS)
            img.save(thumbnail_full_path, 'JPEG', quality=90)
        
        return thumbnail_path
        
    except Exception as e:
        print(f"创建缩略图错误: {e}")
        return None
