from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _

class AdminLog(models.Model):
    """管理员操作日志"""
    admin = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('管理员'),
                            on_delete=models.CASCADE, related_name='admin_logs')
    action = models.CharField(_('操作类型'), max_length=50)
    content_type = models.CharField(_('操作对象类型'), max_length=50, blank=True)
    object_id = models.CharField(_('操作对象ID'), max_length=50, blank=True)
    object_repr = models.CharField(_('操作对象描述'), max_length=200, blank=True)
    change_message = models.TextField(_('变更信息'), blank=True)
    ip_address = models.GenericIPAddressField(_('IP地址'), blank=True, null=True)
    user_agent = models.TextField(_('用户代理'), blank=True)
    created_time = models.DateTimeField(_('操作时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('管理员日志')
        verbose_name_plural = verbose_name
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['admin', 'created_time']),
            models.Index(fields=['action', 'created_time']),
        ]

    def __str__(self):
        return f"{self.admin.username} - {self.action} - {self.created_time}"
