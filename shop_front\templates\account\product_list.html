{% extends "base.html" %}
{% load static %}

{% block title %}商品管理{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mb-3">商品管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'goods:index' %}">首页</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'account:admin_dashboard' %}">管理员仪表板</a></li>
                    <li class="breadcrumb-item active">商品管理</li>
                </ol>
            </nav>
        </div>
    </div>

    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="row mb-4">
        <div class="col">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">商品列表</h5>
                    <a href="{% url 'account:product_add' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> 添加商品
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>图片</th>
                                    <th>商品名称</th>
                                    <th>分类</th>
                                    <th>价格</th>
                                    <th>库存</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>{{ product.id }}</td>
                                    <td>
                                        <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                    </td>
                                    <td>{{ product.name }}</td>
                                    <td>{{ product.category.name }}</td>
                                    <td>¥{{ product.price }}</td>
                                    <td>{{ product.stock }}</td>
                                    <td>
                                        {% if product.is_active %}
                                        <span class="badge bg-success">上架中</span>
                                        {% else %}
                                        <span class="badge bg-danger">已下架</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'account:product_edit' product.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i> 编辑
                                            </a>
                                            <a href="{% url 'goods:detail' product.id %}" class="btn btn-sm btn-outline-info" target="_blank">
                                                <i class="fas fa-eye"></i> 查看
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center">暂无商品数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 