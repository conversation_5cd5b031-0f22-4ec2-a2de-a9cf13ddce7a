<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MARS BUY 后台管理系统 - 管理员注册</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Aria<PERSON>, sans-serif;
        }
        
        .register-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
            margin: 20px;
        }
        
        .register-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 40px 30px 30px;
            text-align: center;
        }
        
        .register-header h2 {
            margin: 0;
            font-weight: 300;
            font-size: 28px;
        }
        
        .register-header .logo {
            font-size: 3rem;
            margin-bottom: 10px;
        }
        
        .register-header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        
        .register-body {
            padding: 40px 30px;
        }
        
        .form-control {
            border: 2px solid #f1f3f4;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 16px;
            transition: all 0.3s ease;
            height: 50px;
        }
        
        .form-control:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220,53,69,0.25);
        }
        
        .form-floating label {
            color: #666;
            font-weight: 500;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
            height: 50px;
        }
        
        .btn-register:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220,53,69,0.4);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        /* 头像上传样式 */
        .avatar-upload {
            text-align: center;
            margin-bottom: 25px;
        }
        
        .avatar-preview {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            overflow: hidden;
            margin: 15px auto 20px;
            border: 4px solid rgba(220,53,69,0.2);
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }
        
        .avatar-preview:hover {
            border-color: #dc3545;
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(220,53,69,0.2);
        }
        
        .avatar-preview img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }
        
        .avatar-preview:hover img {
            transform: scale(1.1);
        }
        
        #id_avatar {
            display: none;
        }
        
        .avatar-upload-btn {
            display: inline-block;
            padding: 8px 16px;
            background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
            color: white;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220,53,69,0.3);
            margin-top: 10px;
        }
        
        .avatar-upload-btn:hover {
            background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(220,53,69,0.4);
        }
        
        /* 登录链接样式 */
        .login-link {
            padding: 20px 0;
            border-top: 1px solid #f1f3f4;
            margin-top: 20px;
            text-align: center;
        }
        
        .login-link p {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .btn-login-link {
            color: #dc3545;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .btn-login-link:hover {
            color: #c82333;
            text-decoration: none;
        }
        
        .btn-login-link:after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -2px;
            left: 50%;
            background: #dc3545;
            transition: all 0.3s ease;
        }
        
        .btn-login-link:hover:after {
            width: 100%;
            left: 0;
        }
        
        /* 错误信息样式 */
        .error-message {
            color: #dc3545;
            font-size: 13px;
            margin-top: 8px;
            padding: 8px 12px;
            background: rgba(220,53,69,0.1);
            border-radius: 8px;
            border-left: 3px solid #dc3545;
            animation: slideIn 0.3s ease;
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* 帮助文本样式 */
        .help-text {
            font-size: 12px;
            color: #888;
            margin-top: 6px;
            padding-left: 5px;
            opacity: 0.8;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .register-container {
                margin: 10px;
            }
            
            .register-body {
                padding: 30px 20px;
            }
            
            .register-header {
                padding: 30px 20px 20px;
            }
            
            .register-header h2 {
                font-size: 24px;
            }
        }
        
        /* 动画效果 */
        .register-container {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .form-floating {
            animation: fadeInUp 0.6s ease-out;
            animation-fill-mode: both;
        }
        
        .form-floating:nth-child(1) { animation-delay: 0.1s; }
        .form-floating:nth-child(2) { animation-delay: 0.2s; }
        .form-floating:nth-child(3) { animation-delay: 0.3s; }
        .form-floating:nth-child(4) { animation-delay: 0.4s; }
        .form-floating:nth-child(5) { animation-delay: 0.5s; }
        .form-floating:nth-child(6) { animation-delay: 0.6s; }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-header">
            <div class="logo">
                <i class="fas fa-user-shield"></i>
            </div>
            <h2>MARS BUY</h2>
            <p>管理员注册</p>
        </div>
        
        <div class="register-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }}" role="alert">
                        <i class="fas fa-{% if message.tags == 'error' %}exclamation-triangle{% else %}check-circle{% endif %} me-2"></i>
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
            
            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- 头像上传 -->
                <div class="avatar-upload">
                    <label style="color: #555; font-weight: 600; margin-bottom: 10px; display: block;">
                        <i class="fas fa-user-circle"></i> 管理员头像
                    </label>
                    <div class="avatar-preview" onclick="document.getElementById('id_avatar').click()">
                        <img src="/media/avatars/default.png" alt="默认头像" id="avatar-preview-img">
                    </div>
                    {{ form.avatar }}
                    <label for="id_avatar" class="avatar-upload-btn">
                        <i class="fas fa-camera"></i> 选择头像
                    </label>
                </div>
                
                <div class="form-floating">
                    <input type="text" class="form-control" id="username" name="username" 
                           placeholder="请输入用户名" required value="{{ form.username.value|default:'' }}">
                    <label for="username">
                        <i class="fas fa-user me-2"></i>用户名
                    </label>
                    {% if form.username.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i> {{ form.username.errors.0 }}
                        </div>
                    {% endif %}
                    {% if form.username.help_text %}
                        <div class="help-text">{{ form.username.help_text }}</div>
                    {% endif %}
                </div>
                
                <div class="form-floating">
                    <input type="email" class="form-control" id="email" name="email" 
                           placeholder="请输入邮箱" required value="{{ form.email.value|default:'' }}">
                    <label for="email">
                        <i class="fas fa-envelope me-2"></i>邮箱地址
                    </label>
                    {% if form.email.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i> {{ form.email.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-floating">
                    <input type="tel" class="form-control" id="phone" name="phone" 
                           placeholder="请输入手机号" value="{{ form.phone.value|default:'' }}">
                    <label for="phone">
                        <i class="fas fa-phone me-2"></i>手机号码
                    </label>
                    {% if form.phone.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i> {{ form.phone.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="password1" name="password1" 
                           placeholder="请输入密码" required>
                    <label for="password1">
                        <i class="fas fa-lock me-2"></i>设置密码
                    </label>
                    {% if form.password1.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i> {{ form.password1.errors.0 }}
                        </div>
                    {% endif %}
                    {% if form.password1.help_text %}
                        <div class="help-text">{{ form.password1.help_text }}</div>
                    {% endif %}
                </div>
                
                <div class="form-floating">
                    <input type="password" class="form-control" id="password2" name="password2" 
                           placeholder="请再次输入密码" required>
                    <label for="password2">
                        <i class="fas fa-lock me-2"></i>确认密码
                    </label>
                    {% if form.password2.errors %}
                        <div class="error-message">
                            <i class="fas fa-exclamation-triangle"></i> {{ form.password2.errors.0 }}
                        </div>
                    {% endif %}
                </div>
                
                <button type="submit" class="btn btn-primary btn-register">
                    <i class="fas fa-user-plus me-2"></i>
                    注册管理员账号
                </button>
            </form>
            
            <div class="login-link">
                <p>已有管理员账号？</p>
                <a href="{% url 'admin_panel:login' %}" class="btn-login-link">
                    <i class="fas fa-sign-in-alt me-1"></i> 立即登录
                </a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 头像预览
            const avatarInput = document.getElementById('id_avatar');
            const avatarPreview = document.getElementById('avatar-preview-img');
            
            if (avatarInput) {
                avatarInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        // 检查文件类型
                        if (!file.type.startsWith('image/')) {
                            alert('请选择图片文件！');
                            return;
                        }
                        
                        // 检查文件大小（限制5MB）
                        if (file.size > 5 * 1024 * 1024) {
                            alert('图片大小不能超过5MB！');
                            return;
                        }
                        
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            avatarPreview.src = e.target.result;
                            // 添加上传成功的视觉反馈
                            avatarPreview.style.transform = 'scale(1.1)';
                            setTimeout(() => {
                                avatarPreview.style.transform = 'scale(1)';
                            }, 300);
                        }
                        reader.readAsDataURL(file);
                    }
                });
            }
            
            // 表单提交动画
            const form = document.querySelector('form');
            const submitBtn = document.querySelector('.btn-register');
            
            form.addEventListener('submit', function(e) {
                submitBtn.classList.add('loading');
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> 注册中...';
            });
            
            // 密码强度检测
            const password1 = document.getElementById('password1');
            if (password1) {
                password1.addEventListener('input', function() {
                    const password = this.value;
                    const strength = checkPasswordStrength(password);
                    showPasswordStrength(strength);
                });
            }
            
            function checkPasswordStrength(password) {
                let score = 0;
                if (password.length >= 8) score++;
                if (/[a-z]/.test(password)) score++;
                if (/[A-Z]/.test(password)) score++;
                if (/[0-9]/.test(password)) score++;
                if (/[^A-Za-z0-9]/.test(password)) score++;
                
                if (score < 2) return 'weak';
                if (score < 4) return 'medium';
                return 'strong';
            }
            
            function showPasswordStrength(strength) {
                let existingIndicator = document.querySelector('.password-strength');
                if (existingIndicator) {
                    existingIndicator.remove();
                }
                
                const indicator = document.createElement('div');
                indicator.className = 'password-strength';
                indicator.style.cssText = `
                    margin-top: 5px;
                    padding: 5px 10px;
                    border-radius: 5px;
                    font-size: 12px;
                    font-weight: 600;
                `;
                
                switch(strength) {
                    case 'weak':
                        indicator.textContent = '密码强度：弱';
                        indicator.style.background = 'rgba(220,53,69,0.1)';
                        indicator.style.color = '#dc3545';
                        break;
                    case 'medium':
                        indicator.textContent = '密码强度：中等';
                        indicator.style.background = 'rgba(255,193,7,0.1)';
                        indicator.style.color = '#ffc107';
                        break;
                    case 'strong':
                        indicator.textContent = '密码强度：强';
                        indicator.style.background = 'rgba(40,167,69,0.1)';
                        indicator.style.color = '#28a745';
                        break;
                }
                
                password1.parentElement.appendChild(indicator);
            }
        });
    </script>
</body>
</html>
