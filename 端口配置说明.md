# 🚀 MARS BUY系统端口配置说明

## 📋 端口分配

| 系统 | 端口 | 访问地址 | 说明 |
|------|------|----------|------|
| **前台商城** | 8001 | http://127.0.0.1:8001 | 用户购物界面 |
| **后台管理** | 8002 | http://127.0.0.1:8002/admin/ | 管理员后台 |

## 🔧 配置文件修改

### 前台系统 (shop_front)
- **设置文件**: `shop_front/shop_front/settings.py`
- **端口**: 8001
- **ALLOWED_HOSTS**: `['127.0.0.1', 'localhost']`
- **支付宝回调URL**: 已更新为8001端口

### 后台系统 (back)
- **设置文件**: `back/back/settings.py`
- **端口**: 8002
- **CSRF_TRUSTED_ORIGINS**: `['http://127.0.0.1:8002', 'http://localhost:8002']`

## 🚀 启动方式

### 方式一：使用启动脚本（推荐）

**Windows用户**:
```bash
双击运行 start_servers.bat
```

**Linux/Mac用户**:
```bash
chmod +x start_servers.sh
./start_servers.sh
```

### 方式二：手动启动

**启动后台管理系统**:
```bash
cd back
python manage.py runserver 127.0.0.1:8002
```

**启动前台商城系统**:
```bash
cd shop_front
python manage.py runserver 127.0.0.1:8001
```

## 🔗 系统链接

### 前台商城 (端口8001)
- **首页**: http://127.0.0.1:8001
- **用户登录**: http://127.0.0.1:8001/users/login/
- **用户注册**: http://127.0.0.1:8001/users/register/
- **个人中心**: http://127.0.0.1:8001/users/center/

### 后台管理 (端口8002)
- **管理员登录**: http://127.0.0.1:8002/admin/
- **后台首页**: http://127.0.0.1:8002/admin/

## ⚙️ 重要说明

1. **数据库共享**: 前后台系统使用同一个数据库，数据实时同步
2. **用户权限**: 只有is_staff=True的用户才能访问后台管理
3. **跨域配置**: 已配置CSRF信任域名，支持前后台交互
4. **媒体文件**: 两个系统都有独立的media目录，头像文件已同步

## 🛠️ 故障排除

### 端口被占用
如果遇到端口被占用的问题：

**Windows**:
```bash
netstat -ano | findstr :8001
netstat -ano | findstr :8002
taskkill /PID <进程ID> /F
```

**Linux/Mac**:
```bash
lsof -i :8001
lsof -i :8002
kill -9 <进程ID>
```

### 访问权限问题
- 确保防火墙允许8001和8002端口
- 检查ALLOWED_HOSTS配置是否正确

## 📝 开发注意事项

1. **迁移文件**: 只在前台项目(shop_front)执行数据库迁移
2. **静态文件**: 两个项目有独立的静态文件配置
3. **日志文件**: 分别记录在各自的logs目录下
4. **会话管理**: 前后台使用独立的会话系统

## 🎯 快速测试

启动系统后，可以通过以下方式快速测试：

1. 访问 http://127.0.0.1:8001 查看前台首页
2. 访问 http://127.0.0.1:8002/admin/ 查看后台登录页
3. 在前台注册用户并登录
4. 使用管理员账号登录后台管理系统

## 📞 技术支持

如果遇到问题，请检查：
- Python环境是否正确
- 数据库连接是否正常
- 端口是否被其他程序占用
- 防火墙设置是否正确
