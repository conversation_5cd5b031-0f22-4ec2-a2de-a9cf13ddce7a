#!/usr/bin/env python
"""
测试秒杀商品显示逻辑
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop/shop_front')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from goods.models import Product

def test_seckill_products():
    """测试秒杀商品选择逻辑"""
    print("🔍 测试秒杀商品选择逻辑...")
    
    # 获取所有商品按价格排序
    all_products = Product.objects.filter(is_active=True).order_by('-price')
    print(f"📊 数据库中共有 {all_products.count()} 个活跃商品")
    
    if all_products.count() == 0:
        print("❌ 没有找到任何活跃商品")
        return
    
    print("\n💰 所有商品按价格排序（前10个）：")
    for i, product in enumerate(all_products[:10], 1):
        print(f"{i:2d}. {product.name:<30} - ¥{product.price}")
    
    # 获取秒杀商品（价格最高的4个）
    seckill_products = Product.objects.filter(is_active=True).order_by('-price')[:4]
    
    print(f"\n🔥 秒杀商品（价格最高的4个）：")
    for i, product in enumerate(seckill_products, 1):
        original_price = float(product.price)
        seckill_price = original_price - 200
        discount = 200
        discount_percent = (discount / original_price) * 100 if original_price > 0 else 0
        
        print(f"{i}. {product.name}")
        print(f"   原价: ¥{original_price:.2f}")
        print(f"   秒杀价: ¥{seckill_price:.2f}")
        print(f"   优惠: ¥{discount:.2f} ({discount_percent:.1f}% off)")
        print(f"   库存: {product.stock}")
        print(f"   销量: {product.sales}")
        print()

if __name__ == '__main__':
    test_seckill_products()
