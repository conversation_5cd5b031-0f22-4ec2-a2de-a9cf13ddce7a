#!/usr/bin/env python3
"""
测试商品收藏和购物车功能
"""

import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def test_product_actions():
    base_url = "http://127.0.0.1:8001"
    session = requests.Session()
    
    print("🧪 测试商品收藏和购物车功能...")
    print("=" * 60)
    
    # 1. 先登录
    print("1. 用户登录...")
    try:
        login_url = urljoin(base_url, "/users/login/")
        response = session.get(login_url)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
            
            if csrf_input:
                csrf_token = csrf_input.get('value')
                
                login_data = {
                    'username': 'testuser',
                    'password': '123456',
                    'user_type': 'normal',
                    'csrfmiddlewaretoken': csrf_token
                }
                
                login_response = session.post(login_url, data=login_data)
                if login_response.status_code == 302:
                    print("   ✅ 登录成功")
                else:
                    print("   ❌ 登录失败")
                    return
            else:
                print("   ❌ 无法获取CSRF令牌")
                return
        else:
            print(f"   ❌ 无法访问登录页面: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return
    
    # 2. 获取商品列表页面
    print("\n2. 获取商品列表页面...")
    try:
        products_url = urljoin(base_url, "/goods/products/")
        response = session.get(products_url)
        
        if response.status_code == 200:
            print("   ✅ 商品列表页面访问成功")
            
            # 解析页面，查找商品ID
            soup = BeautifulSoup(response.text, 'html.parser')
            product_cards = soup.find_all('div', class_='product-card')
            
            if product_cards:
                # 获取第一个商品的ID
                first_card = product_cards[0]
                product_id = first_card.get('data-product-id')
                
                if product_id:
                    print(f"   找到商品ID: {product_id}")
                    
                    # 测试收藏功能
                    print("\n3. 测试收藏功能...")
                    test_favorite(session, base_url, product_id)
                    
                    # 测试购物车功能
                    print("\n4. 测试购物车功能...")
                    test_cart(session, base_url, product_id)
                    
                else:
                    print("   ❌ 未找到商品ID")
            else:
                print("   ❌ 未找到商品卡片")
        else:
            print(f"   ❌ 商品列表页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 商品列表页面访问异常: {e}")

def test_favorite(session, base_url, product_id):
    """测试收藏功能"""
    try:
        # 获取CSRF令牌
        csrf_token = None
        for cookie in session.cookies:
            if cookie.name == 'csrftoken':
                csrf_token = cookie.value
                break
        
        if csrf_token:
            # 测试添加收藏
            add_url = urljoin(base_url, f"/account/favorites/add/{product_id}/")
            
            headers = {
                'X-CSRFToken': csrf_token,
                'X-Requested-With': 'XMLHttpRequest'
            }
            
            response = session.post(add_url, headers=headers)
            print(f"   添加收藏状态码: {response.status_code}")
            print(f"   添加收藏响应: {response.text}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ 收藏功能正常: {data}")
                except:
                    print(f"   ⚠️  收藏响应非JSON格式")
            else:
                print(f"   ❌ 收藏功能异常")
                
            # 测试移除收藏
            remove_url = urljoin(base_url, f"/account/favorites/remove/{product_id}/")
            response = session.post(remove_url, headers=headers)
            print(f"   移除收藏状态码: {response.status_code}")
            
        else:
            print("   ❌ 无法获取CSRF令牌")
    except Exception as e:
        print(f"   ❌ 收藏功能测试异常: {e}")

def test_cart(session, base_url, product_id):
    """测试购物车功能"""
    try:
        # 获取CSRF令牌
        csrf_token = None
        for cookie in session.cookies:
            if cookie.name == 'csrftoken':
                csrf_token = cookie.value
                break
        
        if csrf_token:
            # 测试添加到购物车
            cart_add_url = urljoin(base_url, "/order/cart/add/")
            
            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': csrf_token
            }
            
            data = f'product_id={product_id}&quantity=1'
            
            response = session.post(cart_add_url, data=data, headers=headers)
            print(f"   添加购物车状态码: {response.status_code}")
            print(f"   添加购物车响应: {response.text}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"   ✅ 购物车功能正常: {data}")
                except:
                    print(f"   ⚠️  购物车响应非JSON格式")
            else:
                print(f"   ❌ 购物车功能异常")
        else:
            print("   ❌ 无法获取CSRF令牌")
    except Exception as e:
        print(f"   ❌ 购物车功能测试异常: {e}")

def test_urls():
    """测试URL可访问性"""
    base_url = "http://127.0.0.1:8001"
    
    print("\n5. 测试URL可访问性...")
    
    urls_to_test = [
        "/goods/products/",
        "/account/favorites/",
        "/order/cart/",
        "/account/favorites/add/99/",
        "/order/cart/add/",
    ]
    
    for url in urls_to_test:
        try:
            response = requests.get(urljoin(base_url, url))
            status = "✅" if response.status_code in [200, 302, 405] else "❌"
            print(f"   {url}: {status} {response.status_code}")
        except Exception as e:
            print(f"   {url}: ❌ 异常 - {e}")

if __name__ == "__main__":
    test_product_actions()
    test_urls()
