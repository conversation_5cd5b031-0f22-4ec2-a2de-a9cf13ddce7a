{% extends 'base.html' %}
{% load static %}

{% block title %}我的订单 - MARS BUY{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/order.css' %}">
<style>
    /* 全局样式优化 */
    :root {
        --primary-color: #DD302D;
        --primary-light: #ff6a6a;
        --primary-dark: #c01e1a;
        --gradient-primary: linear-gradient(135deg, #DD302D 0%, #ff6a6a 100%);
    }

    .user-center {
        display: flex;
        gap: 20px;
        padding: 20px 0;
        max-width: 1280px;
        margin: 0 auto;
    }

    /* 面包屑导航美化 */
    .breadcrumb {
        background: linear-gradient(135deg, #fff5f5 0%, #ffe3e3 100%);
        padding: 15px;
        border-radius: 8px;
        margin: 20px auto;
        box-shadow: 0 2px 4px rgba(221,48,45,0.1);
        max-width: 1280px;
    }

    .breadcrumb a {
        color: #DD302D;
        text-decoration: none;
        transition: all 0.3s;
        font-weight: 500;
    }

    .breadcrumb a:hover {
        color: var(--primary-light);
        transform: translateX(3px);
    }

    .breadcrumb span {
        color: #adb5bd;
        margin: 0 10px;
    }

    /* 侧边栏美化 */
    .user-sidebar {
        flex: 0 0 250px;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(221,48,45,0.1);
        overflow: hidden;
        border: 1px solid rgba(221,48,45,0.1);
    }

    .user-info {
        background: var(--gradient-primary);
        padding: 30px 20px;
        color: #fff;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .user-info::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 60%);
        animation: rotate 20s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    .avatar {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        margin: 0 auto;
        border: 4px solid rgba(255,255,255,0.3);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        overflow: hidden;
        transition: all 0.5s;
        position: relative;
        z-index: 1;
    }

    .avatar:hover {
        transform: scale(1.1) rotate(5deg);
        border-color: rgba(255,255,255,0.8);
    }

    .avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .username {
        font-size: 20px;
        margin: 15px 0 5px;
        font-weight: 600;
    }

    .user-level {
        display: inline-block;
        padding: 4px 12px;
        background-color: rgba(255,255,255,0.2);
        border-radius: 20px;
        font-size: 12px;
        margin-top: 8px;
        backdrop-filter: blur(5px);
    }

    .menu-list {
        padding: 15px 0;
    }

    .menu-item {
        position: relative;
        transition: all 0.3s;
    }

    .menu-item a {
        display: flex;
        align-items: center;
        padding: 15px 25px;
        color: #495057;
        text-decoration: none;
        transition: all 0.3s;
        font-weight: 500;
    }

    .menu-item a i {
        margin-right: 12px;
        width: 20px;
        text-align: center;
        color: #DD302D;
        transition: all 0.3s;
    }

    .menu-item:hover a {
        color: #DD302D;
        background: linear-gradient(to right, rgba(221,48,45,0.1), transparent);
        transform: translateX(8px);
    }

    .menu-item.active {
        background: linear-gradient(to right, rgba(221,48,45,0.1), transparent);
    }

    .menu-item.active a {
        color: #DD302D;
        font-weight: 600;
    }

    /* 订单内容区美化 */
    .user-content {
        flex: 1;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(221,48,45,0.1);
        overflow: hidden;
        border: 1px solid rgba(221,48,45,0.1);
    }

    .content-header {
        background: linear-gradient(135deg, #fff5f5 0%, #ffe3e3 100%);
        padding: 25px;
        border-bottom: 1px solid rgba(221,48,45,0.1);
    }

    .content-header h3 {
        margin: 0;
        color: #DD302D;
        font-size: 24px;
        font-weight: 600;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
    }

    /* 搜索框美化 */
    .order-search {
        margin-top: 20px;
    }

    .order-search form {
        display: flex;
        gap: 10px;
    }

    .order-search input {
        flex: 1;
        padding: 12px 20px;
        border: 2px solid rgba(221,48,45,0.2);
        border-radius: 8px;
        transition: all 0.3s;
        font-size: 15px;
    }

    .order-search input:focus {
        border-color: #DD302D;
        box-shadow: 0 0 0 3px rgba(221,48,45,0.2);
        outline: none;
    }

    .order-search button {
        padding: 12px 30px;
        background: var(--gradient-primary);
        color: #fff;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .order-search button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(221,48,45,0.3);
    }

    /* 订单筛选标签美化 */
    .order-filter {
        display: flex;
        padding: 0;
        background: #fff;
        border-bottom: 1px solid rgba(221,48,45,0.1);
        overflow-x: auto;
        scrollbar-width: thin;
        scrollbar-color: #DD302D #f8f9fa;
    }

    .order-filter::-webkit-scrollbar {
        height: 4px;
    }

    .order-filter::-webkit-scrollbar-thumb {
        background: #DD302D;
        border-radius: 2px;
    }

    .order-filter a {
        padding: 15px 25px;
        color: #495057;
        text-decoration: none;
        position: relative;
        white-space: nowrap;
        transition: all 0.3s;
        font-weight: 500;
    }

    .order-filter a:hover {
        color: #DD302D;
    }

    .order-filter a.active {
        color: #DD302D;
        font-weight: 600;
    }

    .order-filter a.active:after {
        content: "";
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 3px;
        background: var(--gradient-primary);
        border-radius: 3px 3px 0 0;
    }

    /* 订单统计卡片美化 */
    .order-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin: 20px;
    }

    .stats-item {
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        transition: all 0.3s;
        border: 1px solid #dee2e6;
    }

    .stats-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 15px rgba(0,0,0,0.1);
    }

    .stats-number {
        font-size: 32px;
        font-weight: 700;
        color: #DD302D;
        margin-bottom: 5px;
    }

    .stats-label {
        color: #6c757d;
        font-size: 14px;
    }

    /* 订单列表美化 */
    .order-box {
        background: #fff;
        border-radius: 12px;
        margin: 20px;
        box-shadow: 0 4px 15px rgba(221,48,45,0.1);
        transition: all 0.3s;
        border: 1px solid rgba(221,48,45,0.1);
        overflow: hidden;
    }

    .order-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(221,48,45,0.15);
    }

    .order-header {
        background: linear-gradient(135deg, #fff5f5 0%, #ffe3e3 100%);
        padding: 15px 20px;
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .order-id {
        font-weight: 600;
        color: #DD302D;
    }

    .order-time {
        color: #6c757d;
        margin-left: 20px;
    }

    .order-status {
        margin-left: auto;
    }

    .status-pending, .status-paid, .status-shipped, 
    .status-completed, .status-cancelled, .status-refunded {
        display: inline-block;
        padding: 6px 15px;
        border-radius: 20px;
        font-size: 13px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-pending {
        background: linear-gradient(135deg, #fff3cd 0%, #ffd700 100%);
        color: #856404;
    }

    .status-paid {
        background: linear-gradient(135deg, #d1ecf1 0%, #17a2b8 100%);
        color: #0c5460;
    }

    .status-shipped {
        background: linear-gradient(135deg, #cce5ff 0%, #007bff 100%);
        color: #004085;
    }

    .status-completed {
        background: linear-gradient(135deg, #d4edda 0%, #28a745 100%);
        color: #155724;
    }

    .status-cancelled {
        background: linear-gradient(135deg, #f8d7da 0%, #dc3545 100%);
        color: #721c24;
    }

    .status-refunded {
        background: linear-gradient(135deg, #f8d7da 0%, #dc3545 100%);
        color: #721c24;
    }

    /* 商品列表美化 */
    .order-goods-item {
        display: flex;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #dee2e6;
        transition: all 0.3s;
    }

    .order-goods-item:hover {
        background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
    }

    .goods-img {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .goods-img img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s;
    }

    .goods-img:hover img {
        transform: scale(1.1);
    }

    .goods-info {
        flex: 1;
        margin-left: 20px;
    }

    .goods-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .goods-name a {
        color: #343a40;
        text-decoration: none;
        transition: color 0.3s;
    }

    .goods-name a:hover {
        color: #DD302D;
    }

    .goods-sku {
        color: #6c757d;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .goods-price {
        color: #DD302D;
        font-weight: 600;
    }

    /* 订单底部美化 */
    .order-footer {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 0 0 12px 12px;
    }

    .order-total {
        color: #495057;
    }

    .total-price {
        color: #DD302D;
        font-size: 18px;
        font-weight: 700;
    }

    .order-actions {
        display: flex;
        gap: 10px;
    }

    .order-actions a {
        padding: 8px 20px;
        border-radius: 6px;
        text-decoration: none;
        transition: all 0.3s;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        font-size: 13px;
    }

    .btn-detail {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        color: #495057;
    }

    .btn-pay {
        background: var(--gradient-primary);
        color: #fff;
    }

    .btn-cancel {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: #fff;
    }

    .btn-confirm {
        background: linear-gradient(135deg, #28a745 0%, #34ce57 100%);
        color: #fff;
    }

    .btn-logistics {
        background: #17a2b8;
        color: #fff;
    }

    .btn-review {
        background: #ffc107;
        color: #212529;
    }

    .btn-buy-again {
        background: #007bff;
        color: #fff;
    }

    .order-actions a:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.15);
    }

    /* 空订单状态美化 */
    .empty-order {
        text-align: center;
        padding: 60px 20px;
        background: linear-gradient(135deg, #fff5f5 0%, #ffe3e3 100%);
        border-radius: 12px;
        margin: 20px;
    }

    .empty-order-icon {
        font-size: 80px;
        color: #DD302D;
        margin-bottom: 20px;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0% { transform: translateY(0); }
        50% { transform: translateY(-15px); }
        100% { transform: translateY(0); }
    }

    .empty-order p {
        color: #6c757d;
        font-size: 18px;
        margin-bottom: 20px;
    }

    .empty-order .btn {
        display: inline-block;
        padding: 12px 30px;
        background: var(--gradient-primary);
        color: #fff;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s;
    }

    .empty-order .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(221,48,45,0.3);
    }

    /* 消息提示样式 */
    .message {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 8px;
        color: #fff;
        font-weight: 500;
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    }

    .message.success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }

    .message.error {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
    }

    .message.fade-out {
        animation: slideOut 0.5s ease-in forwards;
    }

    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }

    /* 响应式优化 */
    @media (max-width: 992px) {
        .user-center {
            flex-direction: column;
            padding: 20px;
        }

        .user-sidebar {
            width: 100%;
            margin-bottom: 20px;
        }

        .order-stats {
            grid-template-columns: repeat(3, 1fr);
        }
    }

    @media (max-width: 768px) {
        .order-stats {
            grid-template-columns: repeat(2, 1fr);
        }

        .order-header {
            flex-wrap: wrap;
        }

        .order-time {
            width: 100%;
            margin: 5px 0 0;
        }

        .order-goods-item {
            flex-wrap: wrap;
        }

        .goods-info {
            width: calc(100% - 100px);
        }

        .goods-num {
            width: 100%;
            margin-top: 10px;
            text-align: right;
        }

        .order-actions {
            width: 100%;
            margin-top: 10px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }
    }

    @media (max-width: 576px) {
        .order-stats {
            grid-template-columns: 1fr;
        }

        .order-filter {
            padding: 0 10px;
        }

        .order-filter a {
            padding: 10px 15px;
        }

        .order-actions {
            flex-wrap: wrap;
            gap: 5px;
        }

        .order-actions a {
            width: calc(50% - 5px);
            text-align: center;
        }

        .batch-actions {
            flex-direction: column;
            gap: 10px;
        }

        .action-buttons {
            width: 100%;
        }

        .btn-batch {
            flex: 1;
            text-align: center;
        }

        .pagination ul {
            flex-wrap: wrap;
            justify-content: center;
        }
    }

    /* 批量操作区域美化 */
    .batch-actions {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px;
        padding: 15px;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border-radius: 12px;
        border: 1px solid #dee2e6;
    }

    .select-all {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .select-all input[type="checkbox"] {
        width: 18px;
        height: 18px;
        cursor: pointer;
        accent-color: #DD302D;
    }

    .select-all label {
        color: #495057;
        font-size: 14px;
        cursor: pointer;
        user-select: none;
    }

    .action-buttons {
        display: flex;
        gap: 10px;
    }

    .btn-batch {
        padding: 8px 20px;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s;
        background: #fff;
        color: #495057;
        border: 1px solid #dee2e6;
    }

    .btn-batch:not(:disabled):hover {
        background: #f8f9fa;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .btn-batch:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        background: #e9ecef;
    }

    #batch-delete {
        color: #fff;
        background: #dc3545;
        border: none;
    }

    #batch-delete:not(:disabled):hover {
        background: #c82333;
    }

    #batch-cancel {
        color: #fff;
        background: #6c757d;
        border: none;
    }

    #batch-cancel:not(:disabled):hover {
        background: #5a6268;
    }

    /* 分页美化 */
    .pagination {
        margin: 30px 20px;
        text-align: center;
    }

    .pagination ul {
        display: inline-flex;
        gap: 5px;
        padding: 0;
        margin: 0;
        list-style: none;
    }

    .pagination li {
        display: inline-block;
    }

    .pagination li a {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 35px;
        height: 35px;
        padding: 0 12px;
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        color: #495057;
        text-decoration: none;
        transition: all 0.3s;
    }

    .pagination li a:hover {
        background: #f8f9fa;
        border-color: #DD302D;
        color: #DD302D;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .pagination li.active a {
        background: #DD302D;
        border-color: #DD302D;
        color: #fff;
    }

    .pagination li.disabled a {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .pagination li.disabled a:hover {
        transform: none;
        box-shadow: none;
    }
</style>
{% endblock %}

{% block content %}
<!-- 面包屑导航 -->
<div class="container">
    <div class="breadcrumb">
        <a href="{% url 'home:index' %}">首页</a>
        <span>&gt;</span>
        <a href="{% url 'users:center' %}">个人中心</a>
        <span>&gt;</span>
        <span>我的订单</span>
    </div>
</div>

<!-- 订单列表主体 -->
<div class="container">
    <div class="user-center clearfix">
        <!-- 侧边栏 -->
        <div class="user-sidebar">
            <div class="user-info">
                <div class="avatar">
                    <img src="{% if user.avatar %}{{ user.avatar.url }}{% else %}{% static 'images/default-avatar.png' %}{% endif %}" alt="{{ user.username }}">
                </div>
                <div class="username">{{ user.username }}</div>
                <div class="user-level">普通会员</div>
            </div>
            <ul class="menu-list">
                <li class="menu-item"><a href="{% url 'users:center' %}"><i class="fas fa-home"></i> 个人中心</a></li>
                <li class="menu-item active"><a href="{% url 'order:list' %}"><i class="fas fa-list-ul"></i> 我的订单</a></li>
                <li class="menu-item"><a href="{% url 'users:address' %}"><i class="fas fa-map-marker-alt"></i> 收货地址</a></li>
                <li class="menu-item"><a href="{% url 'users:profile' %}"><i class="fas fa-user-edit"></i> 个人资料</a></li>
                <li class="menu-item"><a href="{% url 'users:password' %}"><i class="fas fa-lock"></i> 修改密码</a></li>
            </ul>
        </div>
        
        <!-- 订单内容 -->
        <div class="user-content">
            <div class="content-header">
                <h3>我的订单</h3>
                <div class="order-search">
                    <form action="" method="get">
                        <input type="text" name="search" placeholder="输入订单号或商品名称搜索" value="{{ request.GET.search }}">
                        <button type="submit">搜索</button>
                    </form>
                </div>
            </div>
            
            <!-- 订单筛选 -->
            <div class="order-filter">
                <a href="{% url 'order:list' %}" class="{% if not status %}active{% endif %}">全部订单</a>
                <a href="{% url 'order:list' %}?status=pending" class="{% if status == 'pending' %}active{% endif %}">待付款</a>
                <a href="{% url 'order:list' %}?status=paid" class="{% if status == 'paid' %}active{% endif %}">待发货</a>
                <a href="{% url 'order:list' %}?status=shipped" class="{% if status == 'shipped' %}active{% endif %}">待收货</a>
                <a href="{% url 'order:list' %}?status=received" class="{% if status == 'received' %}active{% endif %}">已完成</a>
                <a href="{% url 'order:list' %}?status=cancelled" class="{% if status == 'cancelled' %}active{% endif %}">已取消</a>
            </div>
            
            <!-- 订单统计 -->
            <div class="order-stats">
                <div class="stats-item">
                    <div class="stats-number">{{ order_stats.total|default:"0" }}</div>
                    <div class="stats-label">全部订单</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number">{{ order_stats.pending|default:"0" }}</div>
                    <div class="stats-label">待付款</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number">{{ order_stats.paid|default:"0" }}</div>
                    <div class="stats-label">待发货</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number">{{ order_stats.shipped|default:"0" }}</div>
                    <div class="stats-label">待收货</div>
                </div>
                <div class="stats-item">
                    <div class="stats-number">{{ order_stats.received|default:"0" }}</div>
                    <div class="stats-label">已完成</div>
                </div>
            </div>
            
            <!-- 批量操作 -->
            <div class="batch-actions">
                <div class="select-all">
                    <input type="checkbox" id="select-all">
                    <label for="select-all">全选</label>
                </div>
                <div class="action-buttons">
                    <button id="batch-delete" class="btn-batch" disabled>批量删除</button>
                    <button id="batch-cancel" class="btn-batch" disabled>批量取消</button>
                </div>
            </div>
            
            <div class="content-body">
                {% if orders %}
                <div class="order-list">
                    {% for order in orders %}
                    <div class="order-box">
                        <div class="order-header">
                            <div class="order-checkbox-wrapper leftfix">
                                <input type="checkbox" class="order-checkbox" data-id="{{ order.id }}">
                            </div>
                            <div class="order-id leftfix">订单号：{{ order.order_number }}</div>
                            <div class="order-time leftfix">下单时间：{{ order.created_time|date:"Y-m-d H:i" }}</div>
                            <div class="order-status rightfix">
                                {% if order.status == 'pending' %}
                                <span class="status-pending">待付款</span>
                                {% elif order.status == 'paid' %}
                                <span class="status-paid">待发货</span>
                                {% elif order.status == 'shipped' %}
                                <span class="status-shipped">待收货</span>
                                {% elif order.status == 'received' %}
                                <span class="status-completed">已完成</span>
                                {% elif order.status == 'cancelled' %}
                                <span class="status-cancelled">已取消</span>
                                {% elif order.status == 'refunded' %}
                                <span class="status-refunded">已退款</span>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="order-body">
                            <div class="order-goods">
                                {% for item in order.items.all %}
                                <div class="order-goods-item clearfix">
                                    <div class="goods-img">
                                        <a href="{% url 'goods:detail' item.product.id %}">
                                            <img src="{% if item.product.image %}{{ item.product.image.url }}{% elif item.product.main_image %}{{ item.product.main_image.url }}{% else %}{% static 'images/no-image.png' %}{% endif %}" alt="{{ item.product.name }}">
                                        </a>
                                    </div>
                                    <div class="goods-info">
                                        <div class="goods-name">
                                            <a href="{% url 'goods:detail' item.product.id %}">{{ item.product.name }}</a>
                                        </div>
                                        {% if item.sku_text %}
                                        <div class="goods-sku">{{ item.sku_text }}</div>
                                        {% endif %}
                                        <div class="goods-price">
                                            单价：¥{{ item.price }} × {{ item.quantity }}
                                        </div>
                                    </div>
                                    <div class="goods-num rightfix">
                                        数量：{{ item.quantity }}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <div class="order-footer clearfix">
                            <div class="order-total">
                                共 {{ order.items.count }} 件商品，实付款：<span class="total-price">¥{{ order.pay_amount }}</span>
                            </div>
                            <div class="order-actions">
                                <a href="{% url 'order:detail' order.id %}" class="btn-detail">查看详情</a>
                                {% if order.status == 'pending' %}
                                <a href="{% url 'order:create' %}?order_id={{ order.id }}" class="btn-pay">去付款</a>
                                <a href="javascript:;" class="btn-cancel" data-id="{{ order.id }}">取消订单</a>
                                {% elif order.status == 'shipped' %}
                                <a href="javascript:;" class="btn-confirm" data-id="{{ order.id }}">确认收货</a>
                                <a href="javascript:;" class="btn-logistics" data-id="{{ order.id }}">查看物流</a>
                                {% elif order.status == 'received' %}
                                <a href="{% url 'goods:review' %}?order_id={{ order.id }}" class="btn-review">去评价</a>
                                <a href="javascript:;" class="btn-buy-again" data-id="{{ order.id }}">再次购买</a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- 分页 -->
                {% if is_paginated %}
                <div class="pagination">
                    <ul class="clearfix">
                        {% if page_obj.has_previous %}
                        <li><a href="?page=1">&laquo; 首页</a></li>
                        <li><a href="?page={{ page_obj.previous_page_number }}">&lsaquo; 上一页</a></li>
                        {% endif %}
                        
                        {% for i in paginator.page_range %}
                            {% if page_obj.number == i %}
                            <li class="active"><a href="?page={{ i }}">{{ i }}</a></li>
                            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                            <li><a href="?page={{ i }}">{{ i }}</a></li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                        <li><a href="?page={{ page_obj.next_page_number }}">下一页 &rsaquo;</a></li>
                        <li><a href="?page={{ paginator.num_pages }}">末页 &raquo;</a></li>
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
                {% else %}
                <div class="empty-order">
                    <div class="empty-order-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <p>您还没有订单，快去购物吧~</p>
                    <a href="{% url 'goods:list' %}" class="btn">去购物</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 批量操作功能
        const selectAllCheckbox = document.getElementById('select-all');
        const orderCheckboxes = document.querySelectorAll('.order-checkbox');
        const batchDeleteBtn = document.getElementById('batch-delete');
        const batchCancelBtn = document.getElementById('batch-cancel');
        
        // 全选/取消全选
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                orderCheckboxes.forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                updateBatchButtons();
            });
        }
        
        // 单个订单选择
        orderCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                updateBatchButtons();
                // 检查是否全部选中，更新全选框状态
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = document.querySelectorAll('.order-checkbox:not(:checked)').length === 0;
                }
            });
        });
        
        // 更新批量操作按钮状态
        function updateBatchButtons() {
            const selectedCount = document.querySelectorAll('.order-checkbox:checked').length;
            if (batchDeleteBtn) batchDeleteBtn.disabled = selectedCount === 0;
            if (batchCancelBtn) batchCancelBtn.disabled = selectedCount === 0;
        }
        
        // 批量删除
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', function() {
                const selectedOrders = document.querySelectorAll('.order-checkbox:checked');
                if (selectedOrders.length === 0) {
                    showMessage('请选择要删除的订单', 'error');
                    return;
                }
                
                // 使用通用确认对话框替代原生confirm
                showConfirm({
                    title: '删除订单',
                    message: `确定要删除选中的 <strong>${selectedOrders.length}</strong> 个订单吗？<br>删除后将无法恢复！`,
                    type: 'danger',
                    confirmText: '确认删除',
                    confirmIcon: 'fa-trash-alt',
                    onConfirm: function() {
                        const orderIds = Array.from(selectedOrders).map(checkbox => checkbox.dataset.id);
                        const confirmButton = document.getElementById('confirmButton');
                        
                        // 显示加载状态
                        confirmButton.disabled = true;
                        confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                        
                        fetch('/api/order/batch-delete/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': getCookie('csrftoken')
                            },
                            body: JSON.stringify({
                                order_ids: orderIds
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 200) {
                                // 显示成功动画
                                document.getElementById('confirmIcon').className = 'fas fa-check';
                                document.querySelector('.confirm-icon').style.borderColor = '#28a745';
                                document.getElementById('confirmIcon').style.color = '#28a745';
                                document.getElementById('confirmTitle').textContent = '删除成功';
                                document.getElementById('confirmMessage').textContent = '订单已成功删除';
                                
                                // 1秒后刷新页面
                                setTimeout(() => {
                                    location.reload();
                                }, 1000);
                            } else {
                                // 显示错误状态
                                confirmButton.disabled = false;
                                confirmButton.innerHTML = '<i class="fas fa-trash-alt"></i> 确认删除';
                                document.getElementById('confirmMessage').textContent = '删除失败：' + data.msg;
                                document.getElementById('confirmMessage').style.color = '#dc3545';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // 显示错误状态
                            confirmButton.disabled = false;
                            confirmButton.innerHTML = '<i class="fas fa-trash-alt"></i> 确认删除';
                            document.getElementById('confirmMessage').textContent = '删除失败，请稍后重试';
                            document.getElementById('confirmMessage').style.color = '#dc3545';
                        });
                    }
                });
            });
        }
        
        // 批量取消
        if (batchCancelBtn) {
            batchCancelBtn.addEventListener('click', function() {
                const selectedOrders = document.querySelectorAll('.order-checkbox:checked');
                if (selectedOrders.length === 0) {
                    showMessage('请选择要取消的订单', 'error');
                    return;
                }
                
                // 使用通用确认对话框替代原生confirm
                showConfirm({
                    title: '取消订单',
                    message: `确定要取消选中的 <strong>${selectedOrders.length}</strong> 个订单吗？`,
                    type: 'warning',
                    confirmText: '确认取消',
                    confirmIcon: 'fa-ban',
                    onConfirm: function() {
                        const orderIds = Array.from(selectedOrders).map(checkbox => checkbox.dataset.id);
                        const confirmButton = document.getElementById('confirmButton');
                        
                        // 显示加载状态
                        confirmButton.disabled = true;
                        confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                        
                        fetch('/api/order/batch-cancel/', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRFToken': getCookie('csrftoken')
                            },
                            body: JSON.stringify({
                                order_ids: orderIds
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 200) {
                                // 显示成功动画
                                document.getElementById('confirmIcon').className = 'fas fa-check';
                                document.querySelector('.confirm-icon').style.borderColor = '#28a745';
                                document.getElementById('confirmIcon').style.color = '#28a745';
                                document.getElementById('confirmTitle').textContent = '取消成功';
                                document.getElementById('confirmMessage').textContent = '订单已成功取消';
                                
                                // 1秒后刷新页面
                                setTimeout(() => {
                                    location.reload();
                                }, 1000);
                            } else {
                                // 显示错误状态
                                confirmButton.disabled = false;
                                confirmButton.innerHTML = '<i class="fas fa-ban"></i> 确认取消';
                                document.getElementById('confirmMessage').textContent = '取消失败：' + data.msg;
                                document.getElementById('confirmMessage').style.color = '#dc3545';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // 显示错误状态
                            confirmButton.disabled = false;
                            confirmButton.innerHTML = '<i class="fas fa-ban"></i> 确认取消';
                            document.getElementById('confirmMessage').textContent = '取消失败，请稍后重试';
                            document.getElementById('confirmMessage').style.color = '#dc3545';
                        });
                    }
                });
            });
        }
        
        // 取消订单
        document.querySelectorAll('.btn-cancel').forEach(btn => {
            btn.addEventListener('click', function() {
                const orderId = this.dataset.id;
                
                // 使用通用确认对话框替代原生confirm
                showConfirm({
                    title: '取消订单',
                    message: '确定要取消该订单吗？',
                    type: 'warning',
                    confirmText: '确认取消',
                    confirmIcon: 'fa-ban',
                    onConfirm: function() {
                        const confirmButton = document.getElementById('confirmButton');
                        
                        // 显示加载状态
                        confirmButton.disabled = true;
                        confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                        
                        fetch(`/api/order/cancel/${orderId}/`, {
                            method: 'POST',
                            headers: {
                                'X-CSRFToken': getCookie('csrftoken')
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 200) {
                                // 显示成功动画
                                document.getElementById('confirmIcon').className = 'fas fa-check';
                                document.querySelector('.confirm-icon').style.borderColor = '#28a745';
                                document.getElementById('confirmIcon').style.color = '#28a745';
                                document.getElementById('confirmTitle').textContent = '操作成功';
                                document.getElementById('confirmMessage').textContent = '订单已成功取消';
                                
                                // 1秒后刷新页面
                                setTimeout(() => {
                                    location.reload();
                                }, 1000);
                            } else {
                                // 显示错误状态
                                confirmButton.disabled = false;
                                confirmButton.innerHTML = '<i class="fas fa-ban"></i> 确认取消';
                                document.getElementById('confirmMessage').textContent = data.msg || '操作失败';
                                document.getElementById('confirmMessage').style.color = '#dc3545';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // 显示错误状态
                            confirmButton.disabled = false;
                            confirmButton.innerHTML = '<i class="fas fa-ban"></i> 确认取消';
                            document.getElementById('confirmMessage').textContent = '操作失败，请稍后重试';
                            document.getElementById('confirmMessage').style.color = '#dc3545';
                        });
                    }
                });
            });
        });
        
        // 确认收货
        document.querySelectorAll('.btn-confirm').forEach(btn => {
            btn.addEventListener('click', function() {
                const orderId = this.dataset.id;
                
                // 使用通用确认对话框替代原生confirm
                showConfirm({
                    title: '确认收货',
                    message: '确认已收到商品吗？<br>确认后订单将完成。',
                    type: 'success',
                    confirmText: '确认收货',
                    confirmIcon: 'fa-check',
                    onConfirm: function() {
                        const confirmButton = document.getElementById('confirmButton');
                        
                        // 显示加载状态
                        confirmButton.disabled = true;
                        confirmButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
                        
                        fetch(`/api/order/confirm/${orderId}/`, {
                            method: 'POST',
                            headers: {
                                'X-CSRFToken': getCookie('csrftoken')
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.code === 200) {
                                // 显示成功动画
                                document.getElementById('confirmTitle').textContent = '操作成功';
                                document.getElementById('confirmMessage').textContent = '已确认收货，感谢您的购买！';
                                
                                // 1秒后刷新页面
                                setTimeout(() => {
                                    location.reload();
                                }, 1000);
                            } else {
                                // 显示错误状态
                                confirmButton.disabled = false;
                                confirmButton.innerHTML = '<i class="fas fa-check"></i> 确认收货';
                                document.getElementById('confirmMessage').textContent = data.msg || '操作失败';
                                document.getElementById('confirmMessage').style.color = '#dc3545';
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // 显示错误状态
                            confirmButton.disabled = false;
                            confirmButton.innerHTML = '<i class="fas fa-check"></i> 确认收货';
                            document.getElementById('confirmMessage').textContent = '操作失败，请稍后重试';
                            document.getElementById('confirmMessage').style.color = '#dc3545';
                        });
                    }
                });
            });
        });
        
        // 再次购买
        document.querySelectorAll('.btn-buy-again').forEach(btn => {
            btn.addEventListener('click', function() {
                const orderId = this.dataset.id;
                
                fetch(`/api/order/rebuy/${orderId}/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        window.location.href = '{% url "order:cart" %}';
                    } else {
                        showSystemMessage(data.msg, '再次购买', 'error');
                    }
                });
            });
        });
        
        // 获取Cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // 显示消息提示
        function showMessage(message, type) {
            // 使用我们全局定义的showMessage函数
            window.showMessage(message, type);
        }
    });
</script>
{% endblock %} 