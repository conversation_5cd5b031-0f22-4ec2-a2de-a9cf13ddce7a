# 🔑 MARS BUY 系统管理员账户信息

## 👑 超级管理员账户

根据代码分析，系统中配置的超级管理员账户如下：

### 1. **主要管理员账户**
```
用户名: admin123
密码: admin123
邮箱: <EMAIL>
权限: 超级管理员 (is_superuser=True, is_staff=True)
用途: 主要管理员账户
```

### 2. **测试管理员账户**
```
用户名: 12345
密码: 12345
邮箱: <EMAIL>
权限: 超级管理员 (is_superuser=True, is_staff=True)
用途: 测试用管理员账户
```

### 3. **默认管理员账户**
```
用户名: admin
密码: admin123
邮箱: <EMAIL>
权限: 超级管理员 (is_superuser=True, is_staff=True)
用途: 系统默认管理员账户
```

## 👤 测试用户账户

### 1. **主要测试用户**
```
用户名: testuser
密码: 123456
邮箱: <EMAIL>
权限: 普通用户
用途: 测试普通用户功能
```

### 2. **备用测试用户**
```
用户名: user123
密码: 123456
邮箱: <EMAIL>
权限: 普通用户
用途: 备用测试账户
```

## 🎭 批量测试用户

系统还创建了多个测试用户，默认密码都是 **123456**：

- **RainbowWang** (王雨晴) - <EMAIL>
- **CoolChen** (陈俊杰) - <EMAIL>
- **PoetryLi** (李诗涵) - <EMAIL>
- **BraveZhang** (张浩然) - <EMAIL>
- **DreamLiu** (刘梦婷) - <EMAIL>

## 🌐 登录地址

### 前台用户登录
```
URL: http://127.0.0.1:8001/users/login/
支持: 管理员和普通用户登录
功能: 选择用户类型登录
```

### 后台管理员登录
```
URL: http://127.0.0.1:8003/admin_panel/login/
支持: 仅管理员登录
功能: 后台管理系统
```

### Django Admin (如果启用)
```
URL: http://127.0.0.1:8001/admin/
支持: 超级管理员登录
功能: Django原生管理界面
```

## 🔧 管理员权限说明

### 超级管理员 (is_superuser=True)
- ✅ 访问所有后台功能
- ✅ 用户管理
- ✅ 商品管理
- ✅ 订单管理
- ✅ 系统设置
- ✅ 数据统计
- ✅ 权限管理

### 普通管理员 (is_staff=True, is_superuser=False)
- ✅ 基本后台功能
- ✅ 商品管理
- ✅ 订单管理
- ❌ 用户权限管理
- ❌ 系统核心设置

## 🚀 快速登录测试

### 测试超级管理员功能
1. 访问：http://127.0.0.1:8001/users/login/
2. 用户名：`admin123`
3. 密码：`admin123`
4. 选择：管理员登录
5. 应该跳转到后台管理系统

### 测试普通用户功能
1. 访问：http://127.0.0.1:8001/users/login/
2. 用户名：`testuser`
3. 密码：`123456`
4. 选择：普通用户登录
5. 应该跳转到前台首页

## 🔒 安全建议

### 生产环境注意事项
1. **修改默认密码**：所有默认密码都应该修改
2. **删除测试账户**：生产环境应删除所有测试账户
3. **启用双因素认证**：为管理员账户启用2FA
4. **定期审计**：定期检查管理员账户权限

### 密码安全
- 当前所有账户都使用简单密码（测试用）
- 生产环境应使用复杂密码
- 建议密码长度至少8位，包含大小写字母、数字和特殊字符

## 📝 账户管理命令

### 创建超级管理员
```bash
cd D:\python\shop\shop_front
python manage.py createsuperuser
```

### 重置用户密码
```bash
cd D:\python\shop\shop_front
python manage.py shell
>>> from users.models import User
>>> user = User.objects.get(username='admin123')
>>> user.set_password('新密码')
>>> user.save()
```

### 查看所有管理员
```bash
cd D:\python\shop\shop_front
python manage.py shell
>>> from users.models import User
>>> User.objects.filter(is_superuser=True).values('username', 'email', 'is_active')
```

## ⚠️ 重要提醒

1. **当前环境为开发/测试环境**，使用简单密码
2. **生产部署前必须修改所有默认密码**
3. **定期备份用户数据**
4. **监控管理员登录日志**

---

**最后更新**: 2024年当前日期
**系统版本**: MARS BUY v1.0
**维护人员**: 系统管理员
