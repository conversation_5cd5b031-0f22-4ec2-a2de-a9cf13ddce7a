/**
 * 前台实时更新功能
 * 监听后台商品修改，实时更新前台显示
 */

class RealtimeUpdater {
    constructor(options = {}) {
        this.options = {
            checkInterval: 30000, // 检查间隔（毫秒）
            apiUrl: '/goods/api/check_updates/',
            latestProductsUrl: '/goods/api/latest_products/',
            enableNotifications: true,
            enableAutoRefresh: true,
            ...options
        };
        
        this.lastCheckTime = Date.now() / 1000;
        this.isChecking = false;
        this.checkTimer = null;
        this.productIds = [];
        
        this.init();
    }
    
    init() {
        console.log('初始化实时更新功能...');
        
        // 收集当前页面的商品ID
        this.collectProductIds();
        
        // 开始定期检查
        if (this.options.enableAutoRefresh) {
            this.startPeriodicCheck();
        }
        
        // 监听页面可见性变化
        this.handleVisibilityChange();
        
        // 添加手动刷新按钮
        this.addRefreshButton();
    }
    
    collectProductIds() {
        // 从页面中收集商品ID
        const productElements = document.querySelectorAll('[data-product-id]');
        this.productIds = Array.from(productElements).map(el => el.dataset.productId);
        
        console.log(`收集到 ${this.productIds.length} 个商品ID:`, this.productIds);
    }
    
    startPeriodicCheck() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
        }
        
        this.checkTimer = setInterval(() => {
            this.checkForUpdates();
        }, this.options.checkInterval);
        
        console.log(`开始定期检查更新，间隔: ${this.options.checkInterval}ms`);
    }
    
    stopPeriodicCheck() {
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
        console.log('停止定期检查更新');
    }
    
    async checkForUpdates() {
        if (this.isChecking) {
            return;
        }
        
        this.isChecking = true;
        
        try {
            const params = new URLSearchParams({
                last_check: this.lastCheckTime,
                product_ids: this.productIds.join(',')
            });
            
            const response = await fetch(`${this.options.apiUrl}?${params}`);
            const data = await response.json();
            
            if (data.success) {
                if (data.has_updates) {
                    console.log('检测到商品更新:', data);
                    this.handleUpdates(data);
                }
                
                this.lastCheckTime = data.check_time;
            } else {
                console.error('检查更新失败:', data.error);
            }
            
        } catch (error) {
            console.error('检查更新异常:', error);
        } finally {
            this.isChecking = false;
        }
    }
    
    handleUpdates(data) {
        if (data.updated_products && data.updated_products.length > 0) {
            // 处理具体商品更新
            data.updated_products.forEach(product => {
                this.updateProductDisplay(product);
            });
            
            // 显示通知
            if (this.options.enableNotifications) {
                this.showUpdateNotification(data.updated_products.length);
            }
        }
        
        // 如果有全局更新，可能需要刷新整个列表
        if (data.has_updates && this.options.enableAutoRefresh) {
            this.refreshProductList();
        }
    }
    
    updateProductDisplay(product) {
        const productElement = document.querySelector(`[data-product-id="${product.id}"]`);
        
        if (!productElement) {
            return;
        }
        
        if (product.deleted) {
            // 商品已删除，移除元素
            productElement.remove();
            this.showMessage('商品已下架', 'warning');
            return;
        }
        
        // 更新商品信息
        const nameElement = productElement.querySelector('.product-name');
        if (nameElement && product.name) {
            nameElement.textContent = product.name;
        }
        
        const priceElement = productElement.querySelector('.product-price');
        if (priceElement && product.price) {
            priceElement.textContent = `¥${product.price}`;
        }
        
        // 更新状态标签
        this.updateProductStatus(productElement, product);
        
        // 添加更新动画
        this.addUpdateAnimation(productElement);
        
        console.log(`更新商品显示: ${product.name}`);
    }
    
    updateProductStatus(element, product) {
        // 更新热门标签
        const hotBadge = element.querySelector('.hot-badge');
        if (hotBadge) {
            hotBadge.style.display = product.is_hot ? 'inline-block' : 'none';
        }
        
        // 更新新品标签
        const newBadge = element.querySelector('.new-badge');
        if (newBadge) {
            newBadge.style.display = product.is_new ? 'inline-block' : 'none';
        }
        
        // 更新上架状态
        if (!product.is_active) {
            element.classList.add('product-inactive');
            element.style.opacity = '0.5';
        } else {
            element.classList.remove('product-inactive');
            element.style.opacity = '1';
        }
    }
    
    addUpdateAnimation(element) {
        element.classList.add('product-updated');
        
        // 添加闪烁效果
        element.style.transition = 'all 0.3s ease';
        element.style.backgroundColor = '#fff3cd';
        
        setTimeout(() => {
            element.style.backgroundColor = '';
            element.classList.remove('product-updated');
        }, 2000);
    }
    
    async refreshProductList() {
        try {
            const response = await fetch(this.options.latestProductsUrl);
            const data = await response.json();
            
            if (data.success) {
                console.log('获取最新商品列表:', data);
                // 这里可以根据需要更新整个商品列表
                // 或者显示一个提示让用户手动刷新
                this.showRefreshPrompt();
            }
            
        } catch (error) {
            console.error('刷新商品列表失败:', error);
        }
    }
    
    showUpdateNotification(count) {
        const message = `检测到 ${count} 个商品更新`;
        this.showMessage(message, 'info');
    }
    
    showRefreshPrompt() {
        const prompt = document.createElement('div');
        prompt.className = 'update-prompt';
        prompt.innerHTML = `
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-sync-alt"></i>
                商品信息已更新，<a href="javascript:void(0)" onclick="location.reload()">点击刷新</a>查看最新内容
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        document.body.insertBefore(prompt, document.body.firstChild);
        
        // 5秒后自动隐藏
        setTimeout(() => {
            if (prompt.parentNode) {
                prompt.remove();
            }
        }, 5000);
    }
    
    showMessage(message, type = 'info') {
        // 使用全局消息提示组件
        if (typeof window.showMessage === 'function') {
            window.showMessage(message, type);
            return;
        }
        
        // 如果全局组件不存在，使用原有的Toast实现
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">${message}</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        
        // 添加到页面
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.appendChild(toast);
        
        // 显示toast
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // 自动移除
        toast.addEventListener('hidden.bs.toast', () => {
            toast.remove();
        });
    }
    
    addRefreshButton() {
        // 添加手动刷新按钮
        const refreshBtn = document.createElement('button');
        refreshBtn.className = 'btn btn-outline-primary btn-sm position-fixed';
        refreshBtn.style.cssText = 'bottom: 20px; right: 20px; z-index: 1000;';
        refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 检查更新';
        refreshBtn.onclick = () => this.manualCheck();
        
        document.body.appendChild(refreshBtn);
    }
    
    async manualCheck() {
        console.log('手动检查更新...');
        await this.checkForUpdates();
        this.showMessage('检查完成', 'success');
    }
    
    handleVisibilityChange() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.stopPeriodicCheck();
            } else {
                this.startPeriodicCheck();
                // 页面重新可见时立即检查一次
                setTimeout(() => this.checkForUpdates(), 1000);
            }
        });
    }
    
    destroy() {
        this.stopPeriodicCheck();
        console.log('实时更新功能已销毁');
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    // 只在商品相关页面启用实时更新
    const isProductPage = document.querySelector('[data-product-id]') || 
                         window.location.pathname.includes('/goods/') ||
                         window.location.pathname === '/';
    
    if (isProductPage) {
        window.realtimeUpdater = new RealtimeUpdater({
            checkInterval: 30000, // 30秒检查一次
            enableNotifications: true,
            enableAutoRefresh: true
        });
        
        console.log('商品实时更新功能已启动');
    }
});

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
    .product-updated {
        animation: pulse 1s ease-in-out;
    }
    
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }
    
    .product-inactive {
        filter: grayscale(50%);
    }
    
    .update-prompt {
        position: fixed;
        top: 10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 9999;
        width: auto;
        max-width: 500px;
    }
`;
document.head.appendChild(style);
