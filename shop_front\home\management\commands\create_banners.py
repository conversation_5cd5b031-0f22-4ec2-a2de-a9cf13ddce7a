import os
from django.core.management.base import BaseCommand
from django.core.files import File
from home.models import Banner
from django.utils.text import slugify
import glob

class Command(BaseCommand):
    help = '创建初始轮播图数据'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始创建轮播图数据...'))
        
        # 清空现有轮播图数据
        Banner.objects.all().delete()
        self.stdout.write(self.style.SUCCESS('已清空现有轮播图数据'))
        
        # 轮播图数据
        banners_data = [
            {
                'title': '夏季数码促销',
                'image_path': 'banners/轮播图 (1).png',
                'url': '/goods/list/',
                'sort_order': 1,
            },
            {
                'title': '家电满减活动',
                'image_path': 'banners/轮播图 (2).png',
                'url': '/goods/list/',
                'sort_order': 2,
            },
            {
                'title': '新品美妆上市',
                'image_path': 'banners/轮播图 (3).png',
                'url': '/goods/list/',
                'sort_order': 3,
            },
            {
                'title': '母婴促销',
                'image_path': 'banners/轮播图.png',
                'url': '/goods/list/',
                'sort_order': 4,
            },
        ]

        # 创建轮播图
        for banner_data in banners_data:
            banner = Banner.objects.create(
                title=banner_data['title'],
                image=banner_data['image_path'],
                url=banner_data['url'],
                sort_order=banner_data['sort_order'],
                is_active=True
            )
            self.stdout.write(self.style.SUCCESS(f'已创建轮播图: {banner.title}'))
        
        self.stdout.write(self.style.SUCCESS('轮播图数据创建完成！')) 