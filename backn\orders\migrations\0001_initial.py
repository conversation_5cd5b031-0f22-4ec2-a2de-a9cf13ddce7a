# Generated by Django 4.2.22 on 2025-06-09 12:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('goods', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.CharField(db_index=True, max_length=50, unique=True, verbose_name='订单号')),
                ('status', models.CharField(choices=[('pending', '待付款'), ('paid', '已付款'), ('shipped', '已发货'), ('delivered', '已送达'), ('cancelled', '已取消'), ('refunded', '已退款')], db_index=True, default='pending', max_length=20, verbose_name='订单状态')),
                ('payment_method', models.CharField(blank=True, choices=[('alipay', '支付宝'), ('wechat', '微信支付'), ('balance', '余额支付')], max_length=20, null=True, verbose_name='支付方式')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='订单总额')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='优惠金额')),
                ('final_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='实付金额')),
                ('receiver_name', models.CharField(max_length=100, verbose_name='收货人')),
                ('receiver_phone', models.CharField(max_length=20, verbose_name='收货电话')),
                ('receiver_address', models.TextField(verbose_name='收货地址')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('paid_time', models.DateTimeField(blank=True, null=True, verbose_name='付款时间')),
                ('shipped_time', models.DateTimeField(blank=True, null=True, verbose_name='发货时间')),
                ('delivered_time', models.DateTimeField(blank=True, null=True, verbose_name='送达时间')),
                ('remark', models.TextField(blank=True, verbose_name='订单备注')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(max_length=200, verbose_name='商品名称')),
                ('product_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='商品单价')),
                ('quantity', models.IntegerField(default=1, verbose_name='购买数量')),
                ('subtotal', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='小计')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='orders.order', verbose_name='订单')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_items', to='goods.product', verbose_name='商品')),
            ],
            options={
                'verbose_name': '订单商品项',
                'verbose_name_plural': '订单商品项',
            },
        ),
    ]
