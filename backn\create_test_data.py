#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backn.settings')
django.setup()

from goods.models import Category, Product
from users.models import User
from orders.models import Order, OrderItem
from decimal import Decimal
import random
from datetime import datetime, timedelta

def create_categories():
    """创建商品分类"""
    categories_data = [
        {'name': '数码电子', 'sort_order': 1},
        {'name': '家居生活', 'sort_order': 2},
        {'name': '食品饮料', 'sort_order': 3},
        {'name': '运动户外', 'sort_order': 4},
        {'name': '服装鞋包', 'sort_order': 5},
    ]
    
    created_categories = []
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            name=cat_data['name'],
            defaults={'sort_order': cat_data['sort_order']}
        )
        if created:
            print(f"创建分类: {category.name}")
        created_categories.append(category)
    
    return created_categories

def create_products(categories):
    """创建商品"""
    products_data = [
        {'name': 'iPhone 15 Pro', 'category': '数码电子', 'price': 8999.00, 'stock': 50, 'sales': 25, 'is_hot': True},
        {'name': 'MacBook Air M2', 'category': '数码电子', 'price': 9999.00, 'stock': 30, 'sales': 15, 'is_hot': True},
        {'name': '小米13 Ultra', 'category': '数码电子', 'price': 5999.00, 'stock': 80, 'sales': 45, 'is_new': True},
        {'name': 'AirPods Pro 2', 'category': '数码电子', 'price': 1899.00, 'stock': 100, 'sales': 60},
        {'name': '智能扫地机器人', 'category': '家居生活', 'price': 2999.00, 'stock': 25, 'sales': 12, 'is_hot': True},
        {'name': '空气净化器', 'category': '家居生活', 'price': 1599.00, 'stock': 40, 'sales': 28},
        {'name': '电动牙刷', 'category': '家居生活', 'price': 299.00, 'stock': 200, 'sales': 150, 'is_new': True},
        {'name': '咖啡机', 'category': '家居生活', 'price': 3999.00, 'stock': 15, 'sales': 8},
        {'name': '有机牛奶', 'category': '食品饮料', 'price': 25.90, 'stock': 500, 'sales': 320},
        {'name': '进口红酒', 'category': '食品饮料', 'price': 299.00, 'stock': 80, 'sales': 45, 'is_hot': True},
        {'name': '坚果礼盒', 'category': '食品饮料', 'price': 128.00, 'stock': 150, 'sales': 89, 'is_new': True},
        {'name': '跑步鞋', 'category': '运动户外', 'price': 599.00, 'stock': 120, 'sales': 78, 'is_hot': True},
        {'name': '瑜伽垫', 'category': '运动户外', 'price': 199.00, 'stock': 200, 'sales': 156},
        {'name': '登山包', 'category': '运动户外', 'price': 899.00, 'stock': 60, 'sales': 34, 'is_new': True},
        {'name': '羽毛球拍', 'category': '运动户外', 'price': 399.00, 'stock': 80, 'sales': 52},
        {'name': '连衣裙', 'category': '服装鞋包', 'price': 299.00, 'stock': 100, 'sales': 67, 'is_new': True},
        {'name': '休闲鞋', 'category': '服装鞋包', 'price': 399.00, 'stock': 150, 'sales': 89},
        {'name': '手提包', 'category': '服装鞋包', 'price': 599.00, 'stock': 80, 'sales': 45, 'is_hot': True},
    ]
    
    category_dict = {cat.name: cat for cat in categories}
    created_products = []
    
    for prod_data in products_data:
        category = category_dict.get(prod_data['category'])
        if not category:
            continue
            
        product, created = Product.objects.get_or_create(
            name=prod_data['name'],
            defaults={
                'category': category,
                'description': f"这是一款优质的{prod_data['name']}，性价比很高，值得购买。",
                'price': Decimal(str(prod_data['price'])),
                'stock': prod_data['stock'],
                'sales': prod_data['sales'],
                'is_hot': prod_data.get('is_hot', False),
                'is_new': prod_data.get('is_new', False),
                'is_active': True,
            }
        )
        if created:
            print(f"创建商品: {product.name}")
        created_products.append(product)
    
    return created_products

def create_test_users():
    """创建测试用户"""
    users_data = [
        {'username': 'testuser1', 'email': '<EMAIL>', 'is_vip': True, 'balance': 1000.00},
        {'username': 'testuser2', 'email': '<EMAIL>', 'is_vip': False, 'balance': 500.00},
        {'username': 'testuser3', 'email': '<EMAIL>', 'is_vip': True, 'balance': 2000.00},
        {'username': 'vipuser', 'email': '<EMAIL>', 'is_vip': True, 'balance': 5000.00},
        {'username': 'normaluser', 'email': '<EMAIL>', 'is_vip': False, 'balance': 200.00},
    ]
    
    created_users = []
    for user_data in users_data:
        user, created = User.objects.get_or_create(
            username=user_data['username'],
            defaults={
                'email': user_data['email'],
                'is_vip': user_data['is_vip'],
                'balance': Decimal(str(user_data['balance'])),
                'is_active': True,
            }
        )
        if created:
            user.set_password('123456')  # 设置默认密码
            user.save()
            print(f"创建用户: {user.username}")
        created_users.append(user)
    
    return created_users

def create_test_orders(users, products):
    """创建测试订单"""
    if not users or not products:
        print("没有用户或商品，跳过订单创建")
        return []
    
    statuses = ['pending', 'paid', 'shipped', 'delivered', 'cancelled']
    payment_methods = ['alipay', 'wechat', 'balance']
    
    created_orders = []
    for i in range(20):  # 创建20个测试订单
        user = random.choice(users)
        status = random.choice(statuses)
        
        # 生成订单号
        order_no = f"ORD{datetime.now().strftime('%Y%m%d')}{str(i+1).zfill(4)}"
        
        # 随机选择1-3个商品
        order_products = random.sample(products, random.randint(1, 3))
        
        total_amount = Decimal('0')
        for product in order_products:
            quantity = random.randint(1, 3)
            total_amount += product.price * quantity
        
        # 随机优惠
        discount_amount = Decimal(str(random.uniform(0, float(total_amount) * 0.1)))
        final_amount = total_amount - discount_amount
        
        order, created = Order.objects.get_or_create(
            order_no=order_no,
            defaults={
                'user': user,
                'status': status,
                'payment_method': random.choice(payment_methods) if status != 'pending' else None,
                'total_amount': total_amount,
                'discount_amount': discount_amount,
                'final_amount': final_amount,
                'receiver_name': f"{user.username}收货人",
                'receiver_phone': f"138{random.randint(10000000, 99999999)}",
                'receiver_address': f"北京市朝阳区测试地址{i+1}号",
                'created_time': datetime.now() - timedelta(days=random.randint(0, 30)),
            }
        )
        
        if created:
            # 创建订单项
            for product in order_products:
                quantity = random.randint(1, 3)
                OrderItem.objects.create(
                    order=order,
                    product=product,
                    product_name=product.name,
                    product_price=product.price,
                    quantity=quantity,
                    subtotal=product.price * quantity
                )
            
            print(f"创建订单: {order.order_no}")
            created_orders.append(order)
    
    return created_orders

def main():
    print("开始创建测试数据...")
    
    # 创建分类
    print("\n1. 创建商品分类...")
    categories = create_categories()
    
    # 创建商品
    print("\n2. 创建商品...")
    products = create_products(categories)
    
    # 创建用户
    print("\n3. 创建测试用户...")
    users = create_test_users()
    
    # 创建订单
    print("\n4. 创建测试订单...")
    orders = create_test_orders(users, products)
    
    print(f"\n测试数据创建完成！")
    print(f"分类数量: {len(categories)}")
    print(f"商品数量: {len(products)}")
    print(f"用户数量: {len(users)}")
    print(f"订单数量: {len(orders)}")

if __name__ == '__main__':
    main()
