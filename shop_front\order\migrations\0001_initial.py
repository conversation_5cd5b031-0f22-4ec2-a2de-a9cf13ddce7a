# Generated by Django 4.2.22 on 2025-06-07 07:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('goods', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Cart',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='数量')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('selected', models.BooleanField(default=True, verbose_name='是否选中')),
            ],
            options={
                'verbose_name': '购物车',
                'verbose_name_plural': '购物车',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='优惠码')),
                ('type', models.CharField(choices=[('amount', '固定金额'), ('percent', '折扣百分比')], default='amount', max_length=10, verbose_name='优惠类型')),
                ('value', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='优惠值')),
                ('min_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='最低使用金额')),
                ('start_time', models.DateTimeField(verbose_name='生效时间')),
                ('end_time', models.DateTimeField(verbose_name='失效时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否有效')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '优惠券',
                'verbose_name_plural': '优惠券',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='Order',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(db_index=True, max_length=32, unique=True, verbose_name='订单号')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='订单总额')),
                ('pay_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='实付金额')),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='优惠金额')),
                ('status', models.CharField(choices=[('pending', '待支付'), ('paid', '已支付'), ('shipped', '已发货'), ('received', '已收货'), ('cancelled', '已取消'), ('refunded', '已退款')], db_index=True, default='pending', max_length=20, verbose_name='订单状态')),
                ('shipping_address', models.TextField(blank=True, null=True, verbose_name='配送地址')),
                ('contact_phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话')),
                ('receiver', models.CharField(blank=True, max_length=50, null=True, verbose_name='收货人')),
                ('receiver_mobile', models.CharField(blank=True, max_length=11, null=True, verbose_name='收货人手机')),
                ('remark', models.TextField(blank=True, null=True, verbose_name='订单备注')),
                ('created_time', models.DateTimeField(db_index=True, default=django.utils.timezone.now, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('paid_time', models.DateTimeField(blank=True, null=True, verbose_name='支付时间')),
                ('shipped_time', models.DateTimeField(blank=True, null=True, verbose_name='发货时间')),
                ('received_time', models.DateTimeField(blank=True, null=True, verbose_name='收货时间')),
                ('cancelled_time', models.DateTimeField(blank=True, null=True, verbose_name='取消时间')),
                ('refund_time', models.DateTimeField(blank=True, null=True, verbose_name='退款时间')),
                ('coupon', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='order.coupon', verbose_name='使用的优惠券')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '订单',
                'verbose_name_plural': '订单',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='OrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='价格')),
                ('quantity', models.PositiveIntegerField(default=1, verbose_name='数量')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='总金额')),
                ('is_reviewed', models.BooleanField(default=False, verbose_name='是否已评价')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='order.order', verbose_name='订单')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='goods.product', verbose_name='商品')),
            ],
            options={
                'verbose_name': '订单项',
                'verbose_name_plural': '订单项',
            },
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['code'], name='order_coupo_code_3ff4c9_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['is_active'], name='order_coupo_is_acti_ff38f5_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['start_time', 'end_time'], name='order_coupo_start_t_a59725_idx'),
        ),
        migrations.AddField(
            model_name='cart',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='goods.product', verbose_name='商品'),
        ),
        migrations.AddField(
            model_name='cart',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['order', 'product'], name='order_order_order_i_89d10a_idx'),
        ),
        migrations.AddIndex(
            model_name='orderitem',
            index=models.Index(fields=['is_reviewed'], name='order_order_is_revi_997c96_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['user', 'status'], name='order_order_user_id_f784ac_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['order_number'], name='order_order_order_n_fb1851_idx'),
        ),
        migrations.AddIndex(
            model_name='order',
            index=models.Index(fields=['created_time'], name='order_order_created_744765_idx'),
        ),
        migrations.AddIndex(
            model_name='cart',
            index=models.Index(fields=['user', 'product'], name='order_cart_user_id_fe7ce2_idx'),
        ),
        migrations.AddIndex(
            model_name='cart',
            index=models.Index(fields=['selected'], name='order_cart_selecte_fd6d89_idx'),
        ),
    ]
