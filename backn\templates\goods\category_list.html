{% extends 'base.html' %}

{% block title %}分类管理 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}分类管理{% endblock %}

{% block page_actions %}
<a href="{% url 'goods:category_add' %}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>添加分类
</a>
<div class="btn-group ms-2">
    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
        <i class="fas fa-filter me-1"></i>筛选
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="?status=active">启用分类</a></li>
        <li><a class="dropdown-item" href="?status=inactive">禁用分类</a></li>
        <li><a class="dropdown-item" href="?parent=top">顶级分类</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'goods:category_list' %}">全部分类</a></li>
    </ul>
</div>
{% endblock %}

{% block content %}
<!-- 搜索和筛选栏 -->
<div class="row mb-4">
    <div class="col-md-4">
        <form method="get" class="d-flex">
            <input type="text" class="form-control me-2" name="search"
                   placeholder="搜索分类名称" value="{{ search_query }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-3">
        <form method="get">
            <select name="sort" class="form-select" onchange="this.form.submit()">
                <option value="">排序方式</option>
                <option value="name" {% if sort_by == 'name' %}selected{% endif %}>按名称 A-Z</option>
                <option value="-name" {% if sort_by == '-name' %}selected{% endif %}>按名称 Z-A</option>
                <option value="-created_time" {% if sort_by == '-created_time' %}selected{% endif %}>最新创建</option>
                <option value="created_time" {% if sort_by == 'created_time' %}selected{% endif %}>最早创建</option>
                <option value="-products_count" {% if sort_by == '-products_count' %}selected{% endif %}>商品数量多</option>
                <option value="products_count" {% if sort_by == 'products_count' %}selected{% endif %}>商品数量少</option>
            </select>
        </form>
    </div>
    <div class="col-md-5 text-end">
        <span class="text-muted">共 {{ total_categories }} 个分类</span>
    </div>
</div>

<!-- 批量操作 -->
<div class="card mb-3">
    <div class="card-body py-2">
        <form id="batchForm">
            {% csrf_token %}
            <div class="row align-items-center">
                <div class="col-md-2">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">全选</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-success batch-action" data-action="activate">
                            <i class="fas fa-check me-1"></i>批量启用
                        </button>
                        <button type="button" class="btn btn-outline-warning batch-action" data-action="deactivate">
                            <i class="fas fa-ban me-1"></i>批量禁用
                        </button>
                        <button type="button" class="btn btn-outline-danger batch-action" data-action="delete">
                            <i class="fas fa-trash me-1"></i>批量删除
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllTable">
                        </th>
                        <th>分类名称</th>
                        <th>父分类</th>
                        <th>图标</th>
                        <th>商品数量</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for category in categories %}
                    <tr>
                        <td>
                            <input type="checkbox" class="category-checkbox" value="{{ category.id }}">
                        </td>
                        <td>
                            <strong>{{ category.name }}</strong>
                            {% if category.parent %}
                                <small class="text-muted ms-2">
                                    <i class="fas fa-level-up-alt"></i> 子分类
                                </small>
                            {% else %}
                                <small class="text-muted ms-2">
                                    <i class="fas fa-folder"></i> 主分类
                                </small>
                            {% endif %}
                        </td>
                        <td>
                            {% if category.parent %}
                                <span class="badge bg-light text-dark">{{ category.parent.name }}</span>
                            {% else %}
                                <span class="text-muted">顶级分类</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if category.icon %}
                                <img src="{{ category.icon.url }}" alt="{{ category.name }}"
                                     width="40" height="40" class="rounded" style="object-fit: cover;">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-folder text-muted"></i>
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <a href="{% url 'goods:list' %}?category={{ category.id }}"
                               class="text-decoration-none">
                                <span class="badge bg-info">{{ category.get_products_count }} 个商品</span>
                            </a>
                        </td>
                        <td>
                            {% if category.is_active %}
                                <span class="badge bg-success">启用</span>
                            {% else %}
                                <span class="badge bg-secondary">禁用</span>
                            {% endif %}
                        </td>
                        <td>{{ category.created_time|date:"Y-m-d H:i" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'goods:category_edit' category.id %}"
                                   class="btn btn-outline-warning" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{% url 'goods:list' %}?category={{ category.id %}"
                                   class="btn btn-outline-info" title="查看商品">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info toggle-status"
                                        data-category-id="{{ category.id }}"
                                        data-current-status="{{ category.is_active }}"
                                        title="{% if category.is_active %}禁用{% else %}启用{% endif %}">
                                    {% if category.is_active %}
                                        <i class="fas fa-ban"></i>
                                    {% else %}
                                        <i class="fas fa-check"></i>
                                    {% endif %}
                                </button>
                                <button type="button" class="btn btn-outline-danger delete-category"
                                        data-category-id="{{ category.id }}"
                                        data-category-name="{{ category.name }}"
                                        title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center text-muted py-4">
                            <i class="fas fa-folder fa-3x mb-3"></i>
                            <p>暂无分类数据</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        {% if page_obj.has_other_pages %}
        <div class="row mt-4">
            <div class="col-md-6">
                <p class="text-muted">
                    显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 条，
                    共 {{ page_obj.paginator.count }} 条记录
                </p>
            </div>
            <div class="col-md-6">
                <nav aria-label="分类列表分页">
                    <ul class="pagination justify-content-end">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                                    <i class="fas fa-angle-double-left"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                                    <i class="fas fa-angle-left"></i>
                                </a>
                            </li>
                        {% endif %}

                        <!-- 页码显示 -->
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                                    <i class="fas fa-angle-right"></i>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if parent_filter %}&parent={{ parent_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if sort_by %}&sort={{ sort_by }}{% endif %}">
                                    <i class="fas fa-angle-double-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
        {% else %}
        <div class="row mt-4">
            <div class="col-12">
                <p class="text-muted text-center">
                    共 {{ page_obj.paginator.count }} 条记录
                </p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<script>
$(document).ready(function() {
    // 全选功能
    $('#selectAll, #selectAllTable').change(function() {
        $('.category-checkbox').prop('checked', this.checked);
    });

    $('.category-checkbox').change(function() {
        const total = $('.category-checkbox').length;
        const checked = $('.category-checkbox:checked').length;
        $('#selectAll, #selectAllTable').prop('checked', total === checked);
    });

    // 批量操作
    $('.batch-action').click(function() {
        const action = $(this).data('action');
        const selectedIds = $('.category-checkbox:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length === 0) {
            alert('请选择要操作的分类');
            return;
        }

        let confirmMessage = '';
        if (action === 'delete') {
            confirmMessage = `确定要删除选中的 ${selectedIds.length} 个分类吗？此操作不可恢复！`;
        } else {
            confirmMessage = '确定要执行此批量操作吗？';
        }

        if (confirm(confirmMessage)) {
            $.post('{% url "goods:category_batch_action" %}', {
                'action': action,
                'category_ids': selectedIds,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '操作失败');
                }
            })
            .fail(function() {
                alert('操作失败，请重试');
            });
        }
    });

    // 切换分类状态
    $('.toggle-status').click(function() {
        const categoryId = $(this).data('category-id');
        const currentStatus = $(this).data('current-status');

        if (confirm(currentStatus ? '确定要禁用此分类吗？' : '确定要启用此分类吗？')) {
            $.post(`/goods/categories/toggle-status/${categoryId}/`, {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .fail(function() {
                alert('操作失败，请重试');
            });
        }
    });

    // 删除分类
    $('.delete-category').click(function() {
        const categoryId = $(this).data('category-id');
        const categoryName = $(this).data('category-name');

        if (confirm('确定要删除分类 "' + categoryName + '" 吗？此操作不可恢复！')) {
            $.post(`/goods/categories/delete/${categoryId}/`, {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('删除失败，请重试');
                }
            })
            .fail(function() {
                alert('删除失败，请重试');
            });
        }
    });
});
</script>

{% endblock %}
