"""
商品相关表单
"""
from django import forms
from django.core.exceptions import ValidationError
from .models import Product, ProductImage, Category
from .utils import validate_image_file, handle_uploaded_image
import os

class MultipleFileInput(forms.ClearableFileInput):
    """自定义多文件上传Widget"""
    allow_multiple_selected = True

    def __init__(self, attrs=None):
        if attrs is None:
            attrs = {}
        attrs['multiple'] = True
        super().__init__(attrs)

    def value_from_datadict(self, data, files, name):
        """从表单数据中获取文件列表"""
        upload = files.getlist(name)
        if not upload:
            return None
        return upload

class MultipleFileField(forms.FileField):
    """自定义多文件上传字段"""
    def __init__(self, *args, **kwargs):
        kwargs.setdefault("widget", MultipleFileInput())
        super().__init__(*args, **kwargs)

    def clean(self, data, initial=None):
        """清理和验证多文件数据"""
        if not data:
            if self.required:
                raise ValidationError(self.error_messages['required'])
            return []

        if not isinstance(data, (list, tuple)):
            data = [data]

        result = []
        for file in data:
            if file:
                # 使用父类的clean方法验证单个文件
                cleaned_file = super().clean(file, initial)
                if cleaned_file:
                    result.append(cleaned_file)

        return result

class ProductForm(forms.ModelForm):
    """商品表单"""
    
    # 添加一个额外的文件上传字段，支持任意路径
    external_image = forms.FileField(
        required=False,
        label='上传图片（支持任意路径）',
        help_text='可以选择电脑上任意位置的图片文件，系统会自动处理并保存',
        widget=forms.ClearableFileInput(attrs={
            'accept': 'image/*',
            'class': 'form-control'
        })
    )
    
    class Meta:
        model = Product
        fields = ['name', 'category', 'description', 'price', 'stock', 
                 'main_image', 'is_hot', 'is_new', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 4}),
            'price': forms.NumberInput(attrs={'class': 'form-control', 'step': '0.01'}),
            'stock': forms.NumberInput(attrs={'class': 'form-control'}),
            'main_image': forms.ClearableFileInput(attrs={
                'accept': 'image/*',
                'class': 'form-control'
            }),
            'category': forms.Select(attrs={'class': 'form-control'}),
        }
    
    def clean_external_image(self):
        """验证外部图片文件"""
        external_image = self.cleaned_data.get('external_image')
        
        if external_image:
            is_valid, error_message = validate_image_file(external_image)
            if not is_valid:
                raise ValidationError(error_message)
        
        return external_image
    
    def clean_main_image(self):
        """验证主图片文件"""
        main_image = self.cleaned_data.get('main_image')
        
        if main_image:
            is_valid, error_message = validate_image_file(main_image)
            if not is_valid:
                raise ValidationError(error_message)
        
        return main_image
    
    def save(self, commit=True):
        """保存表单，处理图片上传"""
        instance = super().save(commit=False)
        
        # 处理外部图片上传
        external_image = self.cleaned_data.get('external_image')
        if external_image:
            # 如果有外部图片，优先使用外部图片
            saved_path = handle_uploaded_image(external_image, 'products/')
            if saved_path:
                instance.main_image = saved_path
        
        if commit:
            instance.save()
        
        return instance

class ProductImageForm(forms.ModelForm):
    """商品图片表单"""
    
    # 添加外部图片上传字段
    external_image = forms.FileField(
        required=False,
        label='上传图片（支持任意路径）',
        help_text='可以选择电脑上任意位置的图片文件',
        widget=forms.ClearableFileInput(attrs={
            'accept': 'image/*',
            'class': 'form-control'
        })
    )
    
    class Meta:
        model = ProductImage
        fields = ['product', 'image', 'alt_text', 'sort_order']
        widgets = {
            'product': forms.Select(attrs={'class': 'form-control'}),
            'image': forms.ClearableFileInput(attrs={
                'accept': 'image/*',
                'class': 'form-control'
            }),
            'alt_text': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '图片描述（可选）'
            }),
            'sort_order': forms.NumberInput(attrs={'class': 'form-control'}),
        }
    
    def clean_external_image(self):
        """验证外部图片文件"""
        external_image = self.cleaned_data.get('external_image')
        
        if external_image:
            is_valid, error_message = validate_image_file(external_image)
            if not is_valid:
                raise ValidationError(error_message)
        
        return external_image
    
    def clean_image(self):
        """验证图片文件"""
        image = self.cleaned_data.get('image')
        
        if image:
            is_valid, error_message = validate_image_file(image)
            if not is_valid:
                raise ValidationError(error_message)
        
        return image
    
    def save(self, commit=True):
        """保存表单，处理图片上传"""
        instance = super().save(commit=False)

        # 确保 alt_text 有默认值
        if not instance.alt_text:
            instance.alt_text = instance.product.name if instance.product else ''

        # 处理外部图片上传
        external_image = self.cleaned_data.get('external_image')
        if external_image:
            saved_path = handle_uploaded_image(external_image, 'products/')
            if saved_path:
                instance.image = saved_path

        if commit:
            instance.save()

        return instance

class CategoryForm(forms.ModelForm):
    """分类表单"""
    
    # 添加外部图标上传字段
    external_icon = forms.FileField(
        required=False,
        label='上传图标（支持任意路径）',
        help_text='可以选择电脑上任意位置的图片文件作为分类图标',
        widget=forms.ClearableFileInput(attrs={
            'accept': 'image/*',
            'class': 'form-control'
        })
    )
    
    class Meta:
        model = Category
        fields = ['name', 'parent', 'description', 'icon', 'sort_order', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'icon': forms.ClearableFileInput(attrs={
                'accept': 'image/*',
                'class': 'form-control'
            }),
            'parent': forms.Select(attrs={'class': 'form-control'}),
            'sort_order': forms.NumberInput(attrs={'class': 'form-control'}),
        }
    
    def clean_external_icon(self):
        """验证外部图标文件"""
        external_icon = self.cleaned_data.get('external_icon')
        
        if external_icon:
            is_valid, error_message = validate_image_file(external_icon)
            if not is_valid:
                raise ValidationError(error_message)
        
        return external_icon
    
    def clean_icon(self):
        """验证图标文件"""
        icon = self.cleaned_data.get('icon')
        
        if icon:
            is_valid, error_message = validate_image_file(icon)
            if not is_valid:
                raise ValidationError(error_message)
        
        return icon
    
    def save(self, commit=True):
        """保存表单，处理图标上传"""
        instance = super().save(commit=False)
        
        # 处理外部图标上传
        external_icon = self.cleaned_data.get('external_icon')
        if external_icon:
            saved_path = handle_uploaded_image(external_icon, 'categories/')
            if saved_path:
                instance.icon = saved_path
        
        if commit:
            instance.save()
        
        return instance

class BulkImageUploadForm(forms.Form):
    """批量图片上传表单"""
    
    product = forms.ModelChoiceField(
        queryset=Product.objects.all(),
        label='选择商品',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    images = MultipleFileField(
        label='选择多个图片',
        help_text='可以同时选择多个图片文件进行批量上传',
        widget=MultipleFileInput(attrs={
            'accept': 'image/*',
            'class': 'form-control'
        })
    )
    
    def clean_images(self):
        """验证批量上传的图片"""
        images = self.cleaned_data.get('images')

        if not images:
            raise ValidationError('请选择至少一个图片文件')

        # 如果是单个文件，转换为列表
        if not isinstance(images, list):
            images = [images]

        # 验证每个图片文件
        for image in images:
            if image:  # 确保文件存在
                is_valid, error_message = validate_image_file(image)
                if not is_valid:
                    raise ValidationError(f'文件 {image.name}: {error_message}')

        return images
    
    def save(self):
        """保存批量上传的图片"""
        product = self.cleaned_data['product']
        images = self.files.getlist('images')
        
        created_images = []
        
        for i, image in enumerate(images):
            saved_path = handle_uploaded_image(image, 'products/')
            if saved_path:
                product_image = ProductImage.objects.create(
                    product=product,
                    image=saved_path,
                    alt_text=f'{product.name} - 图片{i+1}',
                    sort_order=i
                )
                created_images.append(product_image)
        
        return created_images
