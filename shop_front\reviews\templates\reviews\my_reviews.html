{% extends 'base.html' %}
{% load static %}

{% block title %}我的评价 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    .reviews-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background: #f8f9fa;
        min-height: 100vh;
    }

    .reviews-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 30px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(220,53,69,0.3);
    }

    .reviews-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .reviews-subtitle {
        font-size: 16px;
        opacity: 0.9;
    }

    .review-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        border-left: 4px solid #dc3545;
    }

    .review-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.15);
    }

    .review-product {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }

    .product-image {
        width: 80px;
        height: 80px;
        border-radius: 10px;
        object-fit: cover;
        margin-right: 15px;
        border: 2px solid #f8f9fa;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .product-image:hover {
        transform: scale(1.05);
        border-color: #dc3545;
        box-shadow: 0 4px 15px rgba(220,53,69,0.3);
    }

    .product-image-link {
        display: inline-block;
        position: relative;
        text-decoration: none;
    }

    .product-image-link::after {
        content: '\f35d';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(220,53,69,0.9);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .product-image-link:hover::after {
        opacity: 1;
    }

    .product-info h4 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .product-info h4 a {
        color: #333;
        text-decoration: none;
        transition: color 0.3s ease;
    }

    .product-info h4 a:hover {
        color: #dc3545;
        text-decoration: none;
    }

    .product-price {
        color: #dc3545;
        font-weight: 600;
        font-size: 16px;
    }

    .review-rating {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }

    .stars {
        display: flex;
        margin-right: 10px;
    }

    .star {
        color: #ffc107;
        font-size: 18px;
        margin-right: 2px;
    }

    .star.empty {
        color: #ddd;
    }

    .rating-text {
        color: #666;
        font-size: 14px;
    }

    .review-content {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 10px;
        margin-bottom: 15px;
        line-height: 1.6;
        color: #555;
    }

    .review-images {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }

    .review-image {
        width: 100px;
        height: 100px;
        border-radius: 8px;
        object-fit: cover;
        cursor: pointer;
        transition: transform 0.3s ease;
        border: 2px solid #f8f9fa;
    }

    .review-image:hover {
        transform: scale(1.05);
    }

    .review-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #666;
        font-size: 14px;
    }

    .review-date {
        display: flex;
        align-items: center;
    }

    .review-date i {
        margin-right: 5px;
        color: #dc3545;
    }

    .anonymous-badge {
        background: #6c757d;
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    }

    .empty-state i {
        font-size: 64px;
        color: #ddd;
        margin-bottom: 20px;
    }

    .empty-state h3 {
        color: #666;
        margin-bottom: 10px;
    }

    .empty-state p {
        color: #999;
        margin-bottom: 30px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        font-weight: 600;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220,53,69,0.3);
        color: white;
        text-decoration: none;
    }

    /* 分页样式 */
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 40px;
    }

    .pagination {
        display: flex;
        gap: 5px;
    }

    .pagination a,
    .pagination span {
        padding: 10px 15px;
        border-radius: 8px;
        text-decoration: none;
        color: #666;
        background: white;
        border: 1px solid #ddd;
        transition: all 0.3s ease;
    }

    .pagination a:hover {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    .pagination .current {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .reviews-container {
            padding: 15px;
        }

        .review-product {
            flex-direction: column;
            text-align: center;
        }

        .product-image {
            margin-right: 0;
            margin-bottom: 10px;
        }

        .review-images {
            justify-content: center;
        }

        .review-meta {
            flex-direction: column;
            gap: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="reviews-container">
    <div class="reviews-header">
        <div class="reviews-title">
            <i class="fas fa-star"></i> 我的评价
        </div>
        <div class="reviews-subtitle">
            查看您对商品的所有评价记录
        </div>
    </div>

    {% if reviews %}
        {% for review in reviews %}
        <div class="review-card">
            <div class="review-product">
                <a href="{% url 'goods:detail' pk=review.product.id %}" class="product-image-link" title="查看商品详情">
                    <img src="{% if review.product.main_image %}{{ review.product.main_image.url }}{% else %}{% static 'images/default-product.jpg' %}{% endif %}"
                         alt="{{ review.product.name }}" class="product-image">
                </a>
                <div class="product-info">
                    <h4>
                        <a href="{% url 'goods:detail' pk=review.product.id %}" style="color: #333; text-decoration: none;">
                            {{ review.product.name }}
                        </a>
                    </h4>
                    <div class="product-price">¥{{ review.product.price }}</div>
                </div>
            </div>

            <div class="review-rating">
                <div class="stars">
                    {% for i in "12345" %}
                        {% if forloop.counter <= review.score %}
                            <span class="star">★</span>
                        {% else %}
                            <span class="star empty">★</span>
                        {% endif %}
                    {% endfor %}
                </div>
                <span class="rating-text">{{ review.get_score_display }}</span>
                {% if review.is_anonymous %}
                    <span class="anonymous-badge">匿名评价</span>
                {% endif %}
            </div>

            <div class="review-content">
                {{ review.content }}
            </div>

            {% if review.images_list %}
            <div class="review-images">
                {% for image in review.images_list %}
                    <img src="{{ image.image.url }}" alt="评价图片" class="review-image" 
                         onclick="window.open('{{ image.image.url }}', '_blank')">
                {% endfor %}
            </div>
            {% endif %}

            <div class="review-meta">
                <div class="review-date">
                    <i class="fas fa-clock"></i>
                    {{ review.created_at|date:"Y-m-d H:i" }}
                </div>
                {% if review.is_verified %}
                    <span class="badge badge-success">
                        <i class="fas fa-check-circle"></i> 已验证购买
                    </span>
                {% endif %}
            </div>
        </div>
        {% endfor %}

        <!-- 分页 -->
        {% if reviews.has_other_pages %}
        <div class="pagination-container">
            <div class="pagination">
                {% if reviews.has_previous %}
                    <a href="?page=1">&laquo; 首页</a>
                    <a href="?page={{ reviews.previous_page_number }}">上一页</a>
                {% endif %}

                {% for num in reviews.paginator.page_range %}
                    {% if reviews.number == num %}
                        <span class="current">{{ num }}</span>
                    {% elif num > reviews.number|add:'-3' and num < reviews.number|add:'3' %}
                        <a href="?page={{ num }}">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if reviews.has_next %}
                    <a href="?page={{ reviews.next_page_number }}">下一页</a>
                    <a href="?page={{ reviews.paginator.num_pages }}">末页 &raquo;</a>
                {% endif %}
            </div>
        </div>
        {% endif %}

    {% else %}
        <div class="empty-state">
            <i class="fas fa-comment-slash"></i>
            <h3>暂无评价记录</h3>
            <p>您还没有对任何商品进行评价</p>
            <a href="{% url 'order:list' %}" class="btn-primary">
                <i class="fas fa-shopping-bag"></i> 查看我的订单
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
