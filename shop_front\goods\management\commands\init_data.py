from django.core.management.base import BaseCommand
from goods.models import Product, Category

class Command(BaseCommand):
    help = '初始化商品数据'

    def handle(self, *args, **options):
        self.stdout.write('开始初始化商品数据...')
        
        # 创建分类
        categories_data = [
            {'name': '手机数码', 'description': '手机、电脑、数码产品'},
            {'name': '时尚服装', 'description': '男装、女装、童装'},
            {'name': '家居生活', 'description': '家具、家电、生活用品'},
            {'name': '食品饮料', 'description': '零食、饮料、生鲜'},
            {'name': '运动户外', 'description': '运动装备、户外用品'},
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f"创建分类: {category.name}")
            else:
                self.stdout.write(f"分类已存在: {category.name}")

        # 获取分类
        try:
            digital_cat = Category.objects.get(name='手机数码')
            clothing_cat = Category.objects.get(name='时尚服装')
            home_cat = Category.objects.get(name='家居生活')
        except Category.DoesNotExist:
            self.stdout.write(self.style.ERROR('分类创建失败'))
            return

        # 创建商品
        products_data = [
            {
                'name': '轻薄笔记本电脑',
                'description': '高性能轻薄笔记本，适合办公和娱乐',
                'price': 3999.00,
                'category': digital_cat,
                'is_hot': True,
                'is_new': False,
                'stock': 50,
            },
            {
                'name': '无线降噪蓝牙耳机',
                'description': '主动降噪，高音质无线蓝牙耳机',
                'price': 199.00,
                'category': digital_cat,
                'is_hot': True,
                'is_new': True,
                'stock': 100,
            },
            {
                'name': '高清智能投影仪',
                'description': '4K高清智能投影仪，家庭影院首选',
                'price': 1299.00,
                'category': digital_cat,
                'is_hot': True,
                'is_new': False,
                'stock': 30,
            },
            {
                'name': '智能运动手环',
                'description': '多功能智能手环，健康监测专家',
                'price': 99.00,
                'category': digital_cat,
                'is_hot': True,
                'is_new': True,
                'stock': 200,
            },
            {
                'name': '薄款针织开衫',
                'description': '春秋季薄款针织开衫，舒适百搭',
                'price': 199.00,
                'category': clothing_cat,
                'is_hot': False,
                'is_new': True,
                'stock': 150,
            },
            {
                'name': '防晒冰丝外套',
                'description': '夏季防晒冰丝外套，清爽透气',
                'price': 159.00,
                'category': clothing_cat,
                'is_hot': False,
                'is_new': True,
                'stock': 120,
            },
            {
                'name': '高腰牛仔短裤',
                'description': '时尚高腰牛仔短裤，显瘦修身',
                'price': 129.00,
                'category': clothing_cat,
                'is_hot': True,
                'is_new': False,
                'stock': 180,
            },
            {
                'name': '智能空气净化器',
                'description': '高效净化空气，守护家人健康',
                'price': 899.00,
                'category': home_cat,
                'is_hot': True,
                'is_new': True,
                'stock': 60,
            },
        ]
        
        for product_data in products_data:
            product, created = Product.objects.get_or_create(
                name=product_data['name'],
                defaults=product_data
            )
            if created:
                self.stdout.write(f"创建商品: {product.name} - ¥{product.price}")
            else:
                self.stdout.write(f"商品已存在: {product.name}")

        # 显示统计信息
        total_categories = Category.objects.count()
        total_products = Product.objects.count()
        hot_products = Product.objects.filter(is_hot=True).count()
        new_products = Product.objects.filter(is_new=True).count()
        
        self.stdout.write(self.style.SUCCESS('\n数据初始化完成！'))
        self.stdout.write(f"总分类数: {total_categories}")
        self.stdout.write(f"总商品数: {total_products}")
        self.stdout.write(f"热门商品数: {hot_products}")
        self.stdout.write(f"新品数: {new_products}")
