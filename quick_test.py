#!/usr/bin/env python3
"""
快速测试脚本 - 检查具体问题
"""

import requests
from bs4 import BeautifulSoup

def test_users_and_products():
    base_url = "http://127.0.0.1:8001"
    session = requests.Session()
    
    print("🔍 检查用户和商品数据...")
    
    # 1. 检查商品列表页面
    print("\n1. 检查商品列表页面...")
    try:
        response = session.get(f"{base_url}/goods/products/")
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找商品链接
            product_links = soup.find_all('a', href=lambda x: x and '/goods/detail/' in x)
            print(f"   找到 {len(product_links)} 个商品详情链接")
            
            if product_links:
                for i, link in enumerate(product_links[:3]):  # 只显示前3个
                    print(f"   商品链接 {i+1}: {link.get('href')}")
            
            # 查找商品卡片
            product_cards = soup.find_all('div', class_='product-card')
            print(f"   找到 {len(product_cards)} 个商品卡片")
            
        else:
            print(f"   ❌ 商品列表页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 商品列表页面访问异常: {e}")
    
    # 2. 检查登录页面
    print("\n2. 检查登录页面...")
    try:
        response = session.get(f"{base_url}/users/login/")
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找CSRF令牌
            csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
            if csrf_input:
                print(f"   ✅ 找到CSRF令牌")
                
                # 尝试登录
                login_data = {
                    'username': 'RainbowWang',
                    'password': '123456',
                    'user_type': 'normal',
                    'csrfmiddlewaretoken': csrf_input.get('value')
                }
                
                login_response = session.post(f"{base_url}/users/login/", data=login_data)
                print(f"   登录响应状态码: {login_response.status_code}")
                
                if login_response.status_code == 302:
                    print(f"   ✅ 登录成功 (重定向)")
                elif login_response.status_code == 200:
                    if "错误" in login_response.text or "失败" in login_response.text:
                        print(f"   ❌ 登录失败 - 用户名或密码错误")
                    else:
                        print(f"   ⚠️  登录状态不明确")
                        
            else:
                print(f"   ❌ 未找到CSRF令牌")
        else:
            print(f"   ❌ 登录页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 登录页面访问异常: {e}")
    
    # 3. 检查是否有商品数据
    print("\n3. 检查商品数据...")
    try:
        response = session.get(f"{base_url}/goods/api/latest_products/")
        if response.status_code == 200:
            import json
            data = json.loads(response.text)
            if 'products' in data:
                print(f"   ✅ API返回 {len(data['products'])} 个商品")
                if data['products']:
                    product = data['products'][0]
                    print(f"   示例商品: ID={product.get('id')}, 名称={product.get('name')}")
            else:
                print(f"   ⚠️  API返回格式异常")
        else:
            print(f"   ❌ 商品API访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 商品API访问异常: {e}")

if __name__ == "__main__":
    test_users_and_products()
