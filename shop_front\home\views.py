from django.views.generic import TemplateView
from django.apps import apps
import logging
from django.shortcuts import render

# 设置日志记录器
logger = logging.getLogger(__name__)

logger = logging.getLogger(__name__)

def hot_products(request):
    """热门商品页面"""
    from goods.models import Product

    # 获取热门商品（按销量排序）
    hot_products = Product.objects.filter(
        is_active=True
    ).order_by('-sales')[:20]

    return render(request, 'home/hot_products.html', {
        'hot_products': hot_products,
        'page_title': '热门商品'
    })



class HomeView(TemplateView):
    template_name = 'home/index.html'  # 使用MARS BUY红色主题首页

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        try:
            # 使用延迟导入避免循环导入问题
            Category = apps.get_model('goods', 'Category')
            Product = apps.get_model('goods', 'Product')
            Banner = apps.get_model('home', 'Banner')

            # 获取所有一级分类
            categories = Category.objects.filter(parent=None, is_active=True).prefetch_related(
                'children',
                'children__children'
            )
            context['categories'] = categories

            # 获取轮播图
            try:
                context['banners'] = Banner.objects.filter(is_active=True).order_by('sort_order')[:5]
            except:
                context['banners'] = []

            
            # 获取推荐商品 - 显示库存最多的商品
            try:
                recommend_products = Product.objects.filter(
                    is_active=True
                ).select_related('category').order_by('-stock')[:5]
                context['hot_products'] = recommend_products  # 为你推荐区域使用
            except:
                context['hot_products'] = []

            # 获取真正的热门商品 - 按销量排序
            try:
                real_hot_products = Product.objects.filter(
                    is_hot=True,
                    is_active=True
                ).select_related('category').order_by('-sales')[:6]

                # 如果热门商品不足6个，用销量高的商品补充
                if real_hot_products.count() < 6:
                    additional_hot = Product.objects.filter(
                        is_active=True
                    ).exclude(
                        id__in=[p.id for p in real_hot_products]
                    ).select_related('category').order_by('-sales')[:6-real_hot_products.count()]
                    context['real_hot_products'] = list(real_hot_products) + list(additional_hot)
                else:
                    context['real_hot_products'] = real_hot_products
            except:
                context['real_hot_products'] = []

            # 获取新品
            try:
                new_products = Product.objects.filter(
                    is_new=True,
                    is_active=True
                ).select_related('category').order_by('-id')[:8]

                # 如果新品不足8个，用最新商品补充
                if new_products.count() < 8:
                    additional_new = Product.objects.filter(
                        is_active=True
                    ).exclude(
                        id__in=[p.id for p in new_products]
                    ).select_related('category').order_by('-id')[:8-new_products.count()]
                    context['new_products'] = list(new_products) + list(additional_new)
                else:
                    context['new_products'] = new_products
            except:
                context['new_products'] = []

            # 获取秒杀商品（选择价格最高的商品）
            try:
                seckill_products = Product.objects.filter(
                    is_active=True
                ).select_related('category').order_by('-price')[:4]
                context['seckill_products'] = seckill_products
            except:
                context['seckill_products'] = []



        except Exception as e:
            logger.error(f"首页数据加载错误: {str(e)}")
            context['categories'] = []
            context['banners'] = []
            context['hot_products'] = []
            context['new_products'] = []
            context['seckill_products'] = []

        return context

# 使用HomeView类作为主要的首页视图