#!/usr/bin/env python3
"""
测试管理员注册功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop/backn')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backn.settings')
django.setup()

from users.models import User
from admin_panel.forms import AdminRegisterForm

def test_admin_register_form():
    """测试管理员注册表单"""
    print("🧪 测试管理员注册表单...")
    print("=" * 60)
    
    # 测试数据
    test_data = {
        'username': 'testadmin123',
        'email': '<EMAIL>',
        'password1': 'TestPassword123!',
        'password2': 'TestPassword123!',
        'phone': '13800138001',
        'first_name': '测试管理员'
    }
    
    print(f"📝 测试数据:")
    for key, value in test_data.items():
        if 'password' in key:
            print(f"   {key}: {'*' * len(value)}")
        else:
            print(f"   {key}: {value}")
    
    # 创建表单实例
    form = AdminRegisterForm(data=test_data)
    
    print(f"\n🔍 表单验证结果:")
    if form.is_valid():
        print("   ✅ 表单验证通过")
        
        # 尝试保存用户
        try:
            user = form.save()
            print(f"   ✅ 用户创建成功")
            print(f"   用户名: {user.username}")
            print(f"   邮箱: {user.email}")
            print(f"   手机: {user.phone}")
            print(f"   是否管理员: {'是' if user.is_staff else '否'}")
            print(f"   是否激活: {'是' if user.is_active else '否'}")
            print(f"   是否超级管理员: {'是' if user.is_superuser else '否'}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 用户创建失败: {e}")
            return False
    else:
        print("   ❌ 表单验证失败")
        for field, errors in form.errors.items():
            print(f"   {field}: {', '.join(errors)}")
        return False

def test_existing_admin_accounts():
    """测试现有管理员账户"""
    print("\n🔍 检查现有管理员账户...")
    print("=" * 60)
    
    # 查找所有管理员
    admins = User.objects.filter(is_staff=True)
    print(f"📊 管理员账户数量: {admins.count()}")
    
    if admins.exists():
        for admin in admins:
            print(f"\n👤 管理员: {admin.username}")
            print(f"   邮箱: {admin.email}")
            print(f"   手机: {admin.phone or '未设置'}")
            print(f"   是否激活: {'是' if admin.is_active else '否'}")
            print(f"   是否超级管理员: {'是' if admin.is_superuser else '否'}")
            print(f"   创建时间: {admin.date_joined.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"   最后登录: {admin.last_login.strftime('%Y-%m-%d %H:%M:%S') if admin.last_login else '从未登录'}")
    else:
        print("❌ 没有找到管理员账户")

def test_user_creation_directly():
    """直接测试用户创建"""
    print("\n🔧 直接测试用户创建...")
    print("=" * 60)
    
    try:
        # 检查用户名是否已存在
        username = 'directtest123'
        if User.objects.filter(username=username).exists():
            print(f"⚠️ 用户 {username} 已存在，先删除...")
            User.objects.filter(username=username).delete()
        
        # 直接创建用户
        user = User.objects.create_user(
            username=username,
            email='<EMAIL>',
            password='TestPassword123!',
            phone='13800138002',
            first_name='直接测试',
            is_staff=True,
            is_active=True
        )
        
        print(f"✅ 直接创建用户成功")
        print(f"   用户名: {user.username}")
        print(f"   邮箱: {user.email}")
        print(f"   是否管理员: {'是' if user.is_staff else '否'}")
        print(f"   是否激活: {'是' if user.is_active else '否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 直接创建用户失败: {e}")
        return False

def cleanup_test_users():
    """清理测试用户"""
    print("\n🧹 清理测试用户...")
    print("=" * 60)
    
    test_usernames = ['testadmin123', 'directtest123']
    
    for username in test_usernames:
        try:
            user = User.objects.get(username=username)
            user.delete()
            print(f"✅ 删除测试用户: {username}")
        except User.DoesNotExist:
            print(f"ℹ️ 测试用户不存在: {username}")
        except Exception as e:
            print(f"❌ 删除用户失败 {username}: {e}")

def show_registration_info():
    """显示注册信息"""
    print("\n🌐 管理员注册信息:")
    print("=" * 60)
    print("注册页面: http://127.0.0.1:8003/register/")
    print("登录页面: http://127.0.0.1:8003/login/")
    print("后台首页: http://127.0.0.1:8003/dashboard/")
    print("\n📝 注册要求:")
    print("- 用户名: 3-20个字符，只能包含字母、数字和下划线")
    print("- 密码: 至少8个字符，建议包含大小写字母、数字和特殊字符")
    print("- 邮箱: 有效的邮箱地址")
    print("- 手机: 可选，11位手机号码")
    print("- 头像: 可选，支持JPG、PNG、GIF格式，最大5MB")

if __name__ == "__main__":
    try:
        print("🚀 开始测试管理员注册功能...")
        print("=" * 80)
        
        # 测试现有账户
        test_existing_admin_accounts()
        
        # 测试直接创建用户
        direct_success = test_user_creation_directly()
        
        # 测试表单注册
        form_success = test_admin_register_form()
        
        # 显示注册信息
        show_registration_info()
        
        print("\n🎯 测试结果总结:")
        print("=" * 60)
        print(f"直接创建用户: {'✅ 成功' if direct_success else '❌ 失败'}")
        print(f"表单注册用户: {'✅ 成功' if form_success else '❌ 失败'}")
        
        if direct_success and form_success:
            print("\n🎉 管理员注册功能正常！")
            print("您可以访问 http://127.0.0.1:8003/register/ 进行注册")
        else:
            print("\n⚠️ 管理员注册功能存在问题，请检查错误信息")
        
        # 询问是否清理测试数据
        response = input("\n❓ 是否清理测试用户数据? (y/n): ")
        if response.lower() == 'y':
            cleanup_test_users()
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
