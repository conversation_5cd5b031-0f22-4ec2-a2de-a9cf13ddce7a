<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>商品分类 - 购物商城</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#3B82F6',
            secondary: '#10B981',
            accent: '#F59E0B',
            dark: '#1E293B',
            light: '#F8FAFC'
          },
          fontFamily: {
            sans: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .category-card {
        @apply bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100;
      }
      .category-title {
        @apply text-lg font-semibold text-dark flex items-center justify-between cursor-pointer p-4;
      }
      .subcategory-item {
        @apply py-2 px-4 hover:bg-gray-50 rounded-lg transition-colors duration-200 cursor-pointer;
      }
      .filter-btn {
        @apply px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border;
      }
      .filter-btn-active {
        @apply bg-primary/10 text-primary border-primary;
      }
      .sort-btn {
        @apply px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:bg-gray-100;
      }
      .sort-btn-active {
        @apply bg-primary text-white;
      }
    }
  </style>
</head>
<body class="bg-gray-50 text-dark">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="container mx-auto px-4 py-3 flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <button class="lg:hidden text-dark p-2">
          <i class="fa fa-bars text-xl"></i>
        </button>
        <a href="#" class="text-2xl font-bold text-primary">ShopEase</a>
      </div>
      
      <div class="hidden md:flex items-center flex-1 max-w-xl mx-8">
        <div class="relative w-full">
          <input type="text" placeholder="搜索商品..." 
                 class="w-full pl-10 pr-4 py-2 rounded-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-primary/30 focus:border-primary transition-all">
          <i class="fa fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
        </div>
      </div>
      
      <div class="flex items-center space-x-4">
        <a href="#" class="hidden md:block text-sm text-gray-600 hover:text-primary transition-colors">
          <i class="fa fa-user-circle-o mr-1"></i> 我的账户
        </a>
        <a href="#" class="relative text-dark">
          <i class="fa fa-shopping-cart text-xl"></i>
          <span class="absolute -top-2 -right-2 bg-accent text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
        </a>
      </div>
    </div>
  </header>

  <main class="container mx-auto px-4 py-6">
    <!-- 页面标题 -->
    <div class="mb-6">
      <h1 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-dark">商品分类</h1>
      <p class="text-gray-500 mt-1">浏览我们丰富的商品类别，找到您需要的一切</p>
    </div>

    <!-- 筛选与排序区域 -->
    <div class="bg-white rounded-xl shadow-sm p-4 mb-6">
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div class="flex flex-wrap gap-2">
          <button id="select-all" class="filter-btn filter-btn-active">
            <i class="fa fa-check-square-o mr-1"></i> 全选
          </button>
          <button class="filter-btn border-gray-200 hover:border-primary hover:text-primary">
            <i class="fa fa-fire mr-1"></i> 热门
          </button>
          <button class="filter-btn border-gray-200 hover:border-primary hover:text-primary">
            <i class="fa fa-tag mr-1"></i> 促销
          </button>
          <button class="filter-btn border-gray-200 hover:border-primary hover:text-primary">
            <i class="fa fa-star mr-1"></i> 推荐
          </button>
        </div>
        
        <div class="flex items-center gap-2">
          <span class="text-sm text-gray-500">排序方式:</span>
          <div class="flex rounded-lg overflow-hidden border border-gray-200">
            <button class="sort-btn sort-btn-active">A-Z</button>
            <button class="sort-btn">Z-A</button>
            <button class="sort-btn">热门</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 商品分类区域 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 时尚穿搭 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('fashion')">
          <div class="flex items-center">
            <i class="fa fa-tshirt text-primary mr-3"></i>
            <span>时尚穿搭</span>
          </div>
          <i id="fashion-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="fashion" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-male text-gray-400 mr-2"></i> 男装
          </div>
          <div class="subcategory-item">
            <i class="fa fa-female text-gray-400 mr-2"></i> 女装
          </div>
          <div class="subcategory-item">
            <i class="fa fa-child text-gray-400 mr-2"></i> 童装
          </div>
          <div class="subcategory-item">
            <i class="fa fa-shoe-prints text-gray-400 mr-2"></i> 鞋类
          </div>
          <div class="subcategory-item">
            <i class="fa fa-briefcase text-gray-400 mr-2"></i> 箱包
          </div>
          <div class="subcategory-item">
            <i class="fa fa-gem text-gray-400 mr-2"></i> 服饰配件
          </div>
        </div>
      </div>

      <!-- 美妆护肤 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('beauty')">
          <div class="flex items-center">
            <i class="fa fa-paint-brush text-pink-500 mr-3"></i>
            <span>美妆护肤</span>
          </div>
          <i id="beauty-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="beauty" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-user text-gray-400 mr-2"></i> 面部护理
          </div>
          <div class="subcategory-item">
            <i class="fa fa-palette text-gray-400 mr-2"></i> 彩妆
          </div>
          <div class="subcategory-item">
            <i class="fa fa-bath text-gray-400 mr-2"></i> 身体护理
          </div>
          <div class="subcategory-item">
            <i class="fa fa-scissors text-gray-400 mr-2"></i> 美发产品
          </div>
          <div class="subcategory-item">
            <i class="fa fa-percent text-gray-400 mr-2"></i> 香水香氛
          </div>
        </div>
      </div>

      <!-- 家居日用 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('home')">
          <div class="flex items-center">
            <i class="fa fa-home text-amber-600 mr-3"></i>
            <span>家居日用</span>
          </div>
          <i id="home-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="home" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-bed text-gray-400 mr-2"></i> 家纺床品
          </div>
          <div class="subcategory-item">
            <i class="fa fa-coffee text-gray-400 mr-2"></i> 厨房用品
          </div>
          <div class="subcategory-item">
            <i class="fa fa-shower text-gray-400 mr-2"></i> 卫浴清洁
          </div>
          <div class="subcategory-item">
            <i class="fa fa-picture-o text-gray-400 mr-2"></i> 家居装饰
          </div>
          <div class="subcategory-item">
            <i class="fa fa-archive text-gray-400 mr-2"></i> 收纳用品
          </div>
        </div>
      </div>

      <!-- 数码科技 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('tech')">
          <div class="flex items-center">
            <i class="fa fa-laptop text-blue-700 mr-3"></i>
            <span>数码科技</span>
          </div>
          <i id="tech-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="tech" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-mobile text-gray-400 mr-2"></i> 手机及配件
          </div>
          <div class="subcategory-item">
            <i class="fa fa-desktop text-gray-400 mr-2"></i> 电脑办公设备
          </div>
          <div class="subcategory-item">
            <i class="fa fa-clock-o text-gray-400 mr-2"></i> 智能穿戴
          </div>
          <div class="subcategory-item">
            <i class="fa fa-camera text-gray-400 mr-2"></i> 摄影摄像器材
          </div>
        </div>
      </div>

      <!-- 家用电器 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('appliances')">
          <div class="flex items-center">
            <i class="fa fa-plug text-teal-600 mr-3"></i>
            <span>家用电器</span>
          </div>
          <i id="appliances-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="appliances" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-snowflake-o text-gray-400 mr-2"></i> 大家电(冰箱、空调等)
          </div>
          <div class="subcategory-item">
            <i class="fa fa-coffee text-gray-400 mr-2"></i> 厨房小家电
          </div>
          <div class="subcategory-item">
            <i class="fa fa-television text-gray-400 mr-2"></i> 生活小家电
          </div>
        </div>
      </div>

      <!-- 食品生鲜 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('food')">
          <div class="flex items-center">
            <i class="fa fa-cutlery text-red-500 mr-3"></i>
            <span>食品生鲜</span>
          </div>
          <i id="food-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="food" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-cookie-bite text-gray-400 mr-2"></i> 休闲零食
          </div>
          <div class="subcategory-item">
            <i class="fa fa-shopping-basket text-gray-400 mr-2"></i> 粮油副食
          </div>
          <div class="subcategory-item">
            <i class="fa fa-leaf text-gray-400 mr-2"></i> 新鲜果蔬
          </div>
          <div class="subcategory-item">
            <i class="fa fa-drumstick-bite text-gray-400 mr-2"></i> 肉禽蛋奶
          </div>
          <div class="subcategory-item">
            <i class="fa fa-glass-martini text-gray-400 mr-2"></i> 酒水饮料
          </div>
        </div>
      </div>

      <!-- 母婴亲子 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('baby')">
          <div class="flex items-center">
            <i class="fa fa-baby text-purple-500 mr-3"></i>
            <span>母婴亲子</span>
          </div>
          <i id="baby-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="baby" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-birthday-cake text-gray-400 mr-2"></i> 母婴用品
          </div>
          <div class="subcategory-item">
            <i class="fa fa-toys text-gray-400 mr-2"></i> 儿童玩具
          </div>
          <div class="subcategory-item">
            <i class="fa fa-child text-gray-400 mr-2"></i> 童装童鞋
          </div>
          <div class="subcategory-item">
            <i class="fa fa-book text-gray-400 mr-2"></i> 早教学习产品
          </div>
        </div>
      </div>

      <!-- 运动户外 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('sports')">
          <div class="flex items-center">
            <i class="fa fa-futbol-o text-green-600 mr-3"></i>
            <span>运动户外</span>
          </div>
          <i id="sports-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="sports" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-running text-gray-400 mr-2"></i> 运动服饰
          </div>
          <div class="subcategory-item">
            <i class="fa fa-dumbbell text-gray-400 mr-2"></i> 健身器材
          </div>
          <div class="subcategory-item">
            <i class="fa fa-campground text-gray-400 mr-2"></i> 户外装备
          </div>
          <div class="subcategory-item">
            <i class="fa fa-basketball-ball text-gray-400 mr-2"></i> 球类运动用品
          </div>
        </div>
      </div>

      <!-- 汽车相关 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('auto')">
          <div class="flex items-center">
            <i class="fa fa-car text-indigo-600 mr-3"></i>
            <span>汽车相关</span>
          </div>
          <i id="auto-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="auto" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-car text-gray-400 mr-2"></i> 汽车装饰
          </div>
          <div class="subcategory-item">
            <i class="fa fa-wrench text-gray-400 mr-2"></i> 汽车保养
          </div>
          <div class="subcategory-item">
            <i class="fa fa-radio text-gray-400 mr-2"></i> 汽车电子
          </div>
          <div class="subcategory-item">
            <i class="fa fa-gas-pump text-gray-400 mr-2"></i> 车载用品
          </div>
        </div>
      </div>

      <!-- 生活服务 -->
      <div class="category-card">
        <div class="category-title" onclick="toggleCategory('services')">
          <div class="flex items-center">
            <i class="fa fa-handshake-o text-amber-700 mr-3"></i>
            <span>生活服务</span>
          </div>
          <i id="services-icon" class="fa fa-chevron-down text-gray-400 transition-transform duration-300"></i>
        </div>
        <div id="services" class="p-2">
          <div class="subcategory-item">
            <i class="fa fa-credit-card text-gray-400 mr-2"></i> 虚拟商品(充值、会员等)
          </div>
          <div class="subcategory-item">
            <i class="fa fa-home text-gray-400 mr-2"></i> 家政服务
          </div>
          <div class="subcategory-item">
            <i class="fa fa-map-marker text-gray-400 mr-2"></i> 本地生活团购
          </div>
        </div>
      </div>
    </div>
  </main>

  <footer class="bg-dark text-white mt-12">
    <div class="container mx-auto px-4 py-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <div>
          <h3 class="text-xl font-bold mb-4">ShopEase</h3>
          <p class="text-gray-400 text-sm">为您提供便捷的购物体验，丰富的商品种类，优惠的价格和贴心的服务。</p>
        </div>
        <div>
          <h4 class="text-lg font-semibold mb-4">快速链接</h4>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">首页</a></li>
            <li><a href="#" class="hover:text-white transition-colors">商品分类</a></li>
            <li><a href="#" class="hover:text-white transition-colors">热门促销</a></li>
            <li><a href="#" class="hover:text-white transition-colors">关于我们</a></li>
          </ul>
        </div>
        <div>
          <h4 class="text-lg font-semibold mb-4">客户服务</h4>
          <ul class="space-y-2 text-gray-400">
            <li><a href="#" class="hover:text-white transition-colors">联系我们</a></li>
            <li><a href="#" class="hover:text-white transition-colors">常见问题</a></li>
            <li><a href="#" class="hover:text-white transition-colors">配送信息</a></li>
            <li><a href="#" class="hover:text-white transition-colors">退换货政策</a></li>
          </ul>
        </div>
        <div>
          <h4 class="text-lg font-semibold mb-4">订阅我们</h4>
          <p class="text-gray-400 text-sm mb-4">获取最新的产品信息和促销活动</p>
          <div class="flex">
            <input type="email" placeholder="您的邮箱地址" class="px-4 py-2 rounded-l-lg focus:outline-none text-dark flex-1">
            <button class="bg-primary hover:bg-primary/90 px-4 py-2 rounded-r-lg transition-colors">
              <i class="fa fa-paper-plane"></i>
            </button>
          </div>
        </div>
      </div>
      <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400 text-sm">
        <p>&copy; 2025 ShopEase 购物商城. 保留所有权利.</p>
      </div>
    </div>
  </footer>

  <script>
    // 分类折叠/展开功能
    function toggleCategory(id) {
      const category = document.getElementById(id);
      const icon = document.getElementById(`${id}-icon`);
      
      if (category.style.display === 'none') {
        category.style.display = 'block';
        icon.classList.add('rotate-180');
      } else {
        category.style.display = 'none';
        icon.classList.remove('rotate-180');
      }
    }

    // 全选按钮功能
    document.getElementById('select-all').addEventListener('click', function() {
      const isSelected = this.classList.contains('filter-btn-active');
      
      if (isSelected) {
        // 取消全选
        this.classList.remove('filter-btn-active');
        this.innerHTML = '<i class="fa fa-square-o mr-1"></i> 全选';
        
        // 隐藏所有分类
        document.querySelectorAll('[id$="-icon"]').forEach(icon => {
          icon.classList.remove('rotate-180');
        });
        document.querySelectorAll('[id^="fashion"], [id^="beauty"], [id^="home"], [id^="tech"], [id^="appliances"], [id^="food"], [id^="baby"], [id^="sports"], [id^="auto"], [id^="services"]').forEach(div => {
          div.style.display = 'none';
        });
      } else {
        // 全选
        this.classList.add('filter-btn-active');
        this.innerHTML = '<i class="fa fa-check-square-o mr-1"></i> 全选';
        
        // 显示所有分类
        document.querySelectorAll('[id$="-icon"]').forEach(icon => {
          icon.classList.add('rotate-180');
        });
        document.querySelectorAll('[id^="fashion"], [id^="beauty"], [id^="home"], [id^="tech"], [id^="appliances"], [id^="food"], [id^="baby"], [id^="sports"], [id^="auto"], [id^="services"]').forEach(div => {
          div.style.display = 'block';
        });
      }
    });

    // 默认展开所有分类
    document.addEventListener('DOMContentLoaded', function() {
      document.querySelectorAll('[id$="-icon"]').forEach(icon => {
        icon.classList.add('rotate-180');
      });
    });
  </script>
</body>
</html>