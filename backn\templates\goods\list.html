{% extends 'base.html' %}

{% block title %}商品管理 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}商品管理{% endblock %}

{% block page_actions %}
<a href="{% url 'goods:add' %}" class="btn btn-primary">
    <i class="fas fa-plus me-1"></i>添加商品
</a>
<div class="btn-group ms-2">
    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
        <i class="fas fa-filter me-1"></i>筛选
    </button>
    <ul class="dropdown-menu">
        <li><a class="dropdown-item" href="?status=active">已上架</a></li>
        <li><a class="dropdown-item" href="?status=inactive">已下架</a></li>
        <li><a class="dropdown-item" href="?status=hot">热门商品</a></li>
        <li><a class="dropdown-item" href="?status=new">新品</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'goods:list' %}">全部商品</a></li>
    </ul>
</div>
{% endblock %}

{% block content %}
<!-- 搜索和筛选栏 -->
<div class="row mb-4">
    <div class="col-md-4">
        <form method="get" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="搜索商品名称" value="{{ search_query }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-3">
        <form method="get">
            <select name="category" class="form-select" onchange="this.form.submit()">
                <option value="">全部分类</option>
                {% for category in categories %}
                <option value="{{ category.id }}" {% if category_filter == category.id|stringformat:"s" %}selected{% endif %}>
                    {{ category.name }}
                </option>
                {% endfor %}
            </select>
        </form>
    </div>
    <div class="col-md-5 text-end">
        <span class="text-muted">共 {{ page_obj.paginator.count }} 个商品</span>
    </div>
</div>

<!-- 批量操作 -->
<div class="card mb-3">
    <div class="card-body py-2">
        <form id="batchForm">
            {% csrf_token %}
            <div class="row align-items-center">
                <div class="col-md-2">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll">全选</label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-success batch-action" data-action="activate">
                            <i class="fas fa-check me-1"></i>批量上架
                        </button>
                        <button type="button" class="btn btn-outline-warning batch-action" data-action="deactivate">
                            <i class="fas fa-ban me-1"></i>批量下架
                        </button>
                        <button type="button" class="btn btn-outline-info batch-action" data-action="set_hot">
                            <i class="fas fa-fire me-1"></i>设为热门
                        </button>
                        <button type="button" class="btn btn-outline-secondary batch-action" data-action="unset_hot">
                            <i class="fas fa-snowflake me-1"></i>取消热门
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 商品列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th width="50">
                            <input type="checkbox" id="selectAllTable">
                        </th>
                        <th>商品图片</th>
                        <th>商品名称</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>销量</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for product in page_obj %}
                    <tr>
                        <td>
                            <input type="checkbox" class="product-checkbox" value="{{ product.id }}">
                        </td>
                        <td>
                            {% if product.main_image %}
                                <img src="{{ product.main_image.url }}" alt="{{ product.name }}" 
                                     class="rounded" width="60" height="60" style="object-fit: cover;">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ product.name|truncatechars:30 }}</strong>
                            <div class="small text-muted">
                                {% if product.is_hot %}
                                    <span class="badge bg-danger me-1">热门</span>
                                {% endif %}
                                {% if product.is_new %}
                                    <span class="badge bg-success me-1">新品</span>
                                {% endif %}
                            </div>
                        </td>
                        <td>{{ product.category.name }}</td>
                        <td class="text-success fw-bold">¥{{ product.price }}</td>
                        <td>
                            {% if product.stock > 0 %}
                                <span class="text-success">{{ product.stock }}</span>
                            {% else %}
                                <span class="text-danger">缺货</span>
                            {% endif %}
                        </td>
                        <td>{{ product.sales }}</td>
                        <td>
                            {% if product.is_active %}
                                <span class="badge bg-success">已上架</span>
                            {% else %}
                                <span class="badge bg-secondary">已下架</span>
                            {% endif %}
                        </td>
                        <td>{{ product.created_time|date:"Y-m-d H:i" }}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'goods:detail' product.id %}" 
                                   class="btn btn-outline-primary" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'goods:edit' product.id %}" 
                                   class="btn btn-outline-warning" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-info toggle-status" 
                                        data-product-id="{{ product.id }}" 
                                        data-current-status="{{ product.is_active }}"
                                        title="{% if product.is_active %}下架{% else %}上架{% endif %}">
                                    {% if product.is_active %}
                                        <i class="fas fa-ban"></i>
                                    {% else %}
                                        <i class="fas fa-check"></i>
                                    {% endif %}
                                </button>
                                <button type="button" class="btn btn-outline-danger delete-product" 
                                        data-product-id="{{ product.id }}" 
                                        data-product-name="{{ product.name }}"
                                        title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="10" class="text-center text-muted py-4">
                            <i class="fas fa-box fa-3x mb-3"></i>
                            <p>暂无商品数据</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
{% if page_obj.has_other_pages %}
<nav aria-label="商品列表分页" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">首页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">上一页</a>
            </li>
        {% endif %}
        
        <li class="page-item active">
            <span class="page-link">{{ page_obj.number }} / {{ page_obj.paginator.num_pages }}</span>
        </li>
        
        {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">下一页</a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if category_filter %}&category={{ category_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">末页</a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 全选功能
    $('#selectAll, #selectAllTable').change(function() {
        $('.product-checkbox').prop('checked', this.checked);
    });
    
    $('.product-checkbox').change(function() {
        const total = $('.product-checkbox').length;
        const checked = $('.product-checkbox:checked').length;
        $('#selectAll, #selectAllTable').prop('checked', total === checked);
    });
    
    // 批量操作
    $('.batch-action').click(function() {
        const action = $(this).data('action');
        const selectedIds = $('.product-checkbox:checked').map(function() {
            return this.value;
        }).get();
        
        if (selectedIds.length === 0) {
            alert('请选择要操作的商品');
            return;
        }
        
        if (confirm('确定要执行此批量操作吗？')) {
            $.post('{% url "goods:batch_action" %}', {
                'action': action,
                'product_ids': selectedIds,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    alert(data.message);
                    location.reload();
                } else {
                    alert(data.message || '操作失败');
                }
            })
            .fail(function() {
                alert('操作失败，请重试');
            });
        }
    });
    
    // 切换商品状态
    $('.toggle-status').click(function() {
        const productId = $(this).data('product-id');
        const currentStatus = $(this).data('current-status');
        
        if (confirm(currentStatus ? '确定要下架此商品吗？' : '确定要上架此商品吗？')) {
            $.post('{% url "goods:toggle_status" 0 %}'.replace('0', productId), {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .fail(function() {
                alert('操作失败，请重试');
            });
        }
    });
    
    // 删除商品
    $('.delete-product').click(function() {
        const productId = $(this).data('product-id');
        const productName = $(this).data('product-name');
        
        if (confirm('确定要删除商品 "' + productName + '" 吗？此操作不可恢复！')) {
            $.post('{% url "goods:delete" 0 %}'.replace('0', productId), {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('删除失败，请重试');
                }
            })
            .fail(function() {
                alert('删除失败，请重试');
            });
        }
    });
});
</script>
{% endblock %}
