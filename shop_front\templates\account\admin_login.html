{% extends 'base.html' %}

{% block title %}管理员登录 - 在线商城{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-box">
        <div class="login-header">
            <h2>管理员登录</h2>
            <a href="/account/login/" class="user-login-link">
                <i class="fas fa-user"></i> 用户登录
            </a>
        </div>
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}
        <form method="post" action="/account/admin-login/" class="login-form">
            {% csrf_token %}
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user-shield"></i>
                    管理员账号
                </label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i>
                    密码
                </label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="form-actions">
                <button type="submit" class="login-btn admin-login-btn">
                    <i class="fas fa-sign-in-alt"></i> 登录管理系统
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 100px);
    background-color: #f8f9fa;
    padding: 20px;
}

.login-box {
    background: white;
    padding: 30px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
}

.login-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.login-header h2 {
    color: #333;
    margin: 0;
    font-size: 24px;
}

.user-login-link {
    color: #28a745;
    text-decoration: none;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.3s;
}

.user-login-link:hover {
    background: #f8f9fa;
    color: #218838;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    color: #555;
    font-size: 14px;
}

.form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-group input:focus {
    border-color: #dc3545;
    outline: none;
    box-shadow: 0 0 0 2px rgba(220,53,69,0.25);
}

.form-actions {
    margin-top: 30px;
}

.admin-login-btn {
    width: 100%;
    padding: 12px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.admin-login-btn:hover {
    background: #c82333;
}

.alert {
    padding: 12px;
    margin-bottom: 20px;
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}
</style>
{% endblock %} 