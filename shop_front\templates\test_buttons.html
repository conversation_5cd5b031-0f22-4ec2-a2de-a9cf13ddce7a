<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试收藏和购物车按钮</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .product-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            background: white;
        }
        .product-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .action-btn {
            padding: 10px 15px;
            border: 1px solid #dc3545;
            background: white;
            color: #dc3545;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .action-btn:hover {
            background: #dc3545;
            color: white;
        }
        .action-btn.active {
            background: #dc3545;
            color: white;
        }
        .buy-now {
            padding: 10px 20px;
            background: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .buy-now:hover {
            background: #c82333;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-family: monospace;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 收藏和购物车功能测试</h1>
        
        <div class="debug-info">
            <h3>🔍 调试信息</h3>
            <div id="debug-output"></div>
        </div>
        
        <div class="status info">
            <strong>用户状态:</strong> 
            {% if user.is_authenticated %}
                ✅ 已登录 - {{ user.username }}
            {% else %}
                ❌ 未登录
            {% endif %}
        </div>
        
        {% if user.is_authenticated %}
        <div class="product-card">
            <h3>📱 测试商品 1</h3>
            <p>这是一个测试商品，用于验证收藏和购物车功能。</p>
            <div class="product-actions">
                <button class="action-btn" data-product-id="1" onclick="toggleFavorite(this)" title="收藏">
                    <i class="far fa-heart"></i> 收藏
                </button>
                <button class="action-btn" data-product-id="1" onclick="addToCart(this)" title="加入购物车">
                    <i class="fas fa-shopping-cart"></i> 加入购物车
                </button>
                <a href="#" class="buy-now">立即购买</a>
            </div>
        </div>
        
        <div class="product-card">
            <h3>💻 测试商品 2</h3>
            <p>另一个测试商品，用于验证功能是否正常工作。</p>
            <div class="product-actions">
                <button class="action-btn" data-product-id="2" onclick="toggleFavorite(this)" title="收藏">
                    <i class="far fa-heart"></i> 收藏
                </button>
                <button class="action-btn" data-product-id="2" onclick="addToCart(this)" title="加入购物车">
                    <i class="fas fa-shopping-cart"></i> 加入购物车
                </button>
                <a href="#" class="buy-now">立即购买</a>
            </div>
        </div>
        {% else %}
        <div class="status error">
            <strong>⚠️ 请先登录</strong><br>
            <a href="{% url 'account:login' %}?next={{ request.path }}">点击这里登录</a>
        </div>
        {% endif %}
        
        <div class="debug-info">
            <h3>🔗 URL测试</h3>
            <div id="url-test"></div>
        </div>
    </div>

    <script>
        // 调试输出函数
        function debugLog(message, type = 'info') {
            const output = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
        }

        // 收藏功能
        function toggleFavorite(btn) {
            debugLog('🔄 开始执行收藏功能...', 'info');
            
            {% if user.is_authenticated %}
            const productId = btn.getAttribute('data-product-id');
            const icon = btn.querySelector('i');
            const isCurrentlyFavorite = icon.classList.contains('fas');
            
            debugLog(`商品ID: ${productId}, 当前状态: ${isCurrentlyFavorite ? '已收藏' : '未收藏'}`, 'info');
            
            const url = isCurrentlyFavorite ? 
                `/account/remove-from-favorite/${productId}/` : 
                `/account/add-favorite/${productId}/`;
            
            debugLog(`请求URL: ${url}`, 'info');
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => {
                debugLog(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                return response.json();
            })
            .then(data => {
                debugLog(`响应数据: ${JSON.stringify(data)}`, data.success ? 'success' : 'error');
                
                if (data.success) {
                    // 切换图标状态
                    if (isCurrentlyFavorite) {
                        icon.classList.remove('fas');
                        icon.classList.add('far');
                        btn.classList.remove('active');
                    } else {
                        icon.classList.remove('far');
                        icon.classList.add('fas');
                        btn.classList.add('active');
                    }
                    debugLog('✅ 收藏状态更新成功', 'success');
                } else {
                    debugLog(`❌ 收藏失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                debugLog(`❌ 网络错误: ${error}`, 'error');
            });
            {% else %}
            debugLog('❌ 用户未登录，跳转到登录页面', 'error');
            window.location.href = "{% url 'account:login' %}?next=" + window.location.pathname;
            {% endif %}
        }

        // 购物车功能
        function addToCart(button) {
            debugLog('🛒 开始执行购物车功能...', 'info');
            
            const productId = button.getAttribute('data-product-id');
            debugLog(`商品ID: ${productId}`, 'info');
            
            const originalHTML = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            button.disabled = true;
            
            fetch('/order/cart/add/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `product_id=${productId}&quantity=1`
            })
            .then(response => {
                debugLog(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                return response.json();
            })
            .then(data => {
                debugLog(`响应数据: ${JSON.stringify(data)}`, data.success ? 'success' : 'error');
                
                if (data.success) {
                    debugLog('✅ 添加到购物车成功', 'success');
                } else {
                    debugLog(`❌ 添加失败: ${data.message}`, 'error');
                }
            })
            .catch(error => {
                debugLog(`❌ 网络错误: ${error}`, 'error');
            })
            .finally(() => {
                button.innerHTML = originalHTML;
                button.disabled = false;
            });
        }

        // 测试URL配置
        function testUrls() {
            const urlTest = document.getElementById('url-test');
            const urls = [
                { name: '添加收藏', url: '/account/add-favorite/1/' },
                { name: '移除收藏', url: '/account/remove-from-favorite/1/' },
                { name: '添加购物车', url: '/order/cart/add/' },
                { name: '购物车页面', url: '/order/cart/' }
            ];
            
            urls.forEach(item => {
                urlTest.innerHTML += `<div>✅ ${item.name}: ${item.url}</div>`;
            });
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('📄 页面加载完成', 'success');
            testUrls();
            
            // 检查CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
            if (csrfToken) {
                debugLog('✅ CSRF token 找到', 'success');
            } else {
                debugLog('❌ CSRF token 未找到', 'error');
            }
        });
    </script>
    
    {% csrf_token %}
</body>
</html>
