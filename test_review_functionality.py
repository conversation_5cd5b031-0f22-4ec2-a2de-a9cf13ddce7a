#!/usr/bin/env python3
"""
测试评价功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'shop_front'))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.contrib.auth import get_user_model
from order.models import Order, OrderItem
from goods.models import Product
from reviews.models import Review

User = get_user_model()

def test_review_functionality():
    print("🧪 测试评价功能...")
    print("=" * 50)
    
    # 1. 检查是否有测试用户
    print("1. 检查测试用户...")
    try:
        user = User.objects.get(username='testuser')
        print(f"   ✅ 找到测试用户: {user.username}")
    except User.DoesNotExist:
        print("   ❌ 测试用户不存在")
        return
    
    # 2. 检查用户的订单
    print("\n2. 检查用户订单...")
    orders = Order.objects.filter(user=user)
    print(f"   用户订单数量: {orders.count()}")
    
    if orders.exists():
        for order in orders:
            print(f"   订单: {order.order_number} - 状态: {order.status}")
            items = order.items.all()
            print(f"     商品数量: {items.count()}")
            for item in items:
                print(f"       - {item.product.name} (已评价: {item.is_reviewed})")
    else:
        print("   ⚠️  用户没有订单")
    
    # 3. 检查已支付的订单
    print("\n3. 检查已支付订单...")
    paid_orders = Order.objects.filter(user=user, status='paid')
    print(f"   已支付订单数量: {paid_orders.count()}")
    
    if paid_orders.exists():
        for order in paid_orders:
            print(f"   ✅ 已支付订单: {order.order_number}")
            print(f"      可以评价的商品:")
            for item in order.items.filter(is_reviewed=False):
                print(f"        - {item.product.name}")
    else:
        print("   ⚠️  没有已支付的订单")
        
        # 创建一个测试订单
        print("   🔧 创建测试订单...")
        try:
            # 获取一个商品
            product = Product.objects.first()
            if product:
                order = Order.objects.create(
                    user=user,
                    order_number=f'TEST{user.id}001',
                    status='paid',
                    total_amount=product.price,
                    pay_amount=product.price
                )
                
                OrderItem.objects.create(
                    order=order,
                    product=product,
                    price=product.price,
                    quantity=1
                )
                
                print(f"   ✅ 创建测试订单: {order.order_number}")
            else:
                print("   ❌ 没有商品可用于创建测试订单")
        except Exception as e:
            print(f"   ❌ 创建测试订单失败: {e}")
    
    # 4. 检查评价数据
    print("\n4. 检查评价数据...")
    reviews = Review.objects.filter(user=user)
    print(f"   用户评价数量: {reviews.count()}")
    
    if reviews.exists():
        for review in reviews:
            print(f"   评价: {review.product.name} - 评分: {review.score}")
    
    # 5. 检查URL配置
    print("\n5. 检查URL配置...")
    try:
        from django.urls import reverse
        url = reverse('reviews:add_review')
        print(f"   ✅ 评价URL: {url}")
    except Exception as e:
        print(f"   ❌ URL配置错误: {e}")
    
    print("\n🎉 测试完成!")
    
    # 输出测试建议
    print("\n💡 测试建议:")
    print("1. 登录用户: testuser / 123456")
    print("2. 访问订单列表: http://127.0.0.1:8001/users/orders/")
    print("3. 查找已支付的订单，点击'去评价'按钮")
    print("4. 填写评价内容并提交")

if __name__ == "__main__":
    test_review_functionality()
