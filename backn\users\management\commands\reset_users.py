from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
import random
import os
from django.conf import settings

User = get_user_model()


class Command(BaseCommand):
    help = '删除所有普通用户并创建新的测试用户'

    def handle(self, *args, **options):
        self.stdout.write('开始重置用户数据...')
        
        # 用户数据
        users_data = [
            {
                'username': 'RainbowWang',
                'email': '<EMAIL>',
                'phone': '13812345678',
                'address': '上海市浦东新区世纪大道 100 号',
                'real_name': '王雨晴'
            },
            {
                'username': '<PERSON><PERSON><PERSON>',
                'email': '<EMAIL>',
                'phone': '13987654321',
                'address': '北京市朝阳区望京街道望京西园三区',
                'real_name': '陈俊杰'
            },
            {
                'username': 'PoetryLi',
                'email': '<EMAIL>',
                'phone': '15823456789',
                'address': '广州市天河区珠江新城花城大道',
                'real_name': '李诗涵'
            },
            {
                'username': 'BraveZhang',
                'email': '<EMAIL>',
                'phone': '15967890123',
                'address': '深圳市南山区高新园科技南路',
                'real_name': '张浩然'
            },
            {
                'username': 'DreamLiu',
                'email': '<EMAIL>',
                'phone': '13654321098',
                'address': '杭州市西湖区文二路 200 号',
                'real_name': '刘梦婷'
            },
            {
                'username': 'SkyZhao',
                'email': '<EMAIL>',
                'phone': '13798765432',
                'address': '成都市锦江区春熙路街道红星路三段',
                'real_name': '赵宇轩'
            },
            {
                'username': 'SparkleWu',
                'email': '<EMAIL>',
                'phone': '15012345678',
                'address': '南京市鼓楼区中山路 101 号',
                'real_name': '吴思琪'
            },
            {
                'username': 'HeroZhou',
                'email': '<EMAIL>',
                'phone': '13478901234',
                'address': '武汉市江汉区解放大道 686 号',
                'real_name': '周嘉豪'
            },
            {
                'username': 'ElegantZheng',
                'email': '<EMAIL>',
                'phone': '15678901234',
                'address': '重庆市渝中区解放碑街道民权路',
                'real_name': '郑雅文'
            },
            {
                'username': 'SunnySun',
                'email': '<EMAIL>',
                'phone': '13345678901',
                'address': '西安市碑林区东大街 350 号',
                'real_name': '孙泽宇'
            },
            {
                'username': 'DawnLin',
                'email': '<EMAIL>',
                'phone': '15123456789',
                'address': '福州市鼓楼区五四路 158 号',
                'real_name': '林晓薇'
            },
            {
                'username': 'ShineHe',
                'email': '<EMAIL>',
                'phone': '13256789012',
                'address': '长沙市芙蓉区五一大道 882 号',
                'real_name': '何俊辉'
            },
            {
                'username': 'RainGuo',
                'email': '<EMAIL>',
                'phone': '15790123456',
                'address': '天津市和平区南京路 189 号',
                'real_name': '郭雨桐'
            },
            {
                'username': 'PeakMa',
                'email': '<EMAIL>',
                'phone': '13012345678',
                'address': '合肥市蜀山区长江西路 200 号',
                'real_name': '马晓峰'
            },
            {
                'username': 'LilyXu',
                'email': '<EMAIL>',
                'phone': '15345678901',
                'address': '苏州市工业园区金鸡湖大道 100 号',
                'real_name': '徐若琳'
            }
        ]
        
        # 获取头像文件列表
        avatar_files = [
            'avatars/头像图片生成.png',
            'avatars/头像图片生成 (1).png',
            'avatars/头像图片生成 (2).png',
            'avatars/头像图片生成 (3).png',
            'avatars/头像图片生成 (4).png',
            'avatars/头像图片生成 (5).png',
            'avatars/头像图片生成 (6).png',
            'avatars/头像图片生成 (7).png',
            'avatars/头像图片生成 (8).png',
            'avatars/头像图片生成 (9).png',
            'avatars/默认头像.png'
        ]
        
        try:
            with transaction.atomic():
                # 删除所有普通用户（保留管理员）
                regular_users = User.objects.filter(is_staff=False, is_superuser=False)
                deleted_count = regular_users.count()
                regular_users.delete()
                self.stdout.write(f'删除了 {deleted_count} 个普通用户')
                
                # 创建新用户
                created_count = 0
                for user_data in users_data:
                    # 随机选择头像
                    avatar_file = random.choice(avatar_files)
                    
                    user = User.objects.create_user(
                        username=user_data['username'],
                        email=user_data['email'],
                        password='123456',  # 默认密码
                        phone=user_data.get('phone', ''),
                        address=user_data.get('address', ''),
                        avatar=avatar_file,
                        is_active=True
                    )
                    
                    # 如果User模型有real_name字段，设置真实姓名
                    if hasattr(user, 'real_name'):
                        user.real_name = user_data['real_name']
                        user.save()
                    
                    created_count += 1
                    self.stdout.write(f'创建用户: {user.username} ({user_data["real_name"]}) - 头像: {avatar_file}')
                
                self.stdout.write(self.style.SUCCESS(f'成功创建了 {created_count} 个新用户！'))
                self.stdout.write(self.style.SUCCESS('所有用户的默认密码为: 123456'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'重置用户数据时出错: {e}'))
            
        self.stdout.write('用户数据重置完成！')
