from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from goods.models import Product

class Order(models.Model):
    """订单模型"""
    STATUS_CHOICES = [
        ('pending', _('待付款')),
        ('paid', _('已付款')),
        ('shipped', _('已发货')),
        ('delivered', _('已送达')),
        ('cancelled', _('已取消')),
        ('refunded', _('已退款')),
    ]

    PAYMENT_CHOICES = [
        ('alipay', _('支付宝')),
        ('wechat', _('微信支付')),
        ('balance', _('余额支付')),
    ]

    user = models.ForeignKey(settings.AUTH_USER_MODEL, verbose_name=_('用户'),
                           on_delete=models.CASCADE, related_name='orders')
    order_no = models.CharField(_('订单号'), max_length=50, unique=True, db_index=True)
    status = models.CharField(_('订单状态'), max_length=20, choices=STATUS_CHOICES,
                            default='pending', db_index=True)
    payment_method = models.CharField(_('支付方式'), max_length=20, choices=PAYMENT_CHOICES,
                                    blank=True, null=True)
    total_amount = models.DecimalField(_('订单总额'), max_digits=10, decimal_places=2, default=0)
    discount_amount = models.DecimalField(_('优惠金额'), max_digits=10, decimal_places=2, default=0)
    final_amount = models.DecimalField(_('实付金额'), max_digits=10, decimal_places=2, default=0)

    # 收货信息
    receiver_name = models.CharField(_('收货人'), max_length=100)
    receiver_phone = models.CharField(_('收货电话'), max_length=20)
    receiver_address = models.TextField(_('收货地址'))

    # 时间信息
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)
    paid_time = models.DateTimeField(_('付款时间'), null=True, blank=True)
    shipped_time = models.DateTimeField(_('发货时间'), null=True, blank=True)
    delivered_time = models.DateTimeField(_('送达时间'), null=True, blank=True)

    # 备注信息
    remark = models.TextField(_('订单备注'), blank=True)

    class Meta:
        verbose_name = _('订单')
        verbose_name_plural = _('订单')
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['order_no']),
            models.Index(fields=['created_time']),
            models.Index(fields=['status', 'created_time']),
        ]

    def __str__(self):
        return f"订单 {self.order_no}"

    def get_status_display_color(self):
        """获取状态显示颜色"""
        color_map = {
            'pending': 'warning',
            'paid': 'info',
            'shipped': 'primary',
            'delivered': 'success',
            'cancelled': 'secondary',
            'refunded': 'danger',
        }
        return color_map.get(self.status, 'secondary')

class OrderItem(models.Model):
    """订单商品项模型"""
    order = models.ForeignKey(Order, verbose_name=_('订单'),
                            on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, verbose_name=_('商品'),
                              on_delete=models.CASCADE, related_name='order_items')
    product_name = models.CharField(_('商品名称'), max_length=200)  # 冗余字段，防止商品被删除
    product_price = models.DecimalField(_('商品单价'), max_digits=10, decimal_places=2)
    quantity = models.IntegerField(_('购买数量'), default=1)
    subtotal = models.DecimalField(_('小计'), max_digits=10, decimal_places=2)

    class Meta:
        verbose_name = _('订单商品项')
        verbose_name_plural = _('订单商品项')
        indexes = [
            models.Index(fields=['order', 'product']),
        ]

    def __str__(self):
        return f"{self.order.order_no} - {self.product_name}"

    def save(self, *args, **kwargs):
        """保存时自动计算小计"""
        self.subtotal = self.product_price * self.quantity
        super().save(*args, **kwargs)
