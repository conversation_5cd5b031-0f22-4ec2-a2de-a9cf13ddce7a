from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from django.utils.translation import gettext_lazy as _
from rest_framework import permissions


class IsFrontendUser(permissions.BasePermission):
    """
    前台用户权限 - 只读或受限写权限
    """
    message = _('前台用户只有只读或受限写权限')

    def has_permission(self, request, view):
        # 允许GET, HEAD, OPTIONS请求
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # 对于其他请求方法，检查用户是否有特定权限
        # 例如，只有特定前台用户才能创建或更新某些资源
        if request.user and request.user.is_authenticated:
            # 检查用户是否属于前台用户组
            if request.user.groups.filter(name='frontend_users').exists():
                # 根据视图名称和动作确定所需权限
                app_label = view.queryset.model._meta.app_label
                model_name = view.queryset.model._meta.model_name
                
                if request.method == 'POST':
                    return request.user.has_perm(f'{app_label}.add_{model_name}')
                elif request.method == 'PUT' or request.method == 'PATCH':
                    return request.user.has_perm(f'{app_label}.change_{model_name}')
                elif request.method == 'DELETE':
                    return request.user.has_perm(f'{app_label}.delete_{model_name}')
        
        return False


class IsBackendUser(permissions.BasePermission):
    """
    后台用户权限 - 完全控制权限
    """
    message = _('只有后台用户才能访问此资源')

    def has_permission(self, request, view):
        # 检查用户是否已认证且是后台用户
        return (
            request.user and 
            request.user.is_authenticated and
            request.user.groups.filter(name='backend_users').exists()
        )


def setup_permissions():
    """
    设置前台和后台用户组及其权限
    """
    # 创建前台和后台用户组
    frontend_group, _ = Group.objects.get_or_create(name='frontend_users')
    backend_group, _ = Group.objects.get_or_create(name='backend_users')
    
    # 获取共享模型的内容类型
    from common.models import SystemConfig, SharedCategory
    system_config_ct = ContentType.objects.get_for_model(SystemConfig)
    shared_category_ct = ContentType.objects.get_for_model(SharedCategory)
    
    # 前台用户权限 - 只读权限
    frontend_perms = [
        Permission.objects.get(codename='view_systemconfig', content_type=system_config_ct),
        Permission.objects.get(codename='view_sharedcategory', content_type=shared_category_ct),
    ]
    frontend_group.permissions.set(frontend_perms)
    
    # 后台用户权限 - 完全控制权限
    backend_perms = Permission.objects.filter(
        content_type__in=[system_config_ct, shared_category_ct]
    )
    backend_group.permissions.set(backend_perms) 