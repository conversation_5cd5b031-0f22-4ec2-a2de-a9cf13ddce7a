{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block title %}结算订单 - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    .checkout-container {
        max-width: 1200px;
        margin: 40px auto;
        padding: 30px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
    }

    .checkout-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }

    .checkout-title {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .checkout-title i {
        color: #dc3545;
    }

    .checkout-section {
        margin-bottom: 40px;
    }

    .section-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .section-title i {
        color: #dc3545;
    }

    /* 商品列表 */
    .product-list {
        margin-bottom: 30px;
    }

    .product-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        border-bottom: 1px solid #f5f5f5;
    }

    .product-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 20px;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-info {
        flex: 1;
    }

    .product-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
    }

    .product-price {
        color: #666;
        font-size: 14px;
    }

    .product-quantity {
        margin-left: 30px;
        font-size: 14px;
        color: #666;
    }

    .product-total {
        font-size: 16px;
        font-weight: 600;
        color: #dc3545;
        margin-left: 30px;
        min-width: 100px;
        text-align: right;
    }

    /* 价格摘要 */
    .price-summary {
        background: #f9f9f9;
        border-radius: 12px;
        padding: 20px;
        margin-top: 30px;
    }

    .price-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
    }

    .price-row:last-child {
        margin-bottom: 0;
        padding-top: 15px;
        border-top: 1px dashed #ddd;
    }

    .price-label {
        color: #666;
    }

    .price-value {
        font-weight: 600;
        color: #333;
    }

    .price-row:last-child .price-label,
    .price-row:last-child .price-value {
        font-size: 18px;
        font-weight: 700;
        color: #dc3545;
    }

    /* 收货信息表单 */
    .form-group {
        margin-bottom: 25px;
    }

    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
    }

    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 16px;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #dc3545;
        box-shadow: 0 0 0 3px rgba(220,53,69,0.1);
        outline: none;
    }

    textarea.form-control {
        min-height: 100px;
        resize: vertical;
    }

    /* 提交按钮 */
    .submit-row {
        margin-top: 40px;
        display: flex;
        justify-content: flex-end;
    }

    .submit-btn {
        padding: 15px 40px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border: none;
        border-radius: 30px;
        font-size: 18px;
        font-weight: 700;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
    }

    .submit-btn:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
    }

    /* 优惠信息 */
    .discount-info {
        margin-top: 20px;
        padding: 15px;
        background: #fef8f8;
        border-left: 4px solid #dc3545;
        border-radius: 8px;
    }

    .discount-text {
        color: #dc3545;
        font-weight: 600;
    }
</style>
{% endblock %}

{% block content %}
<div class="checkout-container">
    <div class="checkout-header">
        <div class="checkout-title">
            <i class="fas fa-clipboard-check"></i>
            订单结算
        </div>
    </div>

    <div class="checkout-section">
        <div class="section-title">
            <i class="fas fa-shopping-basket"></i>
            商品信息
        </div>

        <div class="product-list">
            {% for item in cart_items %}
            <div class="product-item">
                <div class="product-image">
                    <img src="{{ item.product.main_image.url }}" alt="{{ item.product.name }}">
                </div>
                <div class="product-info">
                    <div class="product-name">{{ item.product.name }}</div>
                    <div class="product-price">¥{{ item.product.price }}</div>
                </div>
                <div class="product-quantity">x {{ item.quantity }}</div>
                <div class="product-total">¥{{ item.total_price }}</div>
            </div>
            {% endfor %}
        </div>

        <div class="price-summary">
            <div class="price-row">
                <div class="price-label">商品总价</div>
                <div class="price-value">¥{{ total_price }}</div>
            </div>
            {% if discount > 0 %}
            <div class="price-row">
                <div class="price-label">满减优惠</div>
                <div class="price-value">-¥{{ discount }}</div>
            </div>
            <div class="discount-info">
                <div class="discount-text">
                    {% if total_price >= 1000 %}
                    满1000减200优惠已应用
                    {% elif total_price >= 300 %}
                    满300减50优惠已应用
                    {% elif total_price >= 100 %}
                    满100减10优惠已应用
                    {% endif %}
                </div>
            </div>
            {% endif %}
            <div class="price-row">
                <div class="price-label">应付金额</div>
                <div class="price-value">¥{{ final_price }}</div>
            </div>
        </div>
    </div>

    <form method="post" action="{% url 'order:create' %}">
        {% csrf_token %}
        <div class="checkout-section">
            <div class="section-title">
                <i class="fas fa-map-marker-alt"></i>
                收货信息
            </div>

            <div class="form-group">
                <label class="form-label" for="receiver">收货人</label>
                <input type="text" class="form-control" id="receiver" name="receiver" value="{{ user.username }}" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="contact_phone">联系电话</label>
                <input type="tel" class="form-control" id="contact_phone" name="contact_phone" value="{{ user.phone }}" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="address">收货地址</label>
                <input type="text" class="form-control" id="address" name="address" value="{{ user.address }}" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="remark">订单备注</label>
                <textarea class="form-control" id="remark" name="remark" placeholder="选填，请填写与订单相关的备注信息"></textarea>
            </div>
        </div>

        <div class="submit-row">
            <button type="submit" class="submit-btn">
                <i class="fas fa-credit-card"></i>
                提交订单
            </button>
        </div>
    </form>
</div>
{% endblock %} 