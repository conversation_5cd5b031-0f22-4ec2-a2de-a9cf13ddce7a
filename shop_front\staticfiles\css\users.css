/* 用户中心页面样式 */
.user-profile {
    padding: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.user-info {
    margin-bottom: 30px;
}

.user-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 15px;
}

.user-name {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
}

.user-meta {
    color: #666;
    font-size: 14px;
}

/* 登录页面样式 */
.login-container {
    width: 400px;
    margin: 50px auto;
    padding: 30px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.login-title {
    text-align: center;
    font-size: 24px;
    color: #333;
    margin-bottom: 30px;
}

.login-form .form-group {
    margin-bottom: 20px;
}

.login-form .form-control {
    height: 40px;
}

.login-form .btn-login {
    width: 100%;
    height: 40px;
    background-color: #DD302D;
    color: #fff;
    border: none;
    font-size: 16px;
}

.login-links {
    text-align: right;
    margin-top: 15px;
}

.login-links a {
    color: #666;
    font-size: 14px;
    margin-left: 15px;
}

/* 注册页面样式 */
.register-container {
    width: 500px;
    margin: 50px auto;
    padding: 30px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.register-title {
    text-align: center;
    font-size: 24px;
    color: #333;
    margin-bottom: 30px;
}

.register-form .form-group {
    margin-bottom: 20px;
}

.register-form .form-control {
    height: 40px;
}

.register-form .btn-register {
    width: 100%;
    height: 40px;
    background-color: #DD302D;
    color: #fff;
    border: none;
    font-size: 16px;
}

.register-tips {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.register-tips h3 {
    font-size: 16px;
    color: #333;
    margin-bottom: 10px;
}

.register-tips p {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
}

/* 会员中心页面样式 */
.vip-container {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.vip-header {
    text-align: center;
    margin-bottom: 30px;
}

.vip-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.vip-subtitle {
    font-size: 16px;
    color: #666;
}

.vip-level {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.level-item {
    flex: 1;
    text-align: center;
    padding: 20px;
    margin: 0 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.level-item:hover {
    border-color: #DD302D;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.level-item.active {
    border-color: #DD302D;
    background-color: #fff9f9;
}

.level-name {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
}

.level-price {
    font-size: 24px;
    color: #DD302D;
    margin-bottom: 15px;
}

.level-price small {
    font-size: 14px;
    color: #666;
}

.level-features {
    text-align: left;
    margin-top: 15px;
}

.level-features li {
    color: #666;
    font-size: 14px;
    margin-bottom: 5px;
    padding-left: 20px;
    position: relative;
}

.level-features li::before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #DD302D;
}

.vip-benefits {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #ddd;
}

.benefits-title {
    font-size: 20px;
    color: #333;
    margin-bottom: 20px;
    text-align: center;
}

.benefits-list {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.benefit-item {
    width: calc(25% - 20px);
    text-align: center;
    margin-bottom: 20px;
}

.benefit-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 10px;
}

.benefit-name {
    font-size: 16px;
    color: #333;
    margin-bottom: 5px;
}

.benefit-desc {
    font-size: 14px;
    color: #666;
}

/* 用户中心样式 */
.user-center {
    margin-top: 20px;
}

.user-sidebar {
    width: 190px;
    background-color: #f4f4f4;
    border: 1px solid #e4e4e4;
    float: left;
}

.user-sidebar .user-info {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid #e4e4e4;
}

.user-sidebar .user-info .avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 10px;
    overflow: hidden;
}

.user-sidebar .user-info .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-sidebar .user-info .username {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.user-sidebar .user-info .user-level {
    display: inline-block;
    margin-top: 5px;
    padding: 2px 8px;
    background-color: #DD302D;
    color: #fff;
    border-radius: 10px;
    font-size: 12px;
}

.user-sidebar .menu-list {
    padding: 10px 0;
}

.user-sidebar .menu-list .menu-item {
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    font-size: 14px;
}

.user-sidebar .menu-list .menu-item:hover {
    background-color: #e4e4e4;
}

.user-sidebar .menu-list .menu-item.active {
    background-color: #DD302D;
}

.user-sidebar .menu-list .menu-item.active a {
    color: #fff;
}

.user-sidebar .menu-list .menu-item a {
    display: block;
    color: #333;
}

.user-sidebar .menu-list .menu-item i {
    margin-right: 10px;
}

.user-content {
    width: 990px;
    float: right;
    background-color: #fff;
    border: 1px solid #e4e4e4;
}

.user-content .content-header {
    height: 50px;
    line-height: 50px;
    padding: 0 20px;
    border-bottom: 1px solid #e4e4e4;
    background-color: #f8f8f8;
}

.user-content .content-header h3 {
    font-size: 16px;
    color: #333;
    float: left;
}

.user-content .content-body {
    padding: 20px;
}

/* 登录注册样式 */
.login-register-container {
    width: 400px;
    margin: 50px auto;
    background-color: #fff;
    border: 1px solid #e4e4e4;
    padding: 30px;
}

.login-register-container .form-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-register-container .form-header h2 {
    font-size: 24px;
    color: #333;
}

.login-register-container .form-group {
    margin-bottom: 20px;
}

.login-register-container .form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
}

.login-register-container .form-control {
    width: 100%;
    height: 40px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 2px;
    font-size: 14px;
}

.login-register-container .form-control:focus {
    border-color: #DD302D;
    outline: none;
}

.login-register-container .btn {
    width: 100%;
    height: 40px;
    background-color: #DD302D;
    color: #fff;
    border: none;
    border-radius: 2px;
    font-size: 16px;
    cursor: pointer;
}

.login-register-container .btn:hover {
    background-color: #c82333;
}

.login-register-container .form-footer {
    margin-top: 20px;
    text-align: center;
}

.login-register-container .form-footer a {
    color: #DD302D;
}

/* 个人信息样式 */
.user-profile .profile-item {
    margin-bottom: 20px;
}

.user-profile .profile-item label {
    display: inline-block;
    width: 100px;
    text-align: right;
    margin-right: 20px;
    font-size: 14px;
    color: #666;
}

.user-profile .profile-item .profile-value {
    display: inline-block;
    font-size: 14px;
    color: #333;
}

.user-profile .profile-item .btn-edit {
    margin-left: 20px;
    color: #DD302D;
    cursor: pointer;
}

/* 地址管理样式 */
.address-list {
    margin-bottom: 20px;
}

.address-item {
    padding: 15px;
    border: 1px solid #e4e4e4;
    margin-bottom: 10px;
    position: relative;
}

.address-item.default {
    border-color: #DD302D;
}

.address-item .address-info {
    margin-bottom: 10px;
}

.address-item .address-info span {
    margin-right: 10px;
}

.address-item .address-actions {
    position: absolute;
    right: 15px;
    top: 15px;
}

.address-item .address-actions a {
    margin-left: 10px;
    color: #DD302D;
}

.address-form {
    padding: 20px;
    border: 1px solid #e4e4e4;
}

.address-form .form-group {
    margin-bottom: 15px;
}

.address-form .form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #333;
}

.address-form .form-control {
    width: 100%;
    height: 40px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 2px;
    font-size: 14px;
}

.address-form .form-control:focus {
    border-color: #DD302D;
    outline: none;
}

.address-form .btn {
    margin-top: 10px;
    padding: 8px 20px;
    background-color: #DD302D;
    color: #fff;
    border: none;
    border-radius: 2px;
    font-size: 14px;
    cursor: pointer;
}

.address-form .btn:hover {
    background-color: #c82333;
} 