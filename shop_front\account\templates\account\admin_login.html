{% extends 'base.html' %}
{% load static %}

{% block title %}管理员登录 - 尚品汇{% endblock %}

{% block extra_css %}
<style>
    .admin-login-container {
        max-width: 450px;
        margin: 40px auto;
        padding: 30px;
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        border-radius: 16px;
        box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        position: relative;
        overflow: hidden;
        color: #fff;
    }
    
    .admin-login-container:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #FFD700, #FFA500, #FFD700);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
    }
    
    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    .admin-login-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .admin-login-header h2 {
        font-size: 28px;
        color: #FFD700;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .admin-login-header p {
        color: rgba(255,255,255,0.8);
        font-size: 14px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #FFD700;
        font-weight: bold;
    }

    .form-control {
        width: 100%;
        height: 45px;
        padding: 12px 16px;
        border: 2px solid #444;
        border-radius: 8px;
        font-size: 14px;
        background: rgba(255,255,255,0.1);
        color: #fff;
        transition: all 0.3s ease;
    }

    .form-control:focus {
        border-color: #FFD700;
        outline: none;
        box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.2);
        background: rgba(255,255,255,0.15);
    }

    .form-control::placeholder {
        color: rgba(255,255,255,0.5);
    }

    .btn-admin-login {
        width: 100%;
        height: 50px;
        background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
        color: #333;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }
    
    .btn-admin-login:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s;
    }
    
    .btn-admin-login:hover:before {
        left: 100%;
    }

    .btn-admin-login:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
    }

    .admin-login-footer {
        text-align: center;
        margin-top: 20px;
        color: rgba(255,255,255,0.6);
    }

    .admin-login-footer a {
        color: #FFD700;
        text-decoration: none;
    }

    .admin-login-footer a:hover {
        text-decoration: underline;
    }

    .error-message {
        color: #ff6b6b;
        margin-bottom: 15px;
        padding: 12px;
        background: rgba(255, 107, 107, 0.1);
        border-radius: 8px;
        border-left: 4px solid #ff6b6b;
    }

    .admin-badge {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        background: rgba(255, 215, 0, 0.1);
        padding: 8px 16px;
        border-radius: 20px;
        margin-bottom: 20px;
        border: 1px solid rgba(255, 215, 0, 0.3);
    }

    .security-notice {
        background: rgba(255, 215, 0, 0.1);
        border: 1px solid rgba(255, 215, 0, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        font-size: 13px;
        color: rgba(255,255,255,0.8);
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="admin-login-container">
        <div class="admin-login-header">
            <h2>
                <i class="fas fa-shield-alt"></i>
                管理员登录
            </h2>
            <p>管理员专用安全登录入口</p>
        </div>
        
        <div class="admin-badge">
            <i class="fas fa-crown" style="color: #FFD700;"></i>
            <span>管理员权限验证</span>
        </div>
        
        <div class="security-notice">
            <i class="fas fa-info-circle"></i>
            <strong>安全提示：</strong>此登录入口仅供系统管理员使用，需要管理员权限才能登录。
        </div>
        
        {% if messages %}
        {% for message in messages %}
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i>
            {{ message }}
        </div>
        {% endfor %}
        {% endif %}
        
        <form method="post">
            {% csrf_token %}
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user-shield"></i> 管理员账号
                </label>
                <input type="text" id="username" name="username" class="form-control" 
                       placeholder="请输入管理员用户名" required>
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i> 管理员密码
                </label>
                <input type="password" id="password" name="password" class="form-control" 
                       placeholder="请输入管理员密码" required>
            </div>
            
            <button type="submit" class="btn-admin-login">
                <i class="fas fa-sign-in-alt"></i> 管理员登录
            </button>
        </form>
        
        <div class="admin-login-footer">
            <p>
                <i class="fas fa-arrow-left"></i>
                <a href="{% url 'users:login' %}">返回普通用户登录</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const submitBtn = document.querySelector('.btn-admin-login');
    
    // 添加表单提交动画
    form.addEventListener('submit', function(e) {
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证中...';
        submitBtn.disabled = true;
        
        // 如果有错误，恢复按钮状态
        setTimeout(() => {
            if (document.querySelector('.error-message')) {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        }, 2000);
    });
    
    // 添加输入框焦点动画
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'translateY(-2px)';
            this.parentElement.style.transition = 'all 0.3s ease';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'translateY(0)';
        });
    });
});
</script>
{% endblock %}
