{% extends 'base.html' %}

{% block title %}{% if title %}{{ title }}{% else %}商品列表{% endif %} - MARS BUY{% endblock %}

{% load static %}

{% block extra_css %}
<style>
    /* 全局样式 */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    /* 容器样式 */
    .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }

    /* 页面头部 */
    .page-header {
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.08);
        backdrop-filter: blur(10px);
        text-align: center;
    }

    .page-title {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .page-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 0;
    }

    /* 筛选和排序区域 */
    .filter-section {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 25px 30px;
        margin-bottom: 30px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.08);
        backdrop-filter: blur(10px);
    }

    .filter-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .filter-title i {
        color: #dc3545;
        font-size: 20px;
    }

    .filter-options {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        align-items: center;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .filter-label {
        font-weight: 600;
        color: #555;
        min-width: 80px;
    }

    .filter-btn {
        padding: 8px 16px;
        border: 2px solid rgba(220,53,69,0.2);
        border-radius: 25px;
        background: white;
        color: #666;
        text-decoration: none;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .filter-btn:hover,
    .filter-btn.active {
        border-color: #dc3545;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220,53,69,0.3);
        text-decoration: none;
    }

    /* 商品网格 */
    .product-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 25px;
        margin-bottom: 40px;
    }

    /* 商品卡片 */
    .product-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        backdrop-filter: blur(10px);
    }

    .product-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(220,53,69,0.15);
    }

    /* 商品标签 */
    .product-tag {
        position: absolute;
        top: 15px;
        left: 15px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 600;
        z-index: 2;
        box-shadow: 0 4px 15px rgba(220,53,69,0.3);
    }

    /* 管理员控制 */
    .admin-controls {
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 3;
    }

    .edit-btn {
        background: rgba(0,0,0,0.7);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 20px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .edit-btn:hover {
        background: rgba(220,53,69,0.9);
        transform: scale(1.05);
    }

    /* 商品图片 */
    .product-img-wrapper {
        position: relative;
        height: 250px;
        overflow: hidden;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .product-card:hover .product-image {
        transform: scale(1.1);
    }

    /* 商品信息 */
    .product-info {
        padding: 25px;
    }

    .product-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.4;
        height: 44px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .product-price-container {
        margin-bottom: 20px;
    }

    .product-price {
        font-size: 24px;
        font-weight: 700;
        color: #dc3545;
        text-shadow: 0 1px 2px rgba(220,53,69,0.1);
    }

    .product-meta {
        margin-bottom: 20px;
    }

    .product-sales,
    .product-favorites {
        font-size: 14px;
        color: #666;
        margin-right: 20px;
    }

    .product-sales i,
    .product-favorites i {
        margin-right: 5px;
    }

    /* 商品操作按钮 */
    .product-actions {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .action-btn {
        width: 40px;
        height: 40px;
        border: 2px solid rgba(228, 28, 60, 0.2);
        border-radius: 50%;
        background: white;
        color: #e41c3c;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .action-btn:hover {
        border-color: #e41c3c;
        background: #fff5f7;
        color: #e41c3c;
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(228, 28, 60, 0.15);
    }

    .action-btn.active {
        background: #fff5f7;
        border-color: #e41c3c;
        color: #e41c3c;
    }

    .action-btn:active {
        transform: scale(0.95);
    }

    .action-btn.clicked {
        animation: btnPulse 0.5s ease;
    }

    @keyframes btnPulse {
        0% { transform: scale(1); }
        50% { transform: scale(0.95); }
        100% { transform: scale(1); }
    }

    .action-btn .fas.fa-heart {
        animation: heartBeat 0.5s ease-in-out;
    }

    @keyframes heartBeat {
        0% { transform: scale(1); }
        25% { transform: scale(1.3); }
        50% { transform: scale(1); }
        75% { transform: scale(1.3); }
        100% { transform: scale(1); }
    }

    .buy-now {
        flex: 1;
        padding: 12px 20px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border: none;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(220,53,69,0.3);
    }

    .buy-now:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    /* 分页样式 */
    .pagination-container {
        display: flex;
        justify-content: center;
        margin-top: 40px;
    }

    .pagination {
        display: flex;
        gap: 10px;
        align-items: center;
    }

    .pagination a,
    .pagination span {
        padding: 12px 16px;
        border: 2px solid rgba(220,53,69,0.2);
        border-radius: 12px;
        background: white;
        color: #666;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .pagination a:hover,
    .pagination .current {
        border-color: #dc3545;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220,53,69,0.3);
        text-decoration: none;
    }

    /* 空状态 */
    .empty-state {
        text-align: center;
        padding: 80px 40px;
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.08);
        backdrop-filter: blur(10px);
    }

    .empty-state i {
        font-size: 120px;
        color: #dc3545;
        margin-bottom: 30px;
        opacity: 0.7;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .empty-state p {
        color: #666;
        margin-bottom: 30px;
        font-size: 18px;
        line-height: 1.6;
    }

    .empty-state .btn {
        padding: 15px 40px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border-radius: 30px;
        text-decoration: none;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
        transition: all 0.3s ease;
    }

    .empty-state .btn:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    /* 消息提示样式 */
    .message-toast {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 10px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        animation: slideInRight 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .message-toast.success {
        background-color: #28a745;
    }

    .message-toast.error {
        background-color: #dc3545;
    }

    .message-toast.info {
        background-color: #17a2b8;
    }

    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    

    

    

</style>
{% endblock %}

{% block content %}
<div class="container">
    {% csrf_token %}
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">
            {% if title %}{{ title }}{% elif category %}{{ category.name }}{% else %}MARS BUY 商城{% endif %}
        </h1>
        <p class="page-subtitle">精选优质商品，品质生活从这里开始</p>

        <!-- 管理员工具栏 -->
        {% if user.is_staff %}
        <div style="margin-top: 20px;">
            <a href="{% url 'goods:add_product' %}" class="filter-btn active">
                <i class="fas fa-plus"></i> 添加商品
            </a>
        </div>
        {% endif %}
    </div>

    <!-- 筛选和排序区域 -->
    <div class="filter-section">
        <div class="filter-title">
            <i class="fas fa-filter"></i>
            筛选
        </div>
        <div class="filter-options">
            <div class="filter-group">
                <span class="filter-label">排序方式：</span>
                <a href="?{% if category %}category={{ category.id }}&{% endif %}{% if search %}search={{ search }}&{% endif %}sort=-created_time" class="filter-btn {% if sort == '-created_time' or not sort %}active{% endif %}">最新上架</a>
                <a href="?{% if category %}category={{ category.id }}&{% endif %}{% if search %}search={{ search }}&{% endif %}sort=price" class="filter-btn {% if sort == 'price' %}active{% endif %}">价格从低到高</a>
                <a href="?{% if category %}category={{ category.id }}&{% endif %}{% if search %}search={{ search }}&{% endif %}sort=-price" class="filter-btn {% if sort == '-price' %}active{% endif %}">价格从高到低</a>
                <a href="?{% if category %}category={{ category.id }}&{% endif %}{% if search %}search={{ search }}&{% endif %}sort=-sales" class="filter-btn {% if sort == '-sales' %}active{% endif %}">销量优先</a>
                <a href="?{% if category %}category={{ category.id }}&{% endif %}{% if search %}search={{ search }}&{% endif %}sort=-favorite_count" class="filter-btn {% if sort == '-favorite_count' %}active{% endif %}">收藏最多</a>
            </div>
        </div>
    </div>



    <!-- 热门商品区域 -->
    {% if hot_products %}
    <div class="page-header">
        <h2 class="page-title" style="font-size: 24px; margin-bottom: 5px;">
            <i class="fas fa-fire" style="color: #ff6b35;"></i>
            热门商品
        </h2>
        <p class="page-subtitle">精选热销商品，品质保证</p>
    </div>
    <div class="product-grid">
        {% for product in hot_products %}
        <div class="product-card" data-product-id="{{ product.id }}">
            <div class="product-tag hot-badge">
                <i class="fas fa-fire"></i> 热门
            </div>
            {% if user.is_staff %}
            <div class="admin-controls">
                <button class="edit-btn" data-product-id="{{ product.id }}" onclick="openEditModal(this)">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
            {% endif %}
            <div class="product-img-wrapper">
                <a href="{% url 'goods:detail' product.id %}">
                    {% if product.main_image %}
                        <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="product-image" onerror="this.src='{% static 'images/default-product.jpg' %}'">
                    {% else %}
                        <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}" class="product-image">
                    {% endif %}
                </a>
            </div>
            <div class="product-info">
                <h3 class="product-title">
                    <a href="{% url 'goods:detail' product.id %}" style="color: inherit; text-decoration: none;" class="product-name">{{ product.name }}</a>
                </h3>
                <div class="product-price-container">
                    <div class="product-price">¥{{ product.price }}</div>
                </div>
                <div class="product-meta">
                    <span class="product-sales"><i class="fas fa-shopping-cart"></i> {{ product.sales }}人已购买</span>
                    <span class="product-favorites"><i class="fas fa-heart"></i> {{ product.favorite_count }}人收藏</span>
                </div>
                <div class="product-actions">
                    <button class="action-btn {% if user.is_authenticated and product.id in favorite_products %}active{% endif %}" data-product-id="{{ product.id }}" onclick="toggleFavorite(this)" title="收藏">
                        <i class="{% if user.is_authenticated and product.id in favorite_products %}fas{% else %}far{% endif %} fa-heart"></i>
                    </button>
                    <button class="action-btn" data-product-id="{{ product.id }}" onclick="addToCart(this)" title="加入购物车">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                    <a href="{% url 'goods:detail' product.id %}" class="buy-now">
                        立即购买
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- 新品上市区域 -->
    {% if new_products %}
    <div class="page-header">
        <h2 class="page-title" style="font-size: 24px; margin-bottom: 5px;">
            <i class="fas fa-star" style="color: #ffc107;"></i>
            新品上市
        </h2>
        <p class="page-subtitle">最新上架商品，抢先体验</p>
    </div>
    <div class="product-grid">
        {% for product in new_products %}
        <div class="product-card" data-product-id="{{ product.id }}">
            <div class="product-tag new-badge" style="background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);">
                <i class="fas fa-star"></i> 新品
            </div>
            {% if user.is_staff %}
            <div class="admin-controls">
                <button class="edit-btn" data-product-id="{{ product.id }}" onclick="openEditModal(this)">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
            {% endif %}
            <div class="product-img-wrapper">
                <a href="{% url 'goods:detail' product.id %}">
                    {% if product.main_image %}
                        <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="product-image" onerror="this.src='{% static 'images/default-product.jpg' %}'">
                    {% else %}
                        <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}" class="product-image">
                    {% endif %}
                </a>
            </div>
            <div class="product-info">
                <h3 class="product-title">
                    <a href="{% url 'goods:detail' product.id %}" style="color: inherit; text-decoration: none;" class="product-name">{{ product.name }}</a>
                </h3>
                <div class="product-price-container">
                    <div class="product-price">¥{{ product.price }}</div>
                </div>
                <div class="product-meta">
                    <span class="product-sales"><i class="fas fa-shopping-cart"></i> {{ product.sales }}人已购买</span>
                    <span class="product-favorites"><i class="fas fa-heart"></i> {{ product.favorite_count }}人收藏</span>
                </div>
                <div class="product-actions">
                    <button class="action-btn {% if user.is_authenticated and product.id in favorite_products %}active{% endif %}" data-product-id="{{ product.id }}" onclick="toggleFavorite(this)" title="收藏">
                        <i class="{% if user.is_authenticated and product.id in favorite_products %}fas{% else %}far{% endif %} fa-heart"></i>
                    </button>
                    <button class="action-btn" data-product-id="{{ product.id }}" onclick="addToCart(this)" title="加入购物车">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                    <a href="{% url 'goods:detail' product.id %}" class="buy-now">
                        立即购买
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- 商品列表区域 -->
    {% if products %}
    <div class="product-grid">
        {% for product in products %}
        <div class="product-card" data-product-id="{{ product.id }}">
            {% if product.is_hot %}
            <div class="product-tag hot-badge">
                <i class="fas fa-fire"></i> 热门
            </div>
            {% elif product.is_new %}
            <div class="product-tag new-badge" style="background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);">
                <i class="fas fa-star"></i> 新品
            </div>
            {% endif %}
            {% if user.is_staff %}
            <div class="admin-controls">
                <button class="edit-btn" data-product-id="{{ product.id }}" onclick="openEditModal(this)">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
            {% endif %}
            <div class="product-img-wrapper">
                <a href="{% url 'goods:detail' product.id %}">
                    {% if product.main_image %}
                        <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="product-image" onerror="this.src='{% static 'images/default-product.jpg' %}'">
                    {% else %}
                        <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}" class="product-image">
                    {% endif %}
                </a>
            </div>
            <div class="product-info">
                <h3 class="product-title">
                    <a href="{% url 'goods:detail' product.id %}" style="color: inherit; text-decoration: none;" class="product-name">{{ product.name }}</a>
                </h3>
                <div class="product-price-container">
                    <div class="product-price">¥{{ product.price }}</div>
                </div>
                <div class="product-meta">
                    <span class="product-sales"><i class="fas fa-shopping-cart"></i> {{ product.sales }}人已购买</span>
                    <span class="product-favorites"><i class="fas fa-heart"></i> {{ product.favorite_count }}人收藏</span>
                </div>
                <div class="product-actions">
                    <button class="action-btn {% if user.is_authenticated and product.id in favorite_products %}active{% endif %}" data-product-id="{{ product.id }}" onclick="toggleFavorite(this)" title="收藏">
                        <i class="{% if user.is_authenticated and product.id in favorite_products %}fas{% else %}far{% endif %} fa-heart"></i>
                    </button>
                    <button class="action-btn" data-product-id="{{ product.id }}" onclick="addToCart(this)" title="加入购物车">
                        <i class="fas fa-shopping-cart"></i>
                    </button>
                    <a href="{% url 'goods:detail' product.id %}" class="buy-now">
                        立即购买
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- 分页 -->
    {% if products.has_other_pages %}
    <div class="pagination-container">
        <div class="pagination">
            {% if products.has_previous %}
                <a href="?page=1">&laquo; 首页</a>
                <a href="?page={{ products.previous_page_number }}">上一页</a>
            {% endif %}

            {% for num in products.paginator.page_range %}
                {% if products.number == num %}
                    <span class="current">{{ num }}</span>
                {% elif num > products.number|add:'-3' and num < products.number|add:'3' %}
                    <a href="?page={{ num }}">{{ num }}</a>
                {% endif %}
            {% endfor %}

            {% if products.has_next %}
                <a href="?page={{ products.next_page_number }}">下一页</a>
                <a href="?page={{ products.paginator.num_pages }}">末页 &raquo;</a>
            {% endif %}
        </div>
    </div>
    {% endif %}
    {% else %}
    <!-- 空状态 -->
    <div class="empty-state">
        <i class="fas fa-shopping-bag"></i>
        <p>暂无商品，敬请期待更多精彩内容~</p>
        <a href="{% url 'home:index' %}" class="btn">
            <i class="fas fa-home"></i>
            返回首页
        </a>
    </div>
    {% endif %}
</div>

<!-- 编辑商品模态框 -->
{% if user.is_staff %}
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>编辑商品</h2>
        <form id="editProductForm" method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <input type="hidden" id="productId" name="product_id">
            <div class="form-group">
                <label for="name">商品名称</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label for="price">价格</label>
                <input type="number" id="price" name="price" step="0.01" required>
            </div>
            <div class="form-group">
                <label for="main_image">主图</label>
                <input type="file" id="main_image" name="main_image" accept="image/*">
                <div id="imagePreview"></div>
            </div>
            <div class="form-group">
                <label for="description">商品描述</label>
                <textarea id="description" name="description" rows="4"></textarea>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="is_hot" name="is_hot">
                    热门商品
                </label>
                <label>
                    <input type="checkbox" id="is_new" name="is_new">
                    新品上市
                </label>
            </div>
            <div class="form-actions">
                <button type="submit" class="save-btn">保存</button>
                <button type="button" class="cancel-btn" onclick="closeEditModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<style>
    /* 响应式设计 */
    @media (max-width: 1200px) {
        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
        }
    }

    @media (max-width: 768px) {
        .container {
            padding: 15px;
        }

        .page-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .page-title {
            font-size: 24px;
        }

        .filter-section {
            padding: 20px;
        }

        .filter-options {
            flex-direction: column;
            gap: 15px;
        }

        .filter-group {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
        }

        .product-card {
            border-radius: 15px;
        }

        .product-img-wrapper {
            height: 200px;
        }

        .product-info {
            padding: 20px;
        }

        .product-title {
            font-size: 14px;
            height: 40px;
        }

        .product-price {
            font-size: 20px;
        }

        .product-actions {
            flex-direction: column;
            gap: 10px;
        }

        .action-btn {
            width: 35px;
            height: 35px;
            font-size: 14px;
        }

        .buy-now {
            padding: 10px 15px;
            font-size: 13px;
        }

        .pagination {
            flex-wrap: wrap;
            gap: 5px;
        }

        .pagination a,
        .pagination span {
            padding: 8px 12px;
            font-size: 14px;
        }
    }

    @media (max-width: 480px) {
        .product-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 10px;
        }

        .filter-btn {
            padding: 6px 12px;
            font-size: 12px;
        }

        .page-title {
            font-size: 20px;
        }

        .page-subtitle {
            font-size: 14px;
        }
    }

.admin-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 2;
}

.edit-btn {
    background: rgba(228, 57, 60, 0.9);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 4px;
    cursor: pointer;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.modal-content {
    background: white;
    margin: 10% auto;
    padding: 20px;
    width: 80%;
    max-width: 600px;
    border-radius: 8px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 10px;
    font-size: 24px;
    cursor: pointer;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

.save-btn {
    background: #e4393c;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.cancel-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

#imagePreview {
    margin-top: 10px;
    max-width: 200px;
}

#imagePreview img {
    max-width: 100%;
    height: auto;
}
</style>

<script>
function openEditModal(button) {
    const productId = button.getAttribute('data-product-id');
    fetch(`/api/products/${productId}/`)
        .then(response => response.json())
        .then(product => {
            document.getElementById('productId').value = product.id;
            document.getElementById('name').value = product.name;
            document.getElementById('price').value = product.price;
            document.getElementById('description').value = product.description;
            document.getElementById('is_hot').checked = product.is_hot;
            document.getElementById('is_new').checked = product.is_new;
            
            const imagePreview = document.getElementById('imagePreview');
            if (product.main_image) {
                imagePreview.innerHTML = `<img src="${product.main_image}" alt="商品图片预览">`;
            } else {
                imagePreview.innerHTML = '';
            }
            
            document.getElementById('editModal').style.display = 'block';
        });
}

function closeEditModal() {
    document.getElementById('editModal').style.display = 'none';
}

document.querySelector('.close').onclick = closeEditModal;

window.onclick = function(event) {
    const modal = document.getElementById('editModal');
    if (event.target == modal) {
        closeEditModal();
    }
}

document.getElementById('main_image').onchange = function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('imagePreview').innerHTML = 
                `<img src="${e.target.result}" alt="图片预览">`;
        }
        reader.readAsDataURL(file);
    }
}

document.getElementById('editProductForm').onsubmit = function(e) {
    e.preventDefault();
    const formData = new FormData(this);
    const productId = formData.get('product_id');
    
    fetch(`/api/products/${productId}/update/`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('保存失败：' + data.error);
        }
    })
    .catch(error => {
        alert('保存失败：' + error);
    });
}

function toggleFavorite(btn) {
    // 检查用户是否登录
    {% if user.is_authenticated %}
    const productId = btn.getAttribute('data-product-id');
    const icon = btn.querySelector('i');
    const isCurrentlyFavorite = icon.classList.contains('fas');
    
    // 添加点击动画效果
    btn.classList.add('clicked');
    setTimeout(function() {
        btn.classList.remove('clicked');
    }, 300);
    
    // 禁用按钮避免重复点击
    btn.disabled = true;
    
    // 确定操作类型：添加或移除
    const action = isCurrentlyFavorite ? 'remove' : 'add';
    const url = action === 'add'
        ? '{% url "account:add_favorite" 0 %}'.replace('0', productId)
        : '{% url "account:remove_from_favorite" 0 %}'.replace('0', productId);
    
    // 发送AJAX请求
    fetch(url, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/x-www-form-urlencoded'
        },
        credentials: 'same-origin'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('网络响应不正常');
        }
        return response.json();
    })
    .then(data => {
        console.log('收藏响应:', data);
        
        // 更新图标状态
        if (data.status === 'success' || data.status === 'info') {
            if (action === 'add') {
                icon.classList.remove('far');
                icon.classList.add('fas');
                btn.classList.add('active');
                // 添加心跳动画
                icon.style.animation = 'none';
                setTimeout(function() {
                    icon.style.animation = 'heartBeat 0.5s ease-in-out';
                }, 10);
            } else {
                icon.classList.remove('fas');
                icon.classList.add('far');
                btn.classList.remove('active');
            }
        }
        
        // 显示提示信息
        showMessage(data.message, data.status === 'error' ? 'error' : 'success');
    })
    .catch(error => {
        console.error('收藏错误:', error);
        showMessage('操作失败，请稍后重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        btn.disabled = false;
    });
    {% else %}
    // 未登录时跳转到登录页面
    window.location.href = "{% url 'account:login' %}?next=" + window.location.pathname;
    {% endif %}
}

function addToCart(button) {
    // 检查用户是否登录
    {% if user.is_authenticated %}
    const productId = button.getAttribute('data-product-id');

    // 添加加载状态
    const originalHTML = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    button.disabled = true;

    // 发送AJAX请求
    fetch("{% url 'order:cart_add' %}", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: `product_id=${productId}&quantity=1`
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            // 成功提示
            button.innerHTML = '<i class="fas fa-check"></i>';
            button.style.background = '#28a745';
            button.style.borderColor = '#28a745';

            // 显示成功消息
            showMessage('商品已加入购物车！', 'success');

            // 2秒后恢复原状
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.disabled = false;
                button.style.background = '';
                button.style.borderColor = '';
            }, 2000);
        } else {
            throw new Error(data.msg || '添加失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        button.innerHTML = originalHTML;
        button.disabled = false;
        showMessage('添加失败，请重试', 'error');
    });
    {% else %}
    // 未登录时跳转到登录页面
    window.location.href = "{% url 'account:login' %}?next=" + window.location.pathname;
    {% endif %}
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast ${type}`;
    messageDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        ${message}
    `;
    
    document.body.appendChild(messageDiv);

    // 3秒后自动移除
    setTimeout(() => {
        messageDiv.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
            document.body.removeChild(messageDiv);
        }, 300);
    }, 3000);
}

// 添加动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);

// 调试函数 - 检查按钮功能
function debugButtons() {
    console.log('🔧 开始调试按钮功能...');

    // 检查所有收藏按钮
    const favoriteButtons = document.querySelectorAll('button[onclick*="toggleFavorite"]');
    console.log(`找到 ${favoriteButtons.length} 个收藏按钮`);

    favoriteButtons.forEach((btn, index) => {
        const productId = btn.getAttribute('data-product-id');
        if (!productId) {
            console.error(`❌ 收藏按钮 ${index + 1} 缺少 data-product-id 属性`, btn);
        } else {
            console.log(`✅ 收藏按钮 ${index + 1} 商品ID: ${productId}`);
        }
    });

    // 检查所有购物车按钮
    const cartButtons = document.querySelectorAll('button[onclick*="addToCart"]');
    console.log(`找到 ${cartButtons.length} 个购物车按钮`);

    cartButtons.forEach((btn, index) => {
        const productId = btn.getAttribute('data-product-id');
        if (!productId) {
            console.error(`❌ 购物车按钮 ${index + 1} 缺少 data-product-id 属性`, btn);
        } else {
            console.log(`✅ 购物车按钮 ${index + 1} 商品ID: ${productId}`);
        }
    });

    // 检查CSRF令牌
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
    if (csrfToken) {
        console.log('✅ CSRF令牌存在');
    } else {
        console.error('❌ CSRF令牌不存在');
    }

    console.log('🔧 按钮调试完成');
}

// 页面加载完成后自动调试
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(debugButtons, 1000);
});

// 手动调试函数
window.debugButtons = debugButtons;
</script>
{% endif %}
{% endblock %}