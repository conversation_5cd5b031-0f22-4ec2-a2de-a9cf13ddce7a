"""
定义商品相关模型
"""
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.urls import reverse
import os

# 缓存键前缀
CATEGORY_PRODUCT_COUNT_CACHE_KEY = 'category_product_count_{}'

def product_image_upload_path(instance, filename):
    """
    自定义商品图片上传路径
    支持更灵活的文件路径
    """
    # 获取文件扩展名
    ext = filename.split('.')[-1].lower()

    # 允许的图片格式
    allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp']

    if ext not in allowed_extensions:
        # 如果不是图片格式，默认为jpg
        ext = 'jpg'

    # 生成新的文件名：products/product_id_timestamp.ext
    import time
    timestamp = str(int(time.time()))

    if hasattr(instance, 'id') and instance.id:
        new_filename = f"product_{instance.id}_{timestamp}.{ext}"
    else:
        new_filename = f"product_new_{timestamp}.{ext}"

    return os.path.join('products', new_filename)

def category_icon_upload_path(instance, filename):
    """
    自定义分类图标上传路径
    """
    ext = filename.split('.')[-1].lower()
    allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg']

    if ext not in allowed_extensions:
        ext = 'png'

    import time
    timestamp = str(int(time.time()))

    if hasattr(instance, 'id') and instance.id:
        new_filename = f"category_{instance.id}_{timestamp}.{ext}"
    else:
        new_filename = f"category_new_{timestamp}.{ext}"

    return os.path.join('categories', new_filename)

class Category(models.Model):
    """商品分类模型"""
    name = models.CharField(_('分类名称'), max_length=100, db_index=True)
    parent = models.ForeignKey('self', verbose_name=_('父级分类'), 
                             on_delete=models.CASCADE, null=True, blank=True, 
                             related_name='children')
    icon = models.ImageField(_('分类图标'), upload_to=category_icon_upload_path, null=True, blank=True)
    sort_order = models.IntegerField(_('排序'), default=0)
    is_active = models.BooleanField(_('是否激活'), default=True, db_index=True)
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_time = models.DateTimeField(_('更新时间'), auto_now=True)
    description = models.TextField(_('分类描述'), null=True, blank=True)

    class Meta:
        verbose_name = _('商品分类')
        verbose_name_plural = _('商品分类')
        ordering = ['sort_order', 'id']
        app_label = 'goods'  # 修改app_label

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('goods:category', args=[self.id])
    
    def product_count(self):
        """获取该分类下的商品数量"""
        return self.products.count()
    
    product_count.short_description = '商品数量'

class Product(models.Model):
    """商品模型"""
    category = models.ForeignKey(Category, verbose_name=_('商品分类'), 
                               on_delete=models.CASCADE, related_name='products')
    name = models.CharField(_('商品名称'), max_length=200, db_index=True)
    description = models.TextField(_('商品描述'))
    price = models.DecimalField(_('价格'), max_digits=10, decimal_places=2, default=0)
    main_image = models.ImageField(upload_to='products/', default='products/default.png', verbose_name=_('主图'))
    stock = models.IntegerField(_('库存'), default=0)
    sales = models.IntegerField(_('销量'), default=0)
    is_hot = models.BooleanField(_('是否热门'), default=False, db_index=True)
    is_new = models.BooleanField(_('是否新品'), default=True, db_index=True)
    is_active = models.BooleanField(_('是否上架'), default=True, db_index=True)
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_time = models.DateTimeField(_('更新时间'), auto_now=True)

    class Meta:
        verbose_name = _('商品')
        verbose_name_plural = _('商品')
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['name', 'price']),
            models.Index(fields=['is_hot', 'is_new', 'is_active']),
            models.Index(fields=['created_time']),  # 添加创建时间索引
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('goods:detail', args=[self.id])

    @property
    def image(self):
        """兼容性方法，确保模板中使用image字段的代码依然能正常工作"""
        return self.main_image

    def image_preview(self):
        """商品图片预览方法，用于后台管理"""
        from django.utils.html import format_html
        if self.main_image:
            return format_html('<img src="{}" width="100" height="100" />', self.main_image.url)
        return "无图片"
    image_preview.short_description = '商品图片预览'

class ProductImage(models.Model):
    """商品图片模型"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images', verbose_name=_('商品'))
    image = models.ImageField(_('图片'), upload_to='products/')
    alt_text = models.CharField(_('图片描述'), max_length=200, blank=True, default='')
    sort_order = models.IntegerField(_('排序'), default=0)
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('商品图片')
        verbose_name_plural = _('商品图片')
        ordering = ['sort_order']
        indexes = [
            models.Index(fields=['product', 'sort_order']),
        ]

    def __str__(self):
        return f"{self.product.name}的图片" 