{% extends "base.html" %}
{% load static %}

{% block title %}管理员仪表板{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mb-3">管理员仪表板</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'goods:index' %}">首页</a></li>
                    <li class="breadcrumb-item active">管理员仪表板</li>
                </ol>
            </nav>
        </div>
    </div>

    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white mb-3">
                <div class="card-body">
                    <h5 class="card-title">商品总数</h5>
                    <p class="card-text display-4">{{ products_count }}</p>
                    <a href="{% url 'account:product_list' %}" class="btn btn-light">管理商品</a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white mb-3">
                <div class="card-body">
                    <h5 class="card-title">分类总数</h5>
                    <p class="card-text display-4">{{ categories_count }}</p>
                    <a href="#" class="btn btn-light">管理分类</a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white mb-3">
                <div class="card-body">
                    <h5 class="card-title">用户总数</h5>
                    <p class="card-text display-4">{{ users_count }}</p>
                    <a href="#" class="btn btn-light">管理用户</a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">快捷操作</h5>
                </div>
                <div class="card-body">
                    <div class="list-group">
                        <a href="{% url 'account:product_add' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            添加新商品
                            <span class="badge bg-primary rounded-pill">
                                <i class="fas fa-plus"></i>
                            </span>
                        </a>
                        <a href="{% url 'account:product_list' %}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            商品列表
                            <span class="badge bg-primary rounded-pill">
                                <i class="fas fa-list"></i>
                            </span>
                        </a>
                        <a href="#" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            订单管理
                            <span class="badge bg-primary rounded-pill">
                                <i class="fas fa-shopping-cart"></i>
                            </span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">系统信息</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Django版本
                            <span class="badge bg-secondary rounded-pill">3.2</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Python版本
                            <span class="badge bg-secondary rounded-pill">3.9</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            数据库
                            <span class="badge bg-secondary rounded-pill">SQLite</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 