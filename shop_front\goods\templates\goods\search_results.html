{% extends 'base.html' %}
{% load static %}

{% block title %}
{% if query %}
搜索"{{ query }}" - MARS BUY
{% else %}
商品搜索 - MARS BUY
{% endif %}
{% endblock %}

{% block extra_css %}
<style>
.search-header {
    background: linear-gradient(135deg, #ff0000 0%, #cc0000 100%);
    color: white;
    padding: 20px 0;
    margin-bottom: 30px;
}

.search-info {
    text-align: center;
}

.search-info h1 {
    font-size: 2rem;
    margin-bottom: 10px;
}

.search-stats {
    font-size: 1.1rem;
    opacity: 0.9;
}

.search-form {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
}

.search-input {
    border: 2px solid #ff0000;
    border-radius: 25px;
    padding: 12px 20px;
    font-size: 16px;
    width: 100%;
    outline: none;
}

.search-input:focus {
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.3);
}

.search-btn {
    background: linear-gradient(135deg, #ff0000, #cc0000);
    color: white;
    border: none;
    border-radius: 25px;
    padding: 12px 30px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 0, 0, 0.3);
}

.filter-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.product-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.product-info {
    padding: 15px;
}

.product-name {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.product-price {
    font-size: 1.3rem;
    color: #ff0000;
    font-weight: bold;
    margin-bottom: 10px;
}

.product-category {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 10px;
}

.no-results {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-results i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #ddd;
}

.pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.pagination a, .pagination span {
    padding: 10px 15px;
    margin: 0 5px;
    border-radius: 5px;
    text-decoration: none;
    color: #333;
    border: 1px solid #ddd;
}

.pagination .current {
    background: #ff0000;
    color: white;
    border-color: #ff0000;
}

.pagination a:hover {
    background: #f8f9fa;
}
</style>
{% endblock %}

{% block content %}
<div class="search-header">
    <div class="container">
        <div class="search-info">
            {% if query %}
                <h1>搜索结果</h1>
                <div class="search-stats">
                    找到 <strong>{{ total_count }}</strong> 个关于 "<strong>{{ query }}</strong>" 的商品
                </div>
            {% else %}
                <h1>商品搜索</h1>
                <div class="search-stats">探索 MARS BUY 的所有商品</div>
            {% endif %}
        </div>
    </div>
</div>

<div class="container">
    <!-- 搜索表单 -->
    <div class="search-form">
        <form method="get" action="{% url 'goods:search' %}">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <input type="text" name="q" value="{{ query }}" placeholder="搜索商品名称、描述或分类..." class="search-input">
                </div>
                <div class="col-md-3">
                    <select name="category" class="form-control">
                        <option value="">所有分类</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if current_category.id == category.id %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="sort" class="form-control">
                        <option value="-id" {% if sort == '-id' %}selected{% endif %}>最新</option>
                        <option value="price" {% if sort == 'price' %}selected{% endif %}>价格从低到高</option>
                        <option value="-price" {% if sort == '-price' %}selected{% endif %}>价格从高到低</option>
                        <option value="-sales_count" {% if sort == '-sales_count' %}selected{% endif %}>销量最高</option>
                        <option value="-favorite_count" {% if sort == '-favorite_count' %}selected{% endif %}>最受欢迎</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- 搜索结果 -->
    {% if products %}
        <div class="product-grid">
            {% for product in products %}
                <div class="product-card">
                    <a href="{% url 'goods:detail' product.id %}">
                        {% if product.main_image %}
                            <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="product-image">
                        {% else %}
                            <img src="{% static 'images/default-product.jpg' %}" alt="{{ product.name }}" class="product-image">
                        {% endif %}
                    </a>
                    <div class="product-info">
                        <div class="product-name">
                            <a href="{% url 'goods:detail' product.id %}">{{ product.name }}</a>
                        </div>
                        <div class="product-price">¥{{ product.price }}</div>
                        <div class="product-category">
                            <i class="fas fa-tag"></i> {{ product.category.name }}
                        </div>
                        {% if product.is_hot %}
                            <span class="badge badge-danger">热门</span>
                        {% endif %}
                        {% if product.is_new %}
                            <span class="badge badge-success">新品</span>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if products.has_other_pages %}
            <div class="pagination">
                {% if products.has_previous %}
                    <a href="?q={{ query }}&category={{ request.GET.category }}&sort={{ sort }}&page={{ products.previous_page_number }}">&laquo; 上一页</a>
                {% endif %}

                {% for num in products.paginator.page_range %}
                    {% if products.number == num %}
                        <span class="current">{{ num }}</span>
                    {% elif num > products.number|add:'-3' and num < products.number|add:'3' %}
                        <a href="?q={{ query }}&category={{ request.GET.category }}&sort={{ sort }}&page={{ num }}">{{ num }}</a>
                    {% endif %}
                {% endfor %}

                {% if products.has_next %}
                    <a href="?q={{ query }}&category={{ request.GET.category }}&sort={{ sort }}&page={{ products.next_page_number }}">下一页 &raquo;</a>
                {% endif %}
            </div>
        {% endif %}
    {% else %}
        <div class="no-results">
            <i class="fas fa-search"></i>
            {% if search_performed %}
                <h3>没有找到相关商品</h3>
                <p>尝试使用其他关键词或浏览所有商品</p>
                <a href="{% url 'goods:list' %}" class="btn btn-primary">浏览所有商品</a>
            {% else %}
                <h3>开始搜索</h3>
                <p>在上方输入关键词搜索商品</p>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}
