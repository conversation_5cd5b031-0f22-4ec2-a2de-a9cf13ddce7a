#!/usr/bin/env python3
"""
测试Django配置和服务器启动
"""

import os
import sys
import django
from django.core.management import execute_from_command_line

def test_django_setup():
    """测试Django设置"""
    print("🔧 测试Django配置...")
    
    # 设置Django设置模块
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
    
    try:
        django.setup()
        print("✅ Django设置成功")
        return True
    except Exception as e:
        print(f"❌ Django设置失败: {e}")
        return False

def test_urls():
    """测试URL配置"""
    print("\n🌐 测试URL配置...")
    
    try:
        from django.urls import reverse
        
        # 测试基本URL
        test_urls = [
            ('home:index', [], '首页'),
            ('account:test_buttons', [], '测试按钮页面'),
            ('account:add_favorite', [1], '添加收藏'),
            ('users:login', [], '用户登录'),
        ]
        
        for url_name, args, description in test_urls:
            try:
                url = reverse(url_name, args=args)
                print(f"✅ {description}: {url}")
            except Exception as e:
                print(f"❌ {description}: {e}")
                
        return True
    except Exception as e:
        print(f"❌ URL测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 Django配置测试")
    print("=" * 50)
    
    # 测试Django设置
    if not test_django_setup():
        return
    
    # 测试URL配置
    test_urls()
    
    print("\n🚀 启动开发服务器...")
    print("访问: http://127.0.0.1:8001/account/test-buttons/")
    print("按 Ctrl+C 停止服务器")
    
    # 启动服务器
    try:
        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8001'])
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

if __name__ == '__main__':
    main()
