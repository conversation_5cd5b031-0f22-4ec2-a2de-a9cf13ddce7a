from django.urls import path
from . import views

app_name = 'admin_panel'

urlpatterns = [
    # 登录相关
    path('login/', views.admin_login, name='login'),
    path('register/', views.admin_register, name='register'),
    path('logout/', views.admin_logout, name='logout'),
    
    # 管理员首页
    path('', views.dashboard, name='dashboard'),
    path('dashboard/', views.dashboard, name='dashboard'),
    
    # 数据统计
    path('statistics/', views.statistics, name='statistics'),
    path('sales-chart/', views.sales_chart, name='sales_chart'),
    
    # 系统设置
    path('settings/', views.system_settings, name='settings'),
    path('settings/update/', views.update_settings, name='update_settings'),
    
    # 操作日志
    path('logs/', views.admin_logs, name='logs'),

    # 个人中心
    path('profile/', views.admin_profile, name='admin_profile'),
    path('profile/update/', views.update_admin_profile, name='update_admin_profile'),
    path('profile/change-password/', views.change_admin_password, name='change_admin_password'),
    path('profile/upload-avatar/', views.upload_admin_avatar, name='upload_admin_avatar'),
    path('profile/logout/', views.admin_profile_logout, name='admin_profile_logout'),
]
