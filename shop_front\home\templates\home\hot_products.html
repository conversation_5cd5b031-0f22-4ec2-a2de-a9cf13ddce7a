{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - MARS BUY{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4">🔥 热门商品</h1>
        </div>
    </div>
    
    <div class="row">
        {% for product in hot_products %}
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="card h-100">
                {% if product.main_image %}
                    <img src="{{ product.main_image.url }}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                {% else %}
                    <img src="{% static 'images/default-product.jpg' %}" class="card-img-top" alt="{{ product.name }}" style="height: 200px; object-fit: cover;">
                {% endif %}
                
                <div class="card-body d-flex flex-column">
                    <h5 class="card-title">{{ product.name }}</h5>
                    <p class="card-text flex-grow-1">{{ product.description|truncatechars:100 }}</p>
                    <div class="mt-auto">
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="text-danger h5 mb-0">¥{{ product.price }}</span>
                            <small class="text-muted">销量: {{ product.sales_count }}</small>
                        </div>
                        <a href="{% url 'goods:product_detail' product.id %}" class="btn btn-primary btn-sm mt-2 w-100">查看详情</a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12 text-center">
            <p class="text-muted">暂无热门商品</p>
        </div>
        {% endfor %}
    </div>
</div>
{% endblock %}