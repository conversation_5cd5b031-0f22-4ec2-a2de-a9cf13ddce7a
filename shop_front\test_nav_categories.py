#!/usr/bin/env python3
"""
测试导航栏分类链接功能
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.urls import reverse
from django.test import Client
from goods.models import Category, Product

def test_navigation_categories():
    """测试导航栏中的分类是否存在"""
    print("🧪 测试导航栏分类链接")
    print("=" * 60)
    
    # 检查导航栏中配置的分类ID
    nav_categories = [
        (2, '数码'),
        (26, '家居'),
        (39, '食品')
    ]
    
    print("\n1. 检查分类是否存在...")
    for category_id, expected_name in nav_categories:
        try:
            category = Category.objects.get(id=category_id, is_active=True)
            print(f"   ✅ 分类 {category_id}: {category.name}")
            
            # 检查该分类下的商品数量
            if category.children.exists():
                # 父分类，统计子分类商品
                child_ids = list(category.children.filter(is_active=True).values_list('id', flat=True))
                product_count = Product.objects.filter(
                    category_id__in=child_ids, 
                    is_active=True
                ).count()
                print(f"      📊 子分类数: {category.children.filter(is_active=True).count()}")
            else:
                # 子分类，直接统计商品
                product_count = Product.objects.filter(
                    category=category, 
                    is_active=True
                ).count()
            
            print(f"      📦 商品数量: {product_count}")
            
        except Category.DoesNotExist:
            print(f"   ❌ 分类 {category_id} ({expected_name}) 不存在或未激活")
            
            # 查找相似名称的分类
            similar_categories = Category.objects.filter(
                name__icontains=expected_name, 
                is_active=True
            )
            if similar_categories.exists():
                print(f"      💡 找到相似分类:")
                for cat in similar_categories:
                    print(f"         - {cat.name} (ID: {cat.id})")

def test_category_urls():
    """测试分类URL访问"""
    print("\n2. 测试分类URL访问...")
    
    client = Client()
    nav_categories = [2, 26, 39]
    
    for category_id in nav_categories:
        try:
            url = reverse('goods:category', kwargs={'category_id': category_id})
            response = client.get(url)
            
            if response.status_code == 200:
                print(f"   ✅ 分类 {category_id}: {url} (状态码: {response.status_code})")
            else:
                print(f"   ❌ 分类 {category_id}: {url} (状态码: {response.status_code})")
                
        except Exception as e:
            print(f"   ❌ 分类 {category_id}: 错误 - {e}")

def suggest_category_fixes():
    """建议分类修复方案"""
    print("\n3. 分类修复建议...")
    
    # 获取所有激活的分类
    categories = Category.objects.filter(is_active=True).order_by('id')
    
    print(f"   📋 当前激活分类总数: {categories.count()}")
    
    # 查找可能的替代分类
    category_suggestions = {
        '数码': ['数码', '手机', '电脑', '电子'],
        '家居': ['家居', '家具', '生活', '日用'],
        '食品': ['食品', '食物', '饮料', '零食']
    }
    
    for nav_name, keywords in category_suggestions.items():
        print(f"\n   🔍 查找 '{nav_name}' 相关分类:")
        found_categories = []
        
        for keyword in keywords:
            matches = categories.filter(name__icontains=keyword)
            for match in matches:
                if match not in found_categories:
                    found_categories.append(match)
        
        if found_categories:
            print(f"      找到 {len(found_categories)} 个相关分类:")
            for cat in found_categories[:5]:  # 只显示前5个
                product_count = cat.products.filter(is_active=True).count()
                print(f"         - {cat.name} (ID: {cat.id}, 商品: {product_count})")
        else:
            print(f"      ❌ 未找到相关分类")

def create_missing_categories():
    """创建缺失的分类"""
    print("\n4. 创建缺失的分类...")
    
    # 检查并创建缺失的分类
    required_categories = [
        (2, '数码科技'),
        (26, '家居日用'),
        (39, '食品生鲜')
    ]
    
    for category_id, category_name in required_categories:
        try:
            category = Category.objects.get(id=category_id)
            print(f"   ✅ 分类 {category_id} 已存在: {category.name}")
        except Category.DoesNotExist:
            # 创建分类
            try:
                category = Category.objects.create(
                    id=category_id,
                    name=category_name,
                    is_active=True,
                    sort_order=category_id
                )
                print(f"   ✅ 创建分类 {category_id}: {category_name}")
            except Exception as e:
                print(f"   ❌ 创建分类 {category_id} 失败: {e}")

def main():
    """主函数"""
    print("🔗 导航栏分类链接测试")
    print("=" * 60)
    
    # 测试现有分类
    test_navigation_categories()
    
    # 测试URL访问
    test_category_urls()
    
    # 建议修复方案
    suggest_category_fixes()
    
    # 创建缺失分类
    create_missing_categories()
    
    print("\n✨ 测试完成！")
    print("\n📋 总结:")
    print("1. 导航栏中的'数码'、'家居'、'食品'按钮已配置链接")
    print("2. 链接格式: /goods/category/<category_id>/")
    print("3. 如果分类不存在，需要在后台创建对应的分类")
    
    print("\n🌐 访问链接:")
    print("   首页: http://127.0.0.1:8001/")
    print("   数码分类: http://127.0.0.1:8001/goods/category/2/")
    print("   家居分类: http://127.0.0.1:8001/goods/category/26/")
    print("   食品分类: http://127.0.0.1:8001/goods/category/39/")

if __name__ == '__main__':
    main()
