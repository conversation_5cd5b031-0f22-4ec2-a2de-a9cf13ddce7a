# Generated by Django 4.2.22 on 2025-06-07 17:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Address',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('receiver', models.CharField(max_length=50, verbose_name='收货人')),
                ('phone', models.CharField(max_length=11, verbose_name='手机号码')),
                ('province', models.CharField(max_length=50, verbose_name='省份')),
                ('city', models.CharField(max_length=50, verbose_name='城市')),
                ('district', models.CharField(max_length=50, verbose_name='区县')),
                ('address', models.Char<PERSON><PERSON>(max_length=200, verbose_name='详细地址')),
                ('is_default', models.BooleanField(default=False, verbose_name='是否默认')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_time', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='addresses', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '收货地址',
                'verbose_name_plural': '收货地址',
                'ordering': ['-is_default', '-updated_time'],
            },
        ),
    ]
