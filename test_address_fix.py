#!/usr/bin/env python3
"""
测试地址修复是否有效
"""

import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def test_address_fix():
    base_url = "http://127.0.0.1:8001"
    session = requests.Session()
    
    print("🧪 测试地址修复是否有效...")
    print("=" * 50)
    
    # 1. 登录
    print("1. 用户登录...")
    try:
        login_url = urljoin(base_url, "/users/login/")
        response = session.get(login_url)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
            
            if csrf_input:
                csrf_token = csrf_input.get('value')
                
                login_data = {
                    'username': 'testuser',
                    'password': '123456',
                    'user_type': 'normal',
                    'csrfmiddlewaretoken': csrf_token
                }
                
                login_response = session.post(login_url, data=login_data)
                if login_response.status_code == 302:
                    print("   ✅ 登录成功")
                else:
                    print("   ❌ 登录失败")
                    return
            else:
                print("   ❌ 无法获取CSRF令牌")
                return
        else:
            print(f"   ❌ 无法访问登录页面: {response.status_code}")
            return
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return
    
    # 2. 测试获取地址列表
    print("\n2. 测试获取地址列表...")
    try:
        response = session.get(urljoin(base_url, "/users/api/addresses/"))
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取地址列表，共 {len(data)} 个地址")
        else:
            print(f"   ❌ 获取地址列表失败")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"   ❌ 获取地址列表异常: {e}")
    
    # 3. 测试添加地址
    print("\n3. 测试添加地址...")
    try:
        # 获取CSRF令牌
        csrf_token = None
        for cookie in session.cookies:
            if cookie.name == 'csrftoken':
                csrf_token = cookie.value
                break
        
        if csrf_token:
            address_data = {
                'receiver': '测试用户',
                'phone': '13800138000',
                'province': '北京市',
                'city': '北京市',
                'district': '朝阳区',
                'detail': '测试地址123号',
                'is_default': True
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrf_token
            }
            
            response = session.post(
                urljoin(base_url, "/users/api/addresses/"),
                data=json.dumps(address_data),
                headers=headers
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应: {response.text}")
            
            if response.status_code in [200, 201]:
                print("   ✅ 添加地址成功")
            else:
                print(f"   ❌ 添加地址失败")
        else:
            print("   ❌ 无法获取CSRF令牌")
    except Exception as e:
        print(f"   ❌ 添加地址异常: {e}")
    
    # 4. 再次获取地址列表验证
    print("\n4. 验证地址添加结果...")
    try:
        response = session.get(urljoin(base_url, "/users/api/addresses/"))
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 当前地址数量: {len(data)}")
            if data:
                print("   最新地址:")
                for addr in data:
                    print(f"     - {addr.get('receiver')} {addr.get('phone')}")
        else:
            print(f"   ❌ 验证失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 验证异常: {e}")
    
    print("\n🎉 测试完成!")

if __name__ == "__main__":
    test_address_fix()
