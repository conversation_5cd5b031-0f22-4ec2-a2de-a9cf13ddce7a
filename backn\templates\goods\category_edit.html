{% extends 'base.html' %}

{% block title %}编辑分类 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}编辑分类{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <a href="{% url 'goods:category_list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
    <a href="{% url 'goods:list' %}?category={{ category.id }}" class="btn btn-outline-info">
        <i class="fas fa-eye me-1"></i>查看商品
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>编辑分类信息
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">分类名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ category.name }}" required>
                        <div class="form-text">请输入分类名称，不能为空</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="parent" class="form-label">父分类</label>
                        <select class="form-select" id="parent" name="parent">
                            <option value="">顶级分类</option>
                            {% for parent_category in parent_categories %}
                            <option value="{{ parent_category.id }}" 
                                    {% if category.parent and category.parent.id == parent_category.id %}selected{% endif %}>
                                {{ parent_category.name }}
                            </option>
                            {% endfor %}
                        </select>
                        <div class="form-text">选择父分类，留空则为顶级分类</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="icon" class="form-label">分类图标</label>
                        {% if category.icon %}
                        <div class="mb-2">
                            <img src="{{ category.icon.url }}" alt="{{ category.name }}" 
                                 width="64" height="64" class="rounded border">
                            <small class="text-muted ms-2">当前图标</small>
                        </div>
                        {% endif %}
                        <input type="file" class="form-control" id="icon" name="icon" accept="image/*">
                        <div class="form-text">支持 JPG、PNG、GIF 格式，建议尺寸 64x64 像素</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">排序</label>
                        <input type="number" class="form-control" id="sort_order" name="sort_order" 
                               value="{{ category.sort_order }}" min="0">
                        <div class="form-text">数字越小排序越靠前</div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                               {% if category.is_active %}checked{% endif %}>
                        <label class="form-check-label" for="is_active">
                            启用分类
                        </label>
                        <div class="form-text">禁用后该分类将不会在前台显示</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存
                        </button>
                        <a href="{% url 'goods:category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>分类统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-primary mb-1">{{ category.get_products_count }}</h4>
                            <small class="text-muted">商品数量</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success mb-1">{{ category.children.count }}</h4>
                        <small class="text-muted">子分类</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>操作说明
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        分类名称为必填项
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        不能将自己设置为父分类
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        排序数字越小越靠前
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        禁用的分类不会在前台显示
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        有商品或子分类时不能删除
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
