{% extends "base.html" %}
{% load static %}

{% block title %}编辑商品{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mb-3">编辑商品</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'goods:index' %}">首页</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'account:admin_dashboard' %}">管理员仪表板</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'account:product_list' %}">商品管理</a></li>
                    <li class="breadcrumb-item active">编辑商品</li>
                </ol>
            </nav>
        </div>
    </div>

    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">编辑商品信息</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'account:product_edit' product.id %}" enctype="multipart/form-data">
                        {% csrf_token %}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">商品名称</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                            </div>
                            <div class="col-md-6">
                                <label for="category" class="form-label">商品分类</label>
                                <select class="form-select" id="category" name="category" required>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" {% if category.id == product.category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="price" class="form-label">价格</label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="price" name="price" value="{{ product.price }}" step="0.01" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="stock" class="form-label">库存</label>
                                <input type="number" class="form-control" id="stock" name="stock" value="{{ product.stock }}" min="0" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">商品描述</label>
                            <textarea class="form-control" id="description" name="description" rows="5" required>{{ product.description }}</textarea>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_hot" name="is_hot" {% if product.is_hot %}checked{% endif %}>
                                    <label class="form-check-label" for="is_hot">热门商品</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_new" name="is_new" {% if product.is_new %}checked{% endif %}>
                                    <label class="form-check-label" for="is_new">新品上市</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {% if product.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">上架状态</label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-4">
                            <label for="main_image" class="form-label">商品主图</label>
                            <input class="form-control" type="file" id="main_image" name="main_image" accept="image/*">
                            <div class="form-text">如果不上传新图片，将保留原有图片</div>
                        </div>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'account:product_list' %}" class="btn btn-outline-secondary me-md-2">取消</a>
                            <button type="submit" class="btn btn-primary">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">当前商品图片</h5>
                </div>
                <div class="card-body text-center">
                    <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="img-fluid mb-3" style="max-height: 300px;">
                    <h5>{{ product.name }}</h5>
                    <p class="text-muted">{{ product.category.name }}</p>
                    <p class="fs-4 text-danger">¥{{ product.price }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 