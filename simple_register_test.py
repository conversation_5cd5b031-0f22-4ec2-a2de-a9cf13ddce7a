#!/usr/bin/env python3
"""
简单的管理员注册测试
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop/backn')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backn.settings')
django.setup()

from users.models import User

def test_direct_user_creation():
    """直接测试用户创建"""
    print("🧪 测试直接创建管理员用户...")
    
    # 清理可能存在的测试用户
    test_username = 'testadmin999'
    if User.objects.filter(username=test_username).exists():
        User.objects.filter(username=test_username).delete()
        print(f"✅ 清理已存在的测试用户: {test_username}")
    
    try:
        # 创建管理员用户
        user = User.objects.create_user(
            username=test_username,
            email='<EMAIL>',
            password='TestPassword123!',
            phone='13800138999',
            is_staff=True,
            is_active=True
        )
        
        print(f"✅ 管理员用户创建成功!")
        print(f"   用户名: {user.username}")
        print(f"   邮箱: {user.email}")
        print(f"   手机: {user.phone}")
        print(f"   是否管理员: {'是' if user.is_staff else '否'}")
        print(f"   是否激活: {'是' if user.is_active else '否'}")
        print(f"   是否超级管理员: {'是' if user.is_superuser else '否'}")
        
        # 测试登录
        from django.contrib.auth import authenticate
        auth_user = authenticate(username=test_username, password='TestPassword123!')
        if auth_user:
            print(f"✅ 用户认证成功")
        else:
            print(f"❌ 用户认证失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建用户失败: {e}")
        return False

def check_existing_admins():
    """检查现有管理员"""
    print("\n🔍 检查现有管理员账户...")
    
    admins = User.objects.filter(is_staff=True)
    print(f"📊 管理员数量: {admins.count()}")
    
    for admin in admins:
        print(f"👤 {admin.username} - {admin.email} - {'超级管理员' if admin.is_superuser else '普通管理员'}")

def show_registration_urls():
    """显示注册相关URL"""
    print("\n🌐 管理员注册相关URL:")
    print("注册页面: http://127.0.0.1:8003/register/")
    print("登录页面: http://127.0.0.1:8003/login/")
    print("后台首页: http://127.0.0.1:8003/dashboard/")

if __name__ == "__main__":
    print("🚀 开始测试管理员注册功能...")
    print("=" * 50)
    
    # 检查现有管理员
    check_existing_admins()
    
    # 测试直接创建用户
    success = test_direct_user_creation()
    
    # 显示URL信息
    show_registration_urls()
    
    print("\n🎯 测试结果:")
    print("=" * 50)
    if success:
        print("✅ 管理员用户创建功能正常")
        print("✅ 您可以通过注册页面注册新的管理员")
        print("✅ 注册后的用户将自动获得管理员权限")
    else:
        print("❌ 管理员用户创建功能异常")
        print("❌ 请检查数据库连接和模型配置")
    
    print("\n📝 注册要求:")
    print("- 用户名: 3-20个字符，字母数字下划线")
    print("- 密码: 至少8个字符，建议包含大小写字母数字特殊字符")
    print("- 邮箱: 有效的邮箱地址")
    print("- 手机: 可选，11位手机号")
    print("- 头像: 可选，JPG/PNG/GIF格式，最大5MB")
