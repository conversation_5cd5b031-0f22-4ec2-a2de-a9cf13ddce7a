from rest_framework import serializers
from .models import Address

class AddressSerializer(serializers.ModelSerializer):
    """收货地址序列化器"""
    detail = serializers.CharField(source='address')  # 添加字段别名

    class Meta:
        model = Address
        fields = ['id', 'receiver', 'phone', 'province', 'city', 'district', 'detail', 'is_default']
        
    def validate_phone(self, value):
        """验证手机号码格式"""
        if not value.isdigit() or len(value) != 11 or not value.startswith('1'):
            raise serializers.ValidationError('请输入正确的手机号码')
        return value
        
    def validate(self, data):
        """验证地址数据"""
        if not data.get('receiver'):
            raise serializers.ValidationError({'receiver': '收货人不能为空'})

        if not data.get('province') or not data.get('city') or not data.get('district'):
            raise serializers.ValidationError('请选择完整的地址信息')

        # 由于字段映射，这里应该检查 'address' 而不是 'detail'
        if not data.get('address'):
            raise serializers.ValidationError({'detail': '详细地址不能为空'})

        return data
        
    def create(self, validated_data):
        """创建地址时，如果是默认地址，需要将其他地址设置为非默认"""
        if validated_data.get('is_default'):
            Address.objects.filter(user=self.context['request'].user, is_default=True).update(is_default=False)
        validated_data['user'] = self.context['request'].user
        return super().create(validated_data)
        
    def update(self, instance, validated_data):
        """更新地址时，如果设置为默认地址，需要将其他地址设置为非默认"""
        if validated_data.get('is_default'):
            Address.objects.filter(user=instance.user, is_default=True).exclude(id=instance.id).update(is_default=False)
        return super().update(instance, validated_data) 