#!/usr/bin/env python3
"""
增强分类管理功能
为管理员提供更灵活的分类管理能力
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop/backn')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backn.settings')
django.setup()

from goods.models import Category, Product
from django.db import transaction
from django.db import models

def add_custom_subcategories():
    """添加自定义子分类示例"""
    print("🏗️ 添加自定义子分类示例...")
    
    # 自定义子分类数据
    custom_subcategories = {
        '时尚穿搭': [
            '内衣睡衣', '鞋靴', '箱包配饰', '运动装', '正装'
        ],
        '美妆护肤': [
            '香水', '美容工具', '男士护理', '孕妇专用'
        ],
        '家居日用': [
            '清洁用品', '家装建材', '园艺用品', '宠物用品'
        ],
        '数码科技': [
            '智能穿戴', '游戏设备', '网络设备', '数码配件'
        ],
        '家用电器': [
            '个护健康', '影音娱乐', '智能家居'
        ],
        '食品生鲜': [
            '新鲜水果', '蔬菜蛋品', '肉禽水产', '冷冻食品', '进口食品'
        ],
        '母婴亲子': [
            '孕妇用品', '婴儿服装', '儿童安全座椅', '奶粉辅食'
        ],
        '运动户外': [
            '户外装备', '游泳用品', '瑜伽健身', '骑行用品'
        ],
        '汽车相关': [
            '汽车电子', '轮胎轮毂', '汽车工具', '摩托车用品'
        ],
        '生活服务': [
            '教育培训', '旅游出行', '金融保险', '维修服务'
        ]
    }
    
    added_count = 0
    
    for parent_name, subcategory_names in custom_subcategories.items():
        try:
            parent_category = Category.objects.get(name=parent_name, parent=None)
            
            for subcategory_name in subcategory_names:
                # 检查子分类是否已存在
                if not Category.objects.filter(name=subcategory_name, parent=parent_category).exists():
                    # 获取当前最大排序号
                    max_sort = Category.objects.filter(parent=parent_category).aggregate(
                        max_sort=models.Max('sort_order')
                    )['max_sort'] or 0
                    
                    Category.objects.create(
                        name=subcategory_name,
                        parent=parent_category,
                        is_active=True,
                        sort_order=max_sort + 1
                    )
                    
                    print(f"  ✅ 添加子分类: {parent_name} > {subcategory_name}")
                    added_count += 1
                else:
                    print(f"  📁 子分类已存在: {parent_name} > {subcategory_name}")
                    
        except Category.DoesNotExist:
            print(f"❌ 父分类不存在: {parent_name}")
    
    print(f"✅ 共添加了 {added_count} 个自定义子分类")
    return added_count

def optimize_category_structure():
    """优化分类结构"""
    print(f"\n🔧 优化分类结构...")
    
    # 重新排序分类
    parent_categories = Category.objects.filter(parent=None).order_by('name')
    for i, parent in enumerate(parent_categories, 1):
        parent.sort_order = i * 10  # 留出空间给后续插入
        parent.save()
        
        # 重新排序子分类
        child_categories = parent.children.all().order_by('name')
        for j, child in enumerate(child_categories, 1):
            child.sort_order = j
            child.save()
    
    print(f"✅ 优化了分类排序")

def create_category_statistics():
    """创建分类统计信息"""
    print(f"\n📊 生成分类统计信息...")
    
    stats = {}
    
    parent_categories = Category.objects.filter(parent=None)
    for parent in parent_categories:
        parent_stats = {
            'name': parent.name,
            'total_subcategories': parent.children.count(),
            'active_subcategories': parent.children.filter(is_active=True).count(),
            'total_products': Product.objects.filter(category__parent=parent).count(),
            'active_products': Product.objects.filter(category__parent=parent, is_active=True).count(),
            'subcategories': []
        }
        
        for child in parent.children.all():
            child_stats = {
                'name': child.name,
                'products_count': child.products.count(),
                'active_products_count': child.products.filter(is_active=True).count(),
                'is_active': child.is_active
            }
            parent_stats['subcategories'].append(child_stats)
        
        stats[parent.name] = parent_stats
    
    return stats

def show_category_management_guide():
    """显示分类管理指南"""
    print(f"\n📋 分类管理指南:")
    print("=" * 60)
    
    print("1. 添加新的主分类:")
    print("   - 访问后台: http://127.0.0.1:8003/goods/categories/add/")
    print("   - 填写分类名称，不选择父分类")
    print("   - 设置排序号和状态")
    
    print("\n2. 添加子分类:")
    print("   - 访问后台: http://127.0.0.1:8003/goods/categories/add/")
    print("   - 填写分类名称，选择对应的父分类")
    print("   - 设置排序号和状态")
    
    print("\n3. 编辑分类:")
    print("   - 在分类列表页面点击编辑按钮")
    print("   - 可以修改名称、父分类、排序等")
    
    print("\n4. 分类图标:")
    print("   - 支持上传分类图标")
    print("   - 建议尺寸: 64x64 像素")
    print("   - 支持 PNG、JPG、GIF 格式")
    
    print("\n5. 分类状态:")
    print("   - 启用: 分类在前台显示")
    print("   - 禁用: 分类在前台隐藏，但商品仍可访问")

def backup_categories():
    """备份分类数据"""
    print(f"\n💾 备份分类数据...")
    
    import json
    from datetime import datetime
    
    backup_data = {
        'backup_time': datetime.now().isoformat(),
        'categories': []
    }
    
    for category in Category.objects.all():
        category_data = {
            'id': category.id,
            'name': category.name,
            'parent_id': category.parent.id if category.parent else None,
            'parent_name': category.parent.name if category.parent else None,
            'sort_order': category.sort_order,
            'is_active': category.is_active,
            'created_time': category.created_time.isoformat(),
            'products_count': category.products.count()
        }
        backup_data['categories'].append(category_data)
    
    # 保存备份文件
    backup_filename = f"category_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(backup_filename, 'w', encoding='utf-8') as f:
        json.dump(backup_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 分类数据已备份到: {backup_filename}")
    return backup_filename

def show_category_tree():
    """显示分类树结构"""
    print(f"\n🌳 当前分类树结构:")
    print("=" * 60)
    
    parent_categories = Category.objects.filter(parent=None).order_by('sort_order')
    
    for parent in parent_categories:
        status_icon = "🟢" if parent.is_active else "🔴"
        product_count = Product.objects.filter(category__parent=parent).count()
        print(f"{status_icon} {parent.name} ({product_count} 个商品)")
        
        child_categories = parent.children.all().order_by('sort_order')
        for child in child_categories:
            child_status_icon = "🟢" if child.is_active else "🔴"
            child_product_count = child.products.count()
            print(f"  ├─ {child_status_icon} {child.name} ({child_product_count} 个商品)")

def main():
    """主函数"""
    print("🚀 增强分类管理功能...")
    print("=" * 80)
    
    try:
        with transaction.atomic():
            # 1. 显示当前分类结构
            show_category_tree()
            
            # 2. 添加自定义子分类
            added_count = add_custom_subcategories()
            
            # 3. 优化分类结构
            optimize_category_structure()
            
            # 4. 生成统计信息
            stats = create_category_statistics()
            
            # 5. 备份分类数据
            backup_file = backup_categories()
            
            # 6. 显示更新后的分类结构
            print(f"\n🌳 更新后的分类树结构:")
            print("=" * 60)
            show_category_tree()
            
            # 7. 显示管理指南
            show_category_management_guide()
            
            print(f"\n🎉 分类管理增强完成!")
            print(f"✅ 新增了 {added_count} 个子分类")
            print(f"✅ 优化了分类排序")
            print(f"✅ 生成了统计信息")
            print(f"✅ 创建了数据备份: {backup_file}")
            
            print(f"\n📝 管理员可以:")
            print("1. 在后台继续添加自定义分类")
            print("2. 编辑现有分类信息")
            print("3. 上传分类图标")
            print("4. 调整分类排序")
            print("5. 启用/禁用分类")
            
    except Exception as e:
        print(f"❌ 增强过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
