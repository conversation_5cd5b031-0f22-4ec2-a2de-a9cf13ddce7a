<!-- 多级分类导航 - 鼠标悬停显示子分类 -->
<div class="category-nav-dropdown">
  <div class="category-title">
    <a href="{% url 'goods:list' %}" class="category-title-link">
      <i class="fas fa-bars"></i>
      <span>分类</span>
    </a>
  </div>
  <div class="category-menu">
    <ul class="category-list">
      <!-- 数码 -->
      <li class="category-item">
        <a href="{% url 'goods:category' 309 %}" class="category-link">
          <i class="fas fa-mobile-alt"></i>
          <span>数码</span>
          <i class="fas fa-angle-right"></i>
        </a>
        <div class="subcategory-panel">
          <div class="subcategory-section">
            <div class="subcategory-group">
              <div class="subcategory-title">手机</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 4 %}">华为</a>
                <a href="{% url 'goods:category' 5 %}">苹果</a>
                <a href="{% url 'goods:category' 6 %}">小米</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">电脑</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 8 %}">笔记本</a>
                <a href="{% url 'goods:category' 9 %}">台式机</a>
                <a href="{% url 'goods:category' 10 %}">平板</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">配件</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 12 %}">耳机</a>
                <a href="{% url 'goods:category' 13 %}">鼠标</a>
                <a href="{% url 'goods:category' 14 %}">充电宝</a>
              </div>
            </div>
          </div>
        </div>
      </li>
      
      <!-- 家居 -->
      <li class="category-item">
        <a href="{% url 'goods:category' 303 %}" class="category-link">
          <i class="fas fa-home"></i>
          <span>家居</span>
          <i class="fas fa-angle-right"></i>
        </a>
        <div class="subcategory-panel">
          <div class="subcategory-section">
            <div class="subcategory-group">
              <div class="subcategory-title">厨房</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 28 %}">餐具</a>
                <a href="{% url 'goods:category' 29 %}">锅具</a>
                <a href="{% url 'goods:category' 30 %}">水杯</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">客厅</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 32 %}">沙发</a>
                <a href="{% url 'goods:category' 33 %}">茶几</a>
                <a href="{% url 'goods:category' 34 %}">装饰品</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">卧室</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 36 %}">床品</a>
                <a href="{% url 'goods:category' 37 %}">抱枕</a>
                <a href="{% url 'goods:category' 38 %}">挂毯</a>
              </div>
            </div>
          </div>
        </div>
      </li>
      
      <!-- 食品 -->
      <li class="category-item">
        <a href="{% url 'goods:category' 318 %}" class="category-link">
          <i class="fas fa-utensils"></i>
          <span>食品</span>
          <i class="fas fa-angle-right"></i>
        </a>
        <div class="subcategory-panel">
          <div class="subcategory-section">
            <div class="subcategory-group">
              <div class="subcategory-title">零食</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 41 %}">饼干</a>
                <a href="{% url 'goods:category' 42 %}">薯片</a>
                <a href="{% url 'goods:category' 43 %}">坚果</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">饮品</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 45 %}">茶</a>
                <a href="{% url 'goods:category' 46 %}">咖啡</a>
                <a href="{% url 'goods:category' 47 %}">果汁</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">生鲜</div>
              <div class="subcategory-links">
                <a href="{% url 'goods:category' 49 %}">肉类</a>
                <a href="{% url 'goods:category' 50 %}">水果</a>
                <a href="{% url 'goods:category' 51 %}">蔬菜</a>
              </div>
            </div>
          </div>
        </div>
      </li>
      
      <!-- 手机数码 -->
      <li class="category-item">
        <a href="{% url 'goods:category' 52 %}" class="category-link">
          <i class="fas fa-mobile-alt"></i>
          <span>手机数码</span>
        </a>
      </li>
      
      <!-- 家居生活 -->
      <li class="category-item">
        <a href="{% url 'goods:category' 54 %}" class="category-link">
          <i class="fas fa-couch"></i>
          <span>家居生活</span>
        </a>
      </li>
      
      <!-- 食品饮料 -->
      <li class="category-item">
        <a href="{% url 'goods:category' 55 %}" class="category-link">
          <i class="fas fa-cookie-bite"></i>
          <span>食品饮料</span>
        </a>
      </li>
      
      <!-- 服饰 -->
      <li class="category-item">
        <a href="#" class="category-link">
          <i class="fas fa-tshirt"></i>
          <span>服饰</span>
          <i class="fas fa-angle-right"></i>
        </a>
        <div class="subcategory-panel">
          <div class="subcategory-section">
            <div class="subcategory-group">
              <div class="subcategory-title">女装</div>
              <div class="subcategory-links">
                <a href="#">连衣裙</a>
                <a href="#">T恤</a>
                <a href="#">衬衫</a>
                <a href="#">裤装</a>
                <a href="#">外套</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">男装</div>
              <div class="subcategory-links">
                <a href="#">T恤</a>
                <a href="#">衬衫</a>
                <a href="#">裤装</a>
                <a href="#">夹克</a>
                <a href="#">西装</a>
              </div>
            </div>
            <div class="subcategory-group">
              <div class="subcategory-title">配饰</div>
              <div class="subcategory-links">
                <a href="#">帽子</a>
                <a href="#">围巾</a>
                <a href="#">手套</a>
                <a href="#">腰带</a>
                <a href="#">袜子</a>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</div>

<style>
/* 多级分类导航样式 */
.category-nav-dropdown {
  position: relative;
  width: 100%;
  background-color: #dc3545;
  color: white;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 1000;
}

.category-title {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  user-select: none;
  transition: all 0.3s ease;
}

.category-title-link {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 15px;
  color: white;
  text-decoration: none;
  width: 100%;
  transition: all 0.3s ease;
}

.category-title-link:hover {
  background-color: rgba(255,255,255,0.1);
  color: white;
  text-decoration: none;
}

.category-title i {
  margin-right: 8px;
}

.category-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 0 0 4px 4px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 999;
}

.category-nav-dropdown:hover .category-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.category-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.category-item {
  position: relative;
  border-bottom: 1px solid #f0f0f0;
}

.category-item:last-child {
  border-bottom: none;
}

.category-link {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
}

.category-link i:first-child {
  margin-right: 10px;
  color: #dc3545;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.category-link i.fa-angle-right {
  margin-left: auto;
  font-size: 14px;
  color: #999;
  transition: transform 0.3s;
}

.category-item:hover .category-link {
  color: #dc3545;
  background-color: #f8f8f8;
}

.category-item:hover .fa-angle-right {
  transform: translateX(3px);
}

.subcategory-panel {
  position: absolute;
  top: 0;
  left: 100%;
  width: 650px;
  min-height: 100%;
  background: white;
  box-shadow: 4px 0 12px rgba(0,0,0,0.1);
  padding: 20px;
  border-radius: 0 4px 4px 0;
  opacity: 0;
  visibility: hidden;
  transform: translateX(10px);
  transition: all 0.3s ease;
  z-index: 100;
}

.category-item:hover .subcategory-panel {
  opacity: 1;
  visibility: visible;
  transform: translateX(0);
}

.subcategory-section {
  display: flex;
  flex-wrap: wrap;
}

.subcategory-group {
  flex: 1;
  min-width: 30%;
  margin-right: 20px;
  margin-bottom: 20px;
}

.subcategory-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.subcategory-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.subcategory-links a {
  color: #666;
  text-decoration: none;
  padding: 5px 0;
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  padding-left: 12px;
}

.subcategory-links a:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  width: 5px;
  height: 5px;
  background-color: #dc3545;
  border-radius: 50%;
  transform: translateY(-50%);
  transition: all 0.3s ease;
  opacity: 0.5;
}

.subcategory-links a:hover {
  color: #dc3545;
  transform: translateX(5px);
}

.subcategory-links a:hover:before {
  opacity: 1;
  width: 7px;
  height: 7px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .subcategory-panel {
    width: 450px;
  }
}

@media (max-width: 576px) {
  .subcategory-panel {
    width: 320px;
  }
  
  .subcategory-group {
    min-width: 45%;
  }
}
</style> 