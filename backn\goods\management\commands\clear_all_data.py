from django.core.management.base import BaseCommand
from django.db import transaction
from orders.models import Order, OrderItem
from goods.models import Product, Category, ProductImage


class Command(BaseCommand):
    help = '清理所有商品和分类数据'

    def handle(self, *args, **options):
        self.stdout.write('开始清理数据...')
        
        try:
            with transaction.atomic():
                # 删除订单项
                order_items_count = OrderItem.objects.count()
                OrderItem.objects.all().delete()
                self.stdout.write(f'删除了 {order_items_count} 个订单项')
                
                # 删除订单
                orders_count = Order.objects.count()
                Order.objects.all().delete()
                self.stdout.write(f'删除了 {orders_count} 个订单')
                
                # 删除商品图片
                images_count = ProductImage.objects.count()
                ProductImage.objects.all().delete()
                self.stdout.write(f'删除了 {images_count} 个商品图片')
                
                # 删除商品
                products_count = Product.objects.count()
                Product.objects.all().delete()
                self.stdout.write(f'删除了 {products_count} 个商品')
                
                # 删除分类
                categories_count = Category.objects.count()
                Category.objects.all().delete()
                self.stdout.write(f'删除了 {categories_count} 个分类')
                
            self.stdout.write(self.style.SUCCESS('所有数据清理完成！'))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'清理数据时出错: {e}'))
