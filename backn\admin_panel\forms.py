from django import forms
from django.contrib.auth import get_user_model
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError
import re

User = get_user_model()


class AdminRegisterForm(UserCreationForm):
    """管理员注册表单"""
    
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入邮箱地址'
        }),
        help_text='请输入有效的邮箱地址，用于接收重要通知'
    )
    
    phone = forms.CharField(
        max_length=11,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入手机号码'
        }),
        help_text='手机号码用于安全验证（可选）'
    )
    
    avatar = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={
            'class': 'form-control',
            'accept': 'image/*'
        }),
        help_text='支持JPG、PNG格式，文件大小不超过5MB'
    )
    
    class Meta:
        model = User
        fields = ('username', 'email', 'phone', 'avatar', 'password1', 'password2')
        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入用户名'
            }),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 设置字段属性
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '请输入用户名'
        })
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '请输入密码'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '请再次输入密码'
        })
        
        # 设置帮助文本
        self.fields['username'].help_text = '用户名长度3-20个字符，只能包含字母、数字和下划线'
        self.fields['password1'].help_text = '密码至少8个字符，建议包含大小写字母、数字和特殊字符'
        self.fields['password2'].help_text = '请再次输入相同的密码进行确认'
    
    def clean_username(self):
        """验证用户名"""
        username = self.cleaned_data.get('username')
        
        if not username:
            raise ValidationError('用户名不能为空')
        
        if len(username) < 3:
            raise ValidationError('用户名至少需要3个字符')
        
        if len(username) > 20:
            raise ValidationError('用户名不能超过20个字符')
        
        # 检查用户名格式
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            raise ValidationError('用户名只能包含字母、数字和下划线')
        
        # 检查用户名是否已存在
        if User.objects.filter(username=username).exists():
            raise ValidationError('该用户名已被使用，请选择其他用户名')
        
        return username
    
    def clean_email(self):
        """验证邮箱"""
        email = self.cleaned_data.get('email')
        
        if not email:
            raise ValidationError('邮箱地址不能为空')
        
        # 检查邮箱格式
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, email):
            raise ValidationError('请输入有效的邮箱地址')
        
        # 检查邮箱是否已存在
        if User.objects.filter(email=email).exists():
            raise ValidationError('该邮箱已被注册，请使用其他邮箱')
        
        return email
    
    def clean_phone(self):
        """验证手机号"""
        phone = self.cleaned_data.get('phone')
        
        if phone:
            # 检查手机号格式
            phone_pattern = r'^1[3-9]\d{9}$'
            if not re.match(phone_pattern, phone):
                raise ValidationError('请输入有效的手机号码')
            
            # 检查手机号是否已存在
            if User.objects.filter(phone=phone).exists():
                raise ValidationError('该手机号已被注册，请使用其他手机号')
        
        return phone
    
    def clean_avatar(self):
        """验证头像"""
        avatar = self.cleaned_data.get('avatar')
        
        if avatar:
            # 检查文件大小（5MB限制）
            if avatar.size > 5 * 1024 * 1024:
                raise ValidationError('头像文件大小不能超过5MB')
            
            # 检查文件类型
            allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
            if avatar.content_type not in allowed_types:
                raise ValidationError('头像只支持JPG、PNG、GIF格式')
        
        return avatar
    
    def clean_password1(self):
        """验证密码强度"""
        password1 = self.cleaned_data.get('password1')
        
        if not password1:
            raise ValidationError('密码不能为空')
        
        if len(password1) < 8:
            raise ValidationError('密码至少需要8个字符')
        
        # 检查密码复杂度
        has_upper = any(c.isupper() for c in password1)
        has_lower = any(c.islower() for c in password1)
        has_digit = any(c.isdigit() for c in password1)
        has_special = any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password1)
        
        strength_count = sum([has_upper, has_lower, has_digit, has_special])
        
        if strength_count < 2:
            raise ValidationError('密码强度太弱，建议包含大小写字母、数字和特殊字符')
        
        return password1
    
    def save(self, commit=True):
        """保存用户"""
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.phone = self.cleaned_data.get('phone', '')
        
        # 设置为管理员（直接获得权限）
        user.is_staff = True  # 直接给予管理员权限
        user.is_active = True  # 账号激活
        
        if commit:
            user.save()
            
            # 处理头像上传
            if self.cleaned_data.get('avatar'):
                user.avatar = self.cleaned_data['avatar']
                user.save()
        
        return user


class AdminLoginForm(forms.Form):
    """管理员登录表单"""
    
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入用户名',
            'required': True
        })
    )
    
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': '请输入密码',
            'required': True
        })
    )
    
    def clean(self):
        """验证登录信息"""
        cleaned_data = super().clean()
        username = cleaned_data.get('username')
        password = cleaned_data.get('password')
        
        if username and password:
            from django.contrib.auth import authenticate
            user = authenticate(username=username, password=password)
            
            if not user:
                raise ValidationError('用户名或密码错误')
            
            if not user.is_active:
                raise ValidationError('账号已被禁用，请联系管理员')
            
            if not (user.is_staff or user.is_superuser):
                raise ValidationError('您没有管理员权限')
            
            cleaned_data['user'] = user
        
        return cleaned_data
