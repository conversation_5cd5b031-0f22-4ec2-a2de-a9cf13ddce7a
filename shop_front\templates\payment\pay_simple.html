{% extends 'base.html' %}

{% block title %}订单支付 - MARS BUY{% endblock %}

{% load static %}

{% csrf_token %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    .container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
    }

    .payment-container {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 40px;
        margin: 20px 0;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
    }

    .payment-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .payment-title {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .order-info {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border-left: 5px solid #dc3545;
    }

    .order-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }

    .order-detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        border-bottom: 1px dashed #ddd;
    }

    .order-detail-item:last-child {
        border-bottom: none;
    }

    .order-amount {
        color: #dc3545 !important;
        font-size: 24px;
        font-weight: 700;
    }

    .payment-methods {
        margin-bottom: 30px;
    }

    .payment-options {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
    }

    .payment-option {
        background: white;
        border: 2px solid #e0e0e0;
        border-radius: 15px;
        padding: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }

    .payment-option:hover {
        border-color: #dc3545;
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(220,53,69,0.15);
    }

    .payment-option.selected {
        border-color: #dc3545;
        background: linear-gradient(135deg, rgba(220,53,69,0.05) 0%, rgba(231,76,60,0.05) 100%);
    }

    .payment-icon {
        font-size: 48px;
        margin-bottom: 15px;
    }

    .alipay { color: #1677ff; }
    .wechat { color: #07c160; }
    .bank { color: #6c757d; }

    .password-section {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 30px;
        margin: 30px 0;
        border-left: 5px solid #28a745;
        display: none;
    }

    .password-display {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin: 20px 0;
    }

    .password-dot {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2px solid #ddd;
        background: #f8f9fa;
        transition: all 0.3s ease;
    }

    .password-dot.filled {
        background: #28a745;
        border-color: #28a745;
    }

    .number-keyboard {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        max-width: 300px;
        margin: 20px auto;
    }

    .num-btn {
        padding: 15px;
        border: 2px solid #e0e0e0;
        background: white;
        border-radius: 10px;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .num-btn:hover {
        background: #f8f9fa;
        border-color: #28a745;
    }

    .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        margin: 10px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        box-shadow: 0 8px 25px rgba(108,117,125,0.3);
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-3px);
        color: white;
        text-decoration: none;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .text-center {
        text-align: center;
    }

    .section-title {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    @media (max-width: 768px) {
        .payment-container {
            padding: 20px;
            margin: 10px;
        }
        
        .payment-options {
            grid-template-columns: 1fr;
        }
        
        .number-keyboard {
            max-width: 280px;
        }
        
        .num-btn {
            padding: 12px;
            font-size: 16px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="payment-container">
        <div class="payment-header">
            <h1 class="payment-title">
                <i class="fas fa-credit-card"></i>
                订单支付
            </h1>
            <p>请选择支付方式完成订单支付</p>
        </div>

        <!-- 订单信息 -->
        <div class="order-info">
            <h3 class="section-title">
                <i class="fas fa-receipt"></i>
                订单信息
            </h3>
            <div class="order-details">
                <div class="order-detail-item">
                    <span>订单号：</span>
                    <span>{{ order.order_number }}</span>
                </div>
                <div class="order-detail-item">
                    <span>创建时间：</span>
                    <span>{{ order.created_time|date:"Y-m-d H:i:s" }}</span>
                </div>
                <div class="order-detail-item">
                    <span>商品数量：</span>
                    <span>{{ order.items.count }} 件</span>
                </div>
                <div class="order-detail-item">
                    <span>应付金额：</span>
                    <span class="order-amount">¥{{ order.pay_amount }}</span>
                </div>
            </div>
        </div>

        <!-- 支付方式选择 -->
        <div class="payment-methods">
            <h3 class="section-title">
                <i class="fas fa-wallet"></i>
                选择支付方式
            </h3>
            <div class="payment-options">
                <div class="payment-option" data-method="alipay">
                    <div class="payment-icon alipay">
                        <i class="fab fa-alipay"></i>
                    </div>
                    <div>支付宝支付</div>
                </div>
                <div class="payment-option" data-method="wechat">
                    <div class="payment-icon wechat">
                        <i class="fab fa-weixin"></i>
                    </div>
                    <div>微信支付</div>
                </div>
                <div class="payment-option" data-method="bank">
                    <div class="payment-icon bank">
                        <i class="fas fa-university"></i>
                    </div>
                    <div>银行卡支付</div>
                </div>
            </div>
        </div>

        <!-- 支付密码输入区域 -->
        <div id="passwordSection" class="password-section">
            <h3 class="section-title">
                <i class="fas fa-shield-alt"></i>
                输入支付密码
            </h3>
            <div class="password-display">
                <span class="password-dot" data-index="0"></span>
                <span class="password-dot" data-index="1"></span>
                <span class="password-dot" data-index="2"></span>
                <span class="password-dot" data-index="3"></span>
                <span class="password-dot" data-index="4"></span>
                <span class="password-dot" data-index="5"></span>
            </div>
            <div class="number-keyboard">
                <button class="num-btn" data-num="1">1</button>
                <button class="num-btn" data-num="2">2</button>
                <button class="num-btn" data-num="3">3</button>
                <button class="num-btn" data-num="4">4</button>
                <button class="num-btn" data-num="5">5</button>
                <button class="num-btn" data-num="6">6</button>
                <button class="num-btn" data-num="7">7</button>
                <button class="num-btn" data-num="8">8</button>
                <button class="num-btn" data-num="9">9</button>
                <button class="num-btn" onclick="cancelPayment()">取消</button>
                <button class="num-btn" data-num="0">0</button>
                <button class="num-btn" onclick="deleteLastDigit()">
                    <i class="fas fa-backspace"></i>
                </button>
            </div>
            <div class="text-center">
                <button id="confirmPayBtn" class="btn btn-primary" disabled>
                    <i class="fas fa-check"></i>
                    确认支付 ¥{{ order.pay_amount }}
                </button>
            </div>
        </div>

        <!-- 支付按钮 -->
        <div class="text-center" id="paymentActions">
            <a href="{% url 'order:detail' order.order_number %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i>
                返回订单
            </a>
            <button id="payBtn" class="btn btn-primary" disabled>
                <i class="fas fa-lock"></i>
                立即支付 ¥{{ order.pay_amount }}
            </button>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentOptions = document.querySelectorAll('.payment-option');
    const payBtn = document.getElementById('payBtn');
    const passwordSection = document.getElementById('passwordSection');
    const paymentActions = document.getElementById('paymentActions');
    const confirmPayBtn = document.getElementById('confirmPayBtn');
    const passwordDots = document.querySelectorAll('.password-dot');
    const numBtns = document.querySelectorAll('.num-btn[data-num]');
    
    let selectedMethod = null;
    let currentPassword = '';

    console.log('支付页面初始化完成');

    // 支付方式选择
    paymentOptions.forEach(option => {
        option.addEventListener('click', function() {
            console.log('选择支付方式:', this.dataset.method);
            
            // 移除其他选中状态
            paymentOptions.forEach(opt => opt.classList.remove('selected'));
            // 添加选中状态
            this.classList.add('selected');
            selectedMethod = this.dataset.method;
            
            // 启用支付按钮
            payBtn.disabled = false;
            payBtn.innerHTML = `<i class="fas fa-lock"></i> 立即支付 ¥{{ order.pay_amount }}`;
        });
    });

    // 支付按钮点击
    payBtn.addEventListener('click', function() {
        if (!selectedMethod) {
            alert('请选择支付方式');
            return;
        }

        console.log('显示密码输入界面');
        // 显示支付密码输入区域
        passwordSection.style.display = 'block';
        paymentActions.style.display = 'none';
        
        // 滚动到密码输入区域
        passwordSection.scrollIntoView({ behavior: 'smooth' });
        
        // 重置密码输入
        currentPassword = '';
        updatePasswordDisplay();
    });

    // 数字键盘事件
    numBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const num = this.dataset.num;
            if (currentPassword.length < 6) {
                currentPassword += num;
                console.log('输入密码位数:', currentPassword.length);
                updatePasswordDisplay();
                
                // 如果输入满6位，自动提交
                if (currentPassword.length === 6) {
                    setTimeout(() => {
                        processPayment();
                    }, 500);
                }
            }
        });
    });

    // 更新密码显示
    function updatePasswordDisplay() {
        passwordDots.forEach((dot, index) => {
            if (index < currentPassword.length) {
                dot.classList.add('filled');
            } else {
                dot.classList.remove('filled');
            }
        });
        
        // 更新确认按钮状态
        confirmPayBtn.disabled = currentPassword.length !== 6;
    }

    // 确认支付按钮
    confirmPayBtn.addEventListener('click', function() {
        if (currentPassword.length === 6) {
            processPayment();
        }
    });

    // 处理支付
    async function processPayment() {
        console.log('开始处理支付，密码:', currentPassword);
        
        // 显示支付处理中
        confirmPayBtn.disabled = true;
        confirmPayBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 验证密码中...';
        
        try {
            // 简化版本：直接验证密码，不调用API
            const validPasswords = ['123456', '888888', '666666', '000000'];

            if (validPasswords.includes(currentPassword)) {
                // 密码验证成功，继续支付流程
                confirmPayBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 支付处理中...';

                console.log('密码验证成功，跳转支付，方式:', selectedMethod);

                // 延迟1秒后跳转，让用户看到处理状态
                setTimeout(() => {
                    // 所有支付方式都跳转到模拟成功页面
                    window.location.href = "{% url 'payment:simulate_success' order.order_number %}";
                }, 1000);
            } else {
                // 密码错误
                showPasswordError();
            }
        } catch (error) {
            console.error('支付处理错误:', error);
            alert('支付处理失败: ' + error.message);
            resetPaymentForm();
        }
    }

    // 显示密码错误
    function showPasswordError() {
        // 密码框震动效果
        passwordSection.style.animation = 'shake 0.5s';
        setTimeout(() => {
            passwordSection.style.animation = '';
        }, 500);
        
        // 清空密码
        currentPassword = '';
        updatePasswordDisplay();
        
        // 显示错误提示
        alert('支付密码错误，请重新输入\n默认密码：123456, 888888, 666666, 000000');
        
        // 重置按钮
        confirmPayBtn.disabled = true;
        confirmPayBtn.innerHTML = '<i class="fas fa-check"></i> 确认支付 ¥{{ order.pay_amount }}';
    }

    // 重置支付表单
    function resetPaymentForm() {
        passwordSection.style.display = 'none';
        paymentActions.style.display = 'block';
        currentPassword = '';
        updatePasswordDisplay();
        confirmPayBtn.disabled = true;
        confirmPayBtn.innerHTML = '<i class="fas fa-check"></i> 确认支付 ¥{{ order.pay_amount }}';
    }

    // 全局函数
    window.cancelPayment = function() {
        console.log('取消支付');
        resetPaymentForm();
    };

    window.deleteLastDigit = function() {
        if (currentPassword.length > 0) {
            currentPassword = currentPassword.slice(0, -1);
            console.log('删除一位，当前长度:', currentPassword.length);
            updatePasswordDisplay();
        }
    };
});

// 添加震动动画
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
