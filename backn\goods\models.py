from django.db import models
from django.urls import reverse
from django.utils.translation import gettext_lazy as _

class Category(models.Model):
    """商品分类模型"""
    name = models.CharField(_('分类名称'), max_length=100, db_index=True)
    parent = models.ForeignKey('self', verbose_name=_('父分类'),
                              on_delete=models.CASCADE, null=True, blank=True,
                              related_name='children')
    icon = models.ImageField(upload_to='categories/', blank=True, null=True, verbose_name=_('分类图标'))
    sort_order = models.IntegerField(_('排序'), default=0, db_index=True)
    is_active = models.BooleanField(_('是否启用'), default=True, db_index=True)
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_time = models.DateTimeField(_('更新时间'), auto_now=True)

    class Meta:
        verbose_name = _('商品分类')
        verbose_name_plural = _('商品分类')
        ordering = ['sort_order', 'name']
        indexes = [
            models.Index(fields=['parent', 'is_active']),
            models.Index(fields=['sort_order']),
        ]

    def __str__(self):
        return self.name

    @property
    def level(self):
        """获取分类层级"""
        level = 0
        parent = self.parent
        while parent:
            level += 1
            parent = parent.parent
        return level

    def get_children(self):
        """获取子分类"""
        return self.children.filter(is_active=True).order_by('sort_order')

    def get_all_children(self):
        """递归获取所有子分类"""
        children = []
        for child in self.get_children():
            children.append(child)
            children.extend(child.get_all_children())
        return children

    def get_products_count(self):
        """获取该分类下的商品数量"""
        return self.products.filter(is_active=True).count()

class Product(models.Model):
    """商品模型"""
    category = models.ForeignKey(Category, verbose_name=_('商品分类'),
                               on_delete=models.CASCADE, related_name='products')
    name = models.CharField(_('商品名称'), max_length=200, db_index=True)
    description = models.TextField(_('商品描述'))
    price = models.DecimalField(_('价格'), max_digits=10, decimal_places=2, default=0)
    main_image = models.ImageField(upload_to='products/', default='products/default.png', verbose_name=_('主图'))
    stock = models.IntegerField(_('库存'), default=0)
    sales = models.IntegerField(_('销量'), default=0)
    is_hot = models.BooleanField(_('是否热门'), default=False, db_index=True)
    is_new = models.BooleanField(_('是否新品'), default=True, db_index=True)
    is_active = models.BooleanField(_('是否上架'), default=True, db_index=True)
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_time = models.DateTimeField(_('更新时间'), auto_now=True)

    class Meta:
        verbose_name = _('商品')
        verbose_name_plural = _('商品')
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['name', 'price']),
            models.Index(fields=['is_hot', 'is_new', 'is_active']),
            models.Index(fields=['created_time']),
        ]

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        return reverse('goods:detail', args=[self.id])

    @property
    def image(self):
        """兼容性方法，确保模板中使用image字段的代码依然能正常工作"""
        return self.main_image

class ProductImage(models.Model):
    """商品图片模型"""
    product = models.ForeignKey(Product, verbose_name=_('商品'),
                               on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/', verbose_name=_('图片'))
    alt_text = models.CharField(_('图片描述'), max_length=200, blank=True)
    sort_order = models.IntegerField(_('排序'), default=0)
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)

    class Meta:
        verbose_name = _('商品图片')
        verbose_name_plural = _('商品图片')
        ordering = ['sort_order', 'created_time']
        indexes = [
            models.Index(fields=['product', 'sort_order']),
        ]

    def __str__(self):
        return f"{self.product.name} - 图片{self.id}"
