from django.db import models
from django.utils.translation import gettext_lazy as _

# Create your models here.

class Payment(models.Model):
    """支付记录模型"""
    PAYMENT_METHOD_CHOICES = (
        ('alipay', _('支付宝')),
        ('wechat', _('微信支付')),
    )
    
    PAYMENT_STATUS_CHOICES = (
        ('pending', _('待支付')),
        ('success', _('支付成功')),
        ('failed', _('支付失败')),
        ('refunded', _('已退款')),
    )
    
    order = models.ForeignKey('order.Order', on_delete=models.CASCADE, verbose_name=_('订单'))
    payment_method = models.CharField(max_length=10, choices=PAYMENT_METHOD_CHOICES, verbose_name=_('支付方式'))
    trade_no = models.CharField(max_length=100, unique=True, db_index=True, verbose_name=_('支付流水号'))
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('支付金额'))
    status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending', db_index=True, verbose_name=_('支付状态'))
    created_time = models.DateTimeField(auto_now_add=True, db_index=True, verbose_name=_('创建时间'))
    paid_time = models.DateTimeField(null=True, blank=True, verbose_name=_('支付时间'))
    refund_time = models.DateTimeField(null=True, blank=True, verbose_name=_('退款时间'))
    remark = models.TextField(blank=True, null=True, verbose_name=_('备注'))

    class Meta:
        verbose_name = _('支付记录')
        verbose_name_plural = verbose_name
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['order', 'status']),
            models.Index(fields=['trade_no']),
            models.Index(fields=['payment_method', 'status']),
        ]

    def __str__(self):
        return f"{self.get_payment_method_display()}-{self.trade_no}"
