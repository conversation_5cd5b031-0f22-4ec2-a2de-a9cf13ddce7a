# Generated by Django 4.2.22 on 2025-06-07 07:19

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('order', '0001_initial'),
        ('goods', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Review',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField(verbose_name='评价内容')),
                ('score', models.IntegerField(choices=[(1, '很差'), (2, '差'), (3, '一般'), (4, '好'), (5, '很好')], default=5, verbose_name='评分')),
                ('is_anonymous', models.BooleanField(default=False, verbose_name='是否匿名')),
                ('is_verified', models.BooleanField(default=False, verbose_name='是否已验证')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('order_item', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='review', to='order.orderitem', verbose_name='订单项')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='goods.product', verbose_name='商品')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '商品评价',
                'verbose_name_plural': '商品评价',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ReviewImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='review_images/%Y/%m/', verbose_name='图片')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('review', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='review_images', to='reviews.review', verbose_name='评价')),
            ],
            options={
                'verbose_name': '评价图片',
                'verbose_name_plural': '评价图片',
                'ordering': ['created_at'],
                'indexes': [models.Index(fields=['review'], name='reviews_rev_review__5c0b89_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['user', 'product'], name='reviews_rev_user_id_eefe96_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['product', 'score'], name='reviews_rev_product_b740b2_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['created_at'], name='reviews_rev_created_bdcc91_idx'),
        ),
        migrations.AddIndex(
            model_name='review',
            index=models.Index(fields=['is_verified'], name='reviews_rev_is_veri_f1ab6d_idx'),
        ),
    ]
