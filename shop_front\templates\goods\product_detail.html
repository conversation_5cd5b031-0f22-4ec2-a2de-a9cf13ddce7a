{% extends 'base.html' %}

{% block title %}{{ product.name }} - 在线商城{% endblock %}

{% load static %}

<!-- CSRF Token for AJAX requests -->
{% csrf_token %}

{% block content %}
<div class="container">
    <!-- 管理员工具栏 -->
    {% if user.is_staff %}
    <div class="admin-toolbar">
        <h3>管理员工具</h3>
        <div class="admin-actions">
            <button class="admin-btn edit-btn" data-product-id="{{ product.id }}" onclick="openEditModal(this)">
                <i class="fas fa-edit"></i> 编辑商品
            </button>
            <button class="admin-btn delete-btn" data-product-id="{{ product.id }}" onclick="deleteProduct(this)">
                <i class="fas fa-trash"></i> 删除商品
            </button>
            <a href="{% url 'goods:add_product' %}" class="admin-btn add-btn">
                <i class="fas fa-plus"></i> 添加商品
            </a>
            <button class="admin-btn toggle-btn" data-product-id="{{ product.id }}" onclick="toggleProductStatus(this)">
                {% if product.is_active %}
                <i class="fas fa-eye-slash"></i> 下架商品
                {% else %}
                <i class="fas fa-eye"></i> 上架商品
                {% endif %}
            </button>
        </div>
    </div>
    {% endif %}

    <!-- 面包屑导航 -->
    <div class="breadcrumb">
        <a href="{% url 'home:index' %}">首页</a>
        <span class="separator">></span>
        <a href="{% url 'goods:category' product.category.id %}">{{ product.category.name }}</a>
        <span class="separator">></span>
        <span class="current">{{ product.name }}</span>
    </div>

    <!-- 商品详情 -->
    <div class="product-detail">
        <div class="product-gallery">
            <div class="main-image-container">
                {% if product.main_image %}
                <img src="{{ product.main_image.url }}" alt="{{ product.name }}" id="main-image" class="main-image" onerror="this.src='{% static 'images/default.jpg' %}'">
                {% if user.is_staff %}
                <div class="image-admin-overlay">
                    <button class="image-edit-btn" onclick="openEditModal()" title="编辑商品">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
                {% endif %}
                {% else %}
                <img src="{% static 'images/default.jpg' %}" alt="{{ product.name }}" id="main-image" class="main-image">
                {% endif %}
            </div>
            <div class="thumbnail-container">
                {% if product.main_image %}
                <div class="thumbnail active" onclick="changeMainImage('{{ product.main_image.url }}', this)">
                    <img src="{{ product.main_image.url }}" alt="{{ product.name }}" onerror="this.src='{% static 'images/default.jpg' %}'">
                    {% if user.is_staff %}
                    <div class="thumbnail-admin-overlay">
                        <button class="thumbnail-edit-btn" onclick="openEditModal()" title="编辑主图">
                            <i class="fas fa-edit"></i>
                        </button>
                    </div>
                    {% endif %}
                </div>
                {% endif %}
                {% for image in product.images.all %}
                <div class="thumbnail" onclick="changeMainImage('{{ image.image.url }}', this)">
                    <img src="{{ image.image.url }}" alt="{{ product.name }}" onerror="this.src='{% static 'images/default.jpg' %}'">
                    {% if user.is_staff %}
                    <div class="thumbnail-admin-overlay">
                        <button class="thumbnail-edit-btn" onclick="openEditModal()" title="编辑图片">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="thumbnail-delete-btn" onclick="deleteProductImage({{ image.id }})" title="删除图片">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="product-info">
            <h1 class="product-title">{{ product.name }}</h1>

            <div class="product-price-section">
                <div class="current-price">¥{{ product.price }}</div>
                {% if product.original_price and product.original_price != product.price %}
                <div class="original-price">¥{{ product.original_price }}</div>
                {% endif %}
            </div>

            <div class="product-meta">
                <div class="meta-row">
                    <span class="meta-label">商品分类：</span>
                    <span class="meta-value">{{ product.category.name }}</span>
                </div>
                <div class="meta-row">
                    <span class="meta-label">库存数量：</span>
                    <span class="meta-value">{{ product.stock }} 件</span>
                </div>
                <div class="meta-row">
                    <span class="meta-label">销售数量：</span>
                    <span class="meta-value">{{ product.sales }} 件</span>
                </div>
                <div class="meta-row">
                    <span class="meta-label">收藏人数：</span>
                    <span class="meta-value">{{ favorite_count }} 人</span>
                </div>
                {% if product.is_hot %}
                <div class="meta-row">
                    <span class="hot-badge">🔥 热门商品</span>
                </div>
                {% endif %}
                {% if product.is_new %}
                <div class="meta-row">
                    <span class="new-badge">✨ 新品上市</span>
                </div>
                {% endif %}
            </div>

            <div class="product-actions">
                <div class="quantity-section">
                    <label class="quantity-label">购买数量：</label>
                    <div class="quantity-control">
                        <button class="quantity-btn minus" onclick="decreaseQuantity()">-</button>
                        <input type="number" id="quantity" value="1" min="1" max="{{ product.stock }}" class="quantity-input">
                        <button class="quantity-btn plus" onclick="increaseQuantity()">+</button>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="add-to-cart" onclick="addToCart({{ product.id }})">
                        <i class="fas fa-shopping-cart"></i> 加入购物车
                    </button>
                    <form method="post" action="{% url 'order:buy_now' %}" style="display: inline;">
                        {% csrf_token %}
                        <input type="hidden" name="product_id" value="{{ product.id }}">
                        <input type="hidden" name="quantity" id="buy-quantity" value="1">
                        <input type="hidden" name="receiver" value="{{ user.username|default:'默认收货人' }}">
                        <input type="hidden" name="address" value="默认地址">
                        <button type="submit" class="buy-now" onclick="updateBuyQuantity()">
                            <i class="fas fa-bolt"></i> 立即购买
                        </button>
                    </form>
                    
                    <!-- 收藏按钮 -->
                    {% if user.is_authenticated %}
                    <form method="post" action="{% if is_favorite %}{% url 'account:remove_from_favorite' product.id %}{% else %}{% url 'account:add_favorite' product.id %}{% endif %}" id="favoriteForm" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="add-to-favorite {% if is_favorite %}active{% endif %}" id="favoriteBtn" data-product-id="{{ product.id }}" data-is-favorite="{{ is_favorite|yesno:'true,false' }}">
                            {% if is_favorite %}
                            <i class="fas fa-heart"></i> 已收藏
                            {% else %}
                            <i class="far fa-heart"></i> 收藏
                            {% endif %}
                        </button>
                    </form>
                    {% else %}
                    <a href="{% url 'account:login' %}?next={{ request.path }}" class="add-to-favorite">
                        <i class="far fa-heart"></i> 收藏
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 商品详情选项卡 -->
    <div class="product-tabs">
        <div class="tab-header">
            <div class="tab active" data-tab="description">
                <i class="fas fa-info-circle"></i> 商品详情
            </div>
            <div class="tab" data-tab="specs">
                <i class="fas fa-list-ul"></i> 规格参数
            </div>
            <div class="tab" data-tab="reviews">
                <i class="fas fa-star"></i> 用户评价
            </div>
        </div>

        <div class="tab-content">
            <div class="tab-pane active" id="description">
                <div class="product-description">
                    <h3>商品描述</h3>
                    <div class="description-content">
                        {{ product.description|linebreaks|default:"暂无商品描述" }}
                    </div>
                </div>
            </div>

            <div class="tab-pane" id="specs">
                <div class="product-specs">
                    <h3>规格参数</h3>
                    <table class="specs-table">
                        <tr>
                            <td class="spec-label">商品名称</td>
                            <td class="spec-value">{{ product.name }}</td>
                        </tr>
                        <tr>
                            <td class="spec-label">商品分类</td>
                            <td class="spec-value">{{ product.category.name }}</td>
                        </tr>
                        <tr>
                            <td class="spec-label">商品价格</td>
                            <td class="spec-value">¥{{ product.price }}</td>
                        </tr>
                        <tr>
                            <td class="spec-label">库存数量</td>
                            <td class="spec-value">{{ product.stock }} 件</td>
                        </tr>
                        <tr>
                            <td class="spec-label">销售数量</td>
                            <td class="spec-value">{{ product.sales }} 件</td>
                        </tr>
                    </table>
                </div>
            </div>

            <div class="tab-pane" id="reviews">
                <div class="product-reviews">
                    <div class="reviews-header">
                        <h3>用户评价</h3>
                        {% if user.is_authenticated and not user.is_staff %}
                        <button class="btn-add-review" onclick="openReviewModal()">
                            <i class="fas fa-star"></i> 写评价
                        </button>
                        {% endif %}
                    </div>

                    <div class="reviews-summary">
                        <div class="rating-overview">
                            <div class="average-rating">
                                {% if reviews_stats.average_score %}
                                <span class="rating-score">{{ reviews_stats.average_score|floatformat:1 }}</span>
                                <div class="rating-stars">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= reviews_stats.average_score %}
                                        <i class="fas fa-star"></i>
                                        {% else %}
                                        <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <span class="rating-count">({{ reviews_stats.total_count }}条评价)</span>
                                {% else %}
                                <span class="rating-score">暂无评分</span>
                                <div class="rating-stars">
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="rating-count">(0条评价)</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="reviews-list">
                        {% for review in product_reviews %}
                        <div class="review-item">
                            <div class="review-header">
                                <div class="reviewer-info">
                                    <div class="reviewer-avatar">
                                        {% if review.user.avatar %}
                                        <img src="{{ review.user.avatar.url }}" alt="{{ review.user.username }}">
                                        {% else %}
                                        <img src="{% static 'images/default-avatar.png' %}" alt="默认头像">
                                        {% endif %}
                                    </div>
                                    <div class="reviewer-details">
                                        <div class="reviewer-name">
                                            {% if review.is_anonymous %}
                                            匿名用户
                                            {% else %}
                                            {{ review.user.username }}
                                            {% endif %}
                                        </div>
                                        <div class="review-date">{{ review.created_at|date:"Y-m-d H:i" }}</div>
                                    </div>
                                </div>
                                <div class="review-rating">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= review.score %}
                                        <i class="fas fa-star"></i>
                                        {% else %}
                                        <i class="far fa-star"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="review-content">
                                {{ review.content|linebreaks }}
                            </div>
                            {% if review.review_images.exists %}
                            <div class="review-images">
                                {% for image in review.review_images.all %}
                                <div class="review-image">
                                    <img src="{{ image.image.url }}" alt="评价图片" onclick="openImageModal('{{ image.image.url }}')">
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        {% empty %}
                        <div class="no-reviews">
                            <i class="fas fa-comment-slash"></i>
                            <p>暂无用户评价，快来成为第一个评价的用户吧！</p>
                        </div>
                        {% endfor %}
                    </div>

                    {% if product_reviews.has_other_pages %}
                    <div class="reviews-pagination">
                        <div class="pagination">
                            {% if product_reviews.has_previous %}
                            <a href="?page={{ product_reviews.previous_page_number }}#reviews" class="page-link">上一页</a>
                            {% endif %}

                            <span class="page-info">
                                第 {{ product_reviews.number }} 页，共 {{ product_reviews.paginator.num_pages }} 页
                            </span>

                            {% if product_reviews.has_next %}
                            <a href="?page={{ product_reviews.next_page_number }}#reviews" class="page-link">下一页</a>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑商品模态框 -->
{% if user.is_staff %}
<div id="editModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeEditModal()">&times;</span>
        <h2>编辑商品</h2>
        <form id="editProductForm" method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="form-section">
                <h3 class="section-title">基本信息</h3>
                
                <div class="form-group">
                    <label for="name">商品名称</label>
                    <input type="text" id="name" name="name" value="{{ product.name }}" required>
                </div>
                
                <div class="form-row">
                    <div class="form-group half">
                        <label for="price">价格</label>
                        <input type="number" id="price" name="price" step="0.01" min="0" value="{{ product.price }}" required>
                    </div>
                    
                    <div class="form-group half">
                        <label for="original_price">原价</label>
                        <input type="number" id="original_price" name="original_price" step="0.01" min="0" value="{{ product.original_price|default:'' }}">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="category">商品分类</label>
                    <select id="category" name="category" required>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if product.category.id == category.id %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="description">商品描述</label>
                    <textarea id="description" name="description" rows="5" required>{{ product.description }}</textarea>
                </div>
                
                <div class="form-row">
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="is_hot" name="is_hot" {% if product.is_hot %}checked{% endif %}>
                            <span class="checkbox-label">热门商品</span>
                        </label>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" id="is_new" name="is_new" {% if product.is_new %}checked{% endif %}>
                            <span class="checkbox-label">新品上市</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h3 class="section-title">商品图片</h3>
                
                <div class="form-group">
                    <label for="main_image">主图</label>
                    <input type="file" id="main_image" name="main_image" accept="image/*">
                    <div class="current-image">
                        {% if product.main_image %}
                        <p>当前主图：</p>
                        <img src="/media/{{ product.main_image }}" alt="{{ product.name }}" class="thumbnail-img">
                        {% else %}
                        <p>暂无主图</p>
                        {% endif %}
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="product_images">添加更多图片</label>
                    <input type="file" id="product_images" name="product_images" accept="image/*" multiple>
                    <div class="current-images">
                        <p>当前图片：</p>
                        <div class="thumbnail-list">
                            {% for image in product_images %}
                            <div class="thumbnail-item" data-image-id="{{ image.id }}">
                                <img src="/media/{{ image.image }}" alt="{{ product.name }}" class="thumbnail-img">
                                <div class="thumbnail-actions">
                                    <button type="button" class="btn-delete-image" onclick="deleteProductImageFromModal({{ image.id }})" title="删除图片">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            {% empty %}
                            <p>暂无额外图片</p>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-save"></i> 保存修改
                </button>
                <button type="button" class="btn-secondary" onclick="closeEditModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </form>
    </div>
</div>
{% endif %}

<!-- 评价模态框 -->
{% if user.is_authenticated and not user.is_staff %}
<div id="reviewModal" class="modal">
    <div class="modal-content review-modal">
        <span class="close" onclick="closeReviewModal()">&times;</span>
        <h2>写评价</h2>
        <form id="reviewForm" method="post" enctype="multipart/form-data">
            {% csrf_token %}
            <div class="form-group">
                <label>商品评分</label>
                <div class="rating-input">
                    <input type="radio" name="score" value="5" id="star5" required>
                    <label for="star5" class="star-label">
                        <i class="fas fa-star"></i>
                    </label>
                    <input type="radio" name="score" value="4" id="star4">
                    <label for="star4" class="star-label">
                        <i class="fas fa-star"></i>
                    </label>
                    <input type="radio" name="score" value="3" id="star3">
                    <label for="star3" class="star-label">
                        <i class="fas fa-star"></i>
                    </label>
                    <input type="radio" name="score" value="2" id="star2">
                    <label for="star2" class="star-label">
                        <i class="fas fa-star"></i>
                    </label>
                    <input type="radio" name="score" value="1" id="star1">
                    <label for="star1" class="star-label">
                        <i class="fas fa-star"></i>
                    </label>
                </div>
                <div class="rating-text">
                    <span id="ratingText">请选择评分</span>
                </div>
            </div>

            <div class="form-group">
                <label for="review-content">评价内容</label>
                <textarea id="review-content" name="content" rows="5" placeholder="请分享您的使用体验..." required></textarea>
            </div>

            <div class="form-group">
                <label for="review-images">上传图片（可选）</label>
                <input type="file" id="review-images" name="images" accept="image/*" multiple>
                <div class="upload-tip">最多可上传5张图片</div>
            </div>

            <div class="form-group checkbox-group">
                <label>
                    <input type="checkbox" name="is_anonymous">
                    <span class="checkbox-label">匿名评价</span>
                </label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-paper-plane"></i> 提交评价
                </button>
                <button type="button" class="btn-secondary" onclick="closeReviewModal()">
                    <i class="fas fa-times"></i> 取消
                </button>
            </div>
        </form>
    </div>
</div>
{% endif %}

<!-- 图片查看模态框 -->
<div id="imageModal" class="modal">
    <div class="modal-content image-modal">
        <span class="close" onclick="closeImageModal()">&times;</span>
        <img id="modalImage" src="" alt="评价图片">
    </div>
</div>

<style>
/* 面包屑导航 */
.breadcrumb {
    background: #f8f9fa;
    padding: 12px 20px;
    margin-bottom: 20px;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
}

.breadcrumb a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s;
}

.breadcrumb a:hover {
    color: #0056b3;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #999;
}

.breadcrumb .current {
    color: #333;
    font-weight: 500;
}

/* 商品详情容器 */
.product-detail {
    display: flex;
    gap: 40px;
    margin-bottom: 40px;
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

/* 管理员工具栏 */
.admin-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
    margin-bottom: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.admin-toolbar h3 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.admin-actions {
    display: flex;
    gap: 12px;
}

.admin-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 10px 18px;
    border-radius: 8px;
    text-decoration: none;
    border: 2px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.admin-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.admin-btn.delete-btn:hover {
    background: rgba(220, 53, 69, 0.8);
    border-color: rgba(220, 53, 69, 0.8);
}

.admin-btn.edit-btn:hover {
    background: rgba(40, 167, 69, 0.8);
    border-color: rgba(40, 167, 69, 0.8);
}

.admin-btn.add-btn:hover {
    background: rgba(0, 123, 255, 0.8);
    border-color: rgba(0, 123, 255, 0.8);
}

.admin-btn.toggle-btn:hover {
    background: rgba(255, 193, 7, 0.8);
    border-color: rgba(255, 193, 7, 0.8);
}

/* 商品图片区域 */
.product-gallery {
    width: 450px;
    flex-shrink: 0;
}

.main-image-container {
    width: 100%;
    height: 450px;
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    background: #fafafa;
    overflow: hidden;
    position: relative;
}

/* 图片管理覆盖层 */
.image-admin-overlay {
    position: absolute;
    top: 10px;
    right: 10px;
    display: none;
    gap: 5px;
}

.main-image-container:hover .image-admin-overlay {
    display: flex;
}

.image-edit-btn {
    background: rgba(0, 123, 255, 0.8);
    color: white;
    border: none;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-edit-btn:hover {
    background: rgba(0, 123, 255, 1);
    transform: scale(1.1);
}

.main-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.main-image:hover {
    transform: scale(1.05);
}

.thumbnail-container {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 5px 0;
}

.thumbnail-container::-webkit-scrollbar {
    height: 6px;
}

.thumbnail-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.thumbnail-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.thumbnail {
    width: 80px;
    height: 80px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    transition: all 0.3s ease;
    background: white;
    flex-shrink: 0;
}

.thumbnail:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,123,255,0.3);
}

.thumbnail.active {
    border-color: #dc3545;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2);
}

.thumbnail img {
    max-width: 100%;
    max-height: 100%;
    object-fit: cover;
}

/* 缩略图管理覆盖层 */
.thumbnail-admin-overlay {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    gap: 3px;
    flex-direction: column;
}

.thumbnail:hover .thumbnail-admin-overlay {
    display: flex;
}

.thumbnail-edit-btn,
.thumbnail-delete-btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 4px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.thumbnail-edit-btn:hover {
    background: rgba(0, 123, 255, 0.9);
}

.thumbnail-delete-btn:hover {
    background: rgba(220, 53, 69, 0.9);
}

/* 模态框中的图片管理 */
.thumbnail-item {
    position: relative;
    display: inline-block;
    margin: 5px;
}

.thumbnail-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
}

.thumbnail-item:hover .thumbnail-actions {
    display: block;
}

.btn-delete-image {
    background: rgba(220, 53, 69, 0.8);
    color: white;
    border: none;
    padding: 4px 6px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.btn-delete-image:hover {
    background: rgba(220, 53, 69, 1);
    transform: scale(1.1);
}

/* 商品信息区域 */
.product-info {
    flex: 1;
    padding-left: 20px;
}

.product-title {
    font-size: 28px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
    line-height: 1.3;
}

.product-price-section {
    display: flex;
    align-items: baseline;
    margin-bottom: 25px;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.current-price {
    font-size: 32px;
    color: #dc3545;
    font-weight: 700;
    margin-right: 15px;
}

.original-price {
    font-size: 18px;
    color: #999;
    text-decoration: line-through;
    position: relative;
}

.original-price::after {
    content: "原价";
    position: absolute;
    top: -20px;
    left: 0;
    font-size: 12px;
    color: #666;
}

.product-meta {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px;
    margin-bottom: 25px;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.meta-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
}

.meta-row:last-child {
    margin-bottom: 0;
}

.meta-label {
    color: #666;
    font-weight: 500;
    min-width: 100px;
}

.meta-value {
    color: #333;
    font-weight: 600;
}

.hot-badge, .new-badge {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    display: inline-block;
}

.new-badge {
    background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

/* 商品操作区域 */
.product-actions {
    margin-top: 30px;
}

.quantity-section {
    margin-bottom: 25px;
}

.quantity-label {
    display: block;
    color: #333;
    font-weight: 500;
    margin-bottom: 10px;
}

.quantity-control {
    display: flex;
    align-items: center;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    width: fit-content;
    background: white;
}

.quantity-btn {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: none;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
}

.quantity-btn:hover {
    background: #e9ecef;
    color: #333;
}

.quantity-btn:active {
    transform: scale(0.95);
}

.quantity-input {
    width: 60px;
    height: 40px;
    text-align: center;
    border: none;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    background: white;
}

.quantity-input:focus {
    outline: none;
}

.action-buttons {
    display: flex;
    gap: 15px;
}

.add-to-cart, .buy-now {
    padding: 15px 30px;
    border-radius: 10px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.add-to-cart {
    background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 149, 0, 0.4);
}

.add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 149, 0, 0.6);
}

.buy-now {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.4);
}

.buy-now:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 53, 69, 0.6);
}

/* 商品详情选项卡 */
.product-tabs {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    margin-top: 30px;
    overflow: hidden;
}

.tab-header {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.tab {
    flex: 1;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #666;
    border-right: 1px solid #e9ecef;
    position: relative;
}

.tab:last-child {
    border-right: none;
}

.tab:hover {
    background: #e9ecef;
    color: #333;
}

.tab.active {
    background: white;
    color: #dc3545;
    border-bottom: 3px solid #dc3545;
}

.tab i {
    margin-right: 8px;
}

.tab-content {
    padding: 30px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.tab-pane h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
    border-bottom: 2px solid #dc3545;
    padding-bottom: 10px;
    display: inline-block;
}

/* 商品描述样式 */
.description-content {
    line-height: 1.8;
    color: #555;
    font-size: 15px;
}

/* 规格参数表格 */
.specs-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
}

.specs-table tr {
    border-bottom: 1px solid #f0f0f0;
}

.specs-table tr:last-child {
    border-bottom: none;
}

.spec-label {
    padding: 15px 20px;
    background: #f8f9fa;
    font-weight: 500;
    color: #666;
    width: 150px;
    border-right: 1px solid #f0f0f0;
}

.spec-value {
    padding: 15px 20px;
    color: #333;
}

/* 评价区域样式 */
.reviews-summary {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.rating-overview {
    text-align: center;
}

.average-rating {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.rating-score {
    font-size: 36px;
    font-weight: 700;
    color: #dc3545;
}

.rating-stars {
    color: #ffc107;
    font-size: 18px;
}

.rating-count {
    color: #666;
    font-size: 14px;
}

/* 评价区域样式 */
.reviews-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.btn-add-review {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-add-review:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
}

.review-item {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.review-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.review-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.reviewer-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.reviewer-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid #e9ecef;
}

.reviewer-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.reviewer-name {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.review-date {
    color: #666;
    font-size: 12px;
}

.review-rating {
    color: #ffc107;
    font-size: 16px;
}

.review-content {
    color: #555;
    line-height: 1.6;
    margin-bottom: 15px;
}

.review-images {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.review-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.review-image:hover {
    transform: scale(1.05);
}

.review-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-reviews {
    text-align: center;
    color: #999;
    padding: 60px 20px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.no-reviews i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #dee2e6;
}

.no-reviews p {
    font-size: 16px;
    margin: 0;
}

.reviews-pagination {
    margin-top: 30px;
    text-align: center;
}

.pagination {
    display: inline-flex;
    align-items: center;
    gap: 15px;
}

.page-link {
    background: #007bff;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.page-link:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

.page-info {
    color: #666;
    font-size: 14px;
}

/* 评价模态框样式 */
.review-modal {
    max-width: 600px;
    width: 90%;
}

.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    gap: 5px;
    margin-bottom: 10px;
}

.rating-input input[type="radio"] {
    display: none;
}

.star-label {
    font-size: 24px;
    color: #ddd;
    cursor: pointer;
    transition: color 0.3s ease;
}

.rating-input input[type="radio"]:checked ~ .star-label,
.rating-input input[type="radio"]:hover ~ .star-label,
.star-label:hover {
    color: #ffc107;
}

.rating-text {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
}

.upload-tip {
    color: #666;
    font-size: 12px;
    margin-top: 5px;
}

/* 图片查看模态框 */
.image-modal {
    max-width: 90%;
    max-height: 90%;
    padding: 20px;
    text-align: center;
}

.image-modal img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 8px;
}

/* 模态框通用样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 5% auto;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    color: #999;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .product-detail {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
    }

    .product-gallery {
        width: 100%;
    }

    .main-image-container {
        height: 300px;
    }

    .product-info {
        padding-left: 0;
    }

    .product-title {
        font-size: 24px;
    }

    .current-price {
        font-size: 28px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .add-to-cart, .buy-now {
        width: 100%;
        justify-content: center;
    }

    .tab-header {
        flex-direction: column;
    }

    .tab {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
    }

    .tab:last-child {
        border-bottom: none;
    }

    .breadcrumb {
        padding: 10px 15px;
        font-size: 12px;
    }

    .admin-toolbar {
        padding: 15px;
    }

    .admin-actions {
        flex-direction: column;
        gap: 8px;
    }
}

.product-tabs {
    margin-top: 30px;
}

.tab-header {
    display: flex;
    border-bottom: 2px solid #e4393c;
}

.tab {
    padding: 10px 20px;
    cursor: pointer;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-bottom: none;
    margin-right: 5px;
}

.tab.active {
    background: #e4393c;
    color: white;
    border-color: #e4393c;
}

.tab-content {
    padding: 20px;
    border: 1px solid #ddd;
    border-top: none;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

.product-description {
    line-height: 1.8;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    overflow-y: auto;
}

.modal-content {
    background: white;
    margin: 5% auto;
    padding: 20px;
    width: 80%;
    max-width: 800px;
    border-radius: 5px;
    position: relative;
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 24px;
    cursor: pointer;
}

.form-section {
    margin-bottom: 30px;
}

.section-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.half {
    flex: 1;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
}

.current-image,
.current-images {
    margin-top: 10px;
}

.thumbnail-img {
    max-width: 100px;
    max-height: 100px;
    border: 1px solid #eee;
    margin-top: 5px;
}

.thumbnail-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 5px;
}

.thumbnail-item {
    width: 80px;
    height: 80px;
    border: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.btn-primary {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.btn-secondary {
    background: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.add-to-favorite {
    display: inline-block;
    background: white;
    color: #e41c3c;
    border: 2px solid rgba(228, 28, 60, 0.2);
    padding: 10px 20px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    margin-left: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.add-to-favorite:hover {
    background: #fff5f7;
    border-color: #e41c3c;
    color: #e41c3c;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(228, 28, 60, 0.15);
    text-decoration: none;
}

.add-to-favorite.active {
    background: #fff5f7;
    border-color: #e41c3c;
    color: #e41c3c;
}

.add-to-favorite.clicked {
    transform: scale(0.95) !important;
    opacity: 0.9 !important;
    box-shadow: 0 2px 5px rgba(228, 28, 60, 0.15) !important;
}

.add-to-favorite .fas.fa-heart {
    animation: heartBeat 0.5s ease-in-out;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.3); }
    50% { transform: scale(1); }
    75% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* 添加收藏结果提示样式 */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.toast-message.success {
    background-color: #28a745;
}

.toast-message.error {
    background-color: #dc3545;
}

.toast-message.info {
    background-color: #17a2b8;
}

.toast-message.fade-out {
    animation: fadeOut 0.3s ease forwards;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeOut {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}
</style>

<script>
// 切换主图
function changeMainImage(src, thumbnail) {
    const mainImage = document.getElementById('main-image');
    mainImage.src = src;

    // 更新缩略图激活状态
    const thumbnails = document.querySelectorAll('.thumbnail');
    thumbnails.forEach(item => {
        item.classList.remove('active');
    });

    if (thumbnail) {
        thumbnail.classList.add('active');
    }
}

// 数量控制
function increaseQuantity() {
    const input = document.getElementById('quantity');
    const max = parseInt(input.getAttribute('max')) || 999;
    let value = parseInt(input.value) || 1;

    if (value < max) {
        input.value = value + 1;
    }
}

function decreaseQuantity() {
    const input = document.getElementById('quantity');
    let value = parseInt(input.value) || 1;

    if (value > 1) {
        input.value = value - 1;
    }
}

// 加入购物车
function addToCart(productId) {
    const quantity = document.getElementById('quantity').value;

    // 添加加载效果
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 添加中...';
    button.disabled = true;

    // 模拟添加到购物车
    setTimeout(() => {
        showSystemMessage(`已添加 ${quantity} 个商品到购物车`, '购物车', 'success');
        button.innerHTML = originalText;
        button.disabled = false;
    }, 1000);
}

// 更新立即购买的数量
function updateBuyQuantity() {
    const quantity = document.getElementById('quantity').value;
    document.getElementById('buy-quantity').value = quantity;
    console.log('更新立即购买数量:', quantity);
}

// 立即购买（备用AJAX版本）
function buyNowAjax(productId) {
    console.log('立即购买被点击，商品ID:', productId);

    // 检查用户是否登录
    if (!isUserLoggedIn()) {
        showSystemMessage('请先登录', '用户验证', 'warning');
        window.location.href = '/users/login/';
        return;
    }

    const quantity = document.getElementById('quantity').value;
    const button = event.target;
    const originalText = button.innerHTML;

    // 显示加载状态
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在处理...';
    button.disabled = true;

    // 获取CSRF token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    if (!csrfToken) {
        console.error('CSRF token not found');
        showSystemMessage('页面错误，请刷新后重试', '系统错误', 'error');
        button.innerHTML = originalText;
        button.disabled = false;
        return;
    }

    console.log('发送立即购买请求...');

    // 发送立即购买请求
    fetch('/order/buy-now/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken
        },
        body: `product_id=${productId}&quantity=${quantity}&receiver=${encodeURIComponent('默认收货人')}&receiver_mobile=&address=${encodeURIComponent('默认地址')}`
    })
    .then(response => {
        console.log('收到响应:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('响应数据:', data);
        if (data.success) {
            // 立即跳转，不延迟
            console.log('跳转到:', data.redirect_url);
            window.location.href = data.redirect_url;
        } else {
            throw new Error(data.msg || '创建订单失败');
        }
    })
    .catch(error => {
        console.error('立即购买错误:', error);
        showSystemMessage(error.message || '立即购买失败，请重试', '订单创建失败', 'error');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 检查用户是否登录
function isUserLoggedIn() {
    // 检查页面中是否有用户信息
    return document.querySelector('.user-info') !== null ||
           document.querySelector('[data-user-authenticated="true"]') !== null ||
           !document.querySelector('a[href*="login"]');
}

// 选项卡切换
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.tab');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有激活状态
            tabs.forEach(t => t.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));

            // 激活当前选项卡
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            const targetPane = document.getElementById(tabId);
            if (targetPane) {
                targetPane.classList.add('active');
            }
        });
    });

    // 表单提交处理
    const editForm = document.getElementById('editProductForm');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveProductChanges();
        });
    }
});

// 模态框控制
function openEditModal(button) {
    const modal = document.getElementById('editModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeEditModal() {
    const modal = document.getElementById('editModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const modal = document.getElementById('editModal');
    if (event.target === modal) {
        closeEditModal();
    }
});

// 保存商品修改 - 使用AJAX避免页面跳转
function saveProductChanges() {
    const form = document.getElementById('editProductForm');
    const formData = new FormData(form);
    const submitBtn = form.querySelector('.btn-primary');

    // 添加加载状态
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 保存中...';
    submitBtn.disabled = true;

    // 使用fetch发送AJAX请求
    fetch(`{% url 'goods:update_product' product.id %}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSystemMessage('商品信息保存成功！', '保存成功', 'success');
            closeEditModal();
            // 刷新页面以显示更新后的信息
            window.location.reload();
        } else {
            showSystemMessage('保存失败：' + (data.error || '未知错误'), '保存失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showSystemMessage('保存失败，请稍后重试', '系统错误', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// ESC键关闭模态框
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        closeEditModal();
        closeReviewModal();
        closeImageModal();
    }
});

// 评价模态框控制
function openReviewModal() {
    const modal = document.getElementById('reviewModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeReviewModal() {
    const modal = document.getElementById('reviewModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
        // 重置表单
        document.getElementById('reviewForm').reset();
        updateRatingText();
    }
}

// 图片查看模态框
function openImageModal(imageSrc) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    if (modal && modalImage) {
        modalImage.src = imageSrc;
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// 评分星级交互
document.addEventListener('DOMContentLoaded', function() {
    const ratingInputs = document.querySelectorAll('.rating-input input[type="radio"]');
    const ratingText = document.getElementById('ratingText');

    const ratingTexts = {
        '1': '很差',
        '2': '差',
        '3': '一般',
        '4': '好',
        '5': '很好'
    };

    ratingInputs.forEach(input => {
        input.addEventListener('change', function() {
            updateRatingText();
        });
    });

    function updateRatingText() {
        const checkedInput = document.querySelector('.rating-input input[type="radio"]:checked');
        if (checkedInput && ratingText) {
            ratingText.textContent = ratingTexts[checkedInput.value];
        } else if (ratingText) {
            ratingText.textContent = '请选择评分';
        }
    }

    // 评价表单提交
    const reviewForm = document.getElementById('reviewForm');
    if (reviewForm) {
        reviewForm.addEventListener('submit', function(e) {
            e.preventDefault();
            submitReview();
        });
    }
});

// 提交评价
function submitReview() {
    const form = document.getElementById('reviewForm');
    const formData = new FormData(form);
    const submitBtn = form.querySelector('.btn-primary');

    // 添加加载状态
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
    submitBtn.disabled = true;

    // 使用fetch发送AJAX请求
    fetch(`{% url 'reviews:add' product.id %}`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSystemMessage('评价提交成功！', '评价提交', 'success');
            closeReviewModal();
            // 刷新页面以显示新评价
            window.location.reload();
        } else {
            showSystemMessage('提交失败：' + (data.error || '未知错误'), '评价提交失败', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showSystemMessage('提交失败，请稍后重试', '系统错误', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// 删除商品
function deleteProduct(button) {
    const productId = button.getAttribute('data-product-id');

    if (confirm('确定要删除这个商品吗？删除后无法恢复！')) {
        fetch(`{% url 'goods:delete_product' 0 %}`.replace('0', productId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSystemMessage(data.message || '商品删除成功！', '商品删除', 'success');
                // 跳转到商品列表页面
                window.location.href = '{% url "goods:list" %}';
            } else {
                showSystemMessage('删除失败：' + (data.error || '未知错误'), '商品删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSystemMessage('删除失败，请稍后重试', '系统错误', 'error');
        });
    }
}

// 切换商品状态（上架/下架）
function toggleProductStatus(button) {
    const productId = button.getAttribute('data-product-id');
    const isActive = button.innerHTML.includes('下架');
    const action = isActive ? '下架' : '上架';

    if (confirm(`确定要${action}这个商品吗？`)) {
        const formData = new FormData();
        formData.append('is_active', !isActive);

        fetch(`{% url 'goods:update_product' 0 %}`.replace('0', productId), {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSystemMessage(`商品${action}成功！`, '商品状态更新', 'success');
                window.location.reload();
            } else {
                showSystemMessage(`${action}失败：` + (data.error || '未知错误'), '商品状态更新失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSystemMessage(`${action}失败，请稍后重试`, '系统错误', 'error');
        });
    }
}

// 删除商品图片
function deleteProductImage(imageId) {
    if (confirm('确定要删除这张图片吗？')) {
        fetch(`{% url 'goods:delete_product_image' 0 %}`.replace('0', imageId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSystemMessage(data.message || '图片删除成功！', '图片删除', 'success');
                window.location.reload();
            } else {
                showSystemMessage('删除失败：' + (data.error || '未知错误'), '图片删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSystemMessage('删除失败，请稍后重试', '系统错误', 'error');
        });
    }
}

// 从模态框中删除商品图片
function deleteProductImageFromModal(imageId) {
    if (confirm('确定要删除这张图片吗？')) {
        fetch(`{% url 'goods:delete_product_image' 0 %}`.replace('0', imageId), {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 从DOM中移除图片元素
                const imageItem = document.querySelector(`[data-image-id="${imageId}"]`);
                if (imageItem) {
                    imageItem.remove();
                }
                showSystemMessage(data.message || '图片删除成功！', '图片删除', 'success');
            } else {
                showSystemMessage('删除失败：' + (data.error || '未知错误'), '图片删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSystemMessage('删除失败，请稍后重试', '系统错误', 'error');
        });
    }
}

// 点击模态框外部关闭
window.addEventListener('click', function(event) {
    const editModal = document.getElementById('editModal');
    const reviewModal = document.getElementById('reviewModal');
    const imageModal = document.getElementById('imageModal');

    if (event.target === editModal) {
        closeEditModal();
    }
    if (event.target === reviewModal) {
        closeReviewModal();
    }
    if (event.target === imageModal) {
        closeImageModal();
    }
});

// 添加收藏功能的AJAX处理
document.addEventListener('DOMContentLoaded', function() {
    const favoriteForm = document.getElementById('favoriteForm');
    if (favoriteForm) {
        favoriteForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const favoriteBtn = document.getElementById('favoriteBtn');
            const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
            const productId = favoriteBtn.getAttribute('data-product-id');
            const isFavorite = favoriteBtn.getAttribute('data-is-favorite') === 'true';

            // 添加点击动画效果
            favoriteBtn.classList.add('clicked');
            setTimeout(function() {
                favoriteBtn.classList.remove('clicked');
            }, 300);

            // 显示加载状态
            const originalText = favoriteBtn.innerHTML;
            favoriteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
            favoriteBtn.disabled = true;

            fetch(this.action, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                body: new URLSearchParams(new FormData(this)),
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                console.log('收藏响应:', data);

                // 显示提示信息
                const toast = document.createElement('div');
                toast.className = 'toast-message ' + data.status;
                toast.innerHTML = `<i class="fas fa-${data.status === 'success' ? 'check-circle' : data.status === 'error' ? 'exclamation-circle' : 'info-circle'}"></i> ${data.message}`;
                document.body.appendChild(toast);

                // 2秒后移除提示
                setTimeout(function() {
                    toast.classList.add('fade-out');
                    setTimeout(function() {
                        document.body.removeChild(toast);
                    }, 500);
                }, 2000);

                // 更新按钮状态和表单action
                if (data.status === 'success') {
                    if (isFavorite) {
                        // 取消收藏成功
                        favoriteBtn.innerHTML = '<i class="far fa-heart"></i> 收藏';
                        favoriteBtn.classList.remove('active');
                        favoriteBtn.setAttribute('data-is-favorite', 'false');
                        favoriteForm.action = `/account/add-favorite/${productId}/`;
                    } else {
                        // 添加收藏成功
                        favoriteBtn.innerHTML = '<i class="fas fa-heart"></i> 已收藏';
                        favoriteBtn.classList.add('active');
                        favoriteBtn.setAttribute('data-is-favorite', 'true');
                        favoriteForm.action = `/account/remove-from-favorite/${productId}/`;

                        // 添加心跳动画
                        const heartIcon = favoriteBtn.querySelector('i');
                        heartIcon.style.animation = 'none';
                        setTimeout(function() {
                            heartIcon.style.animation = 'heartBeat 0.5s ease-in-out';
                        }, 10);
                    }
                } else if (data.status === 'info') {
                    // 商品已在收藏夹中
                    favoriteBtn.innerHTML = '<i class="fas fa-heart"></i> 已收藏';
                    favoriteBtn.classList.add('active');
                    favoriteBtn.setAttribute('data-is-favorite', 'true');
                    favoriteForm.action = `/account/remove-from-favorite/${productId}/`;
                } else {
                    // 错误情况，恢复原状态
                    favoriteBtn.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('收藏错误:', error);
                // 显示错误提示
                const toast = document.createElement('div');
                toast.className = 'toast-message error';
                toast.innerHTML = '<i class="fas fa-exclamation-circle"></i> 收藏失败，请稍后重试';
                document.body.appendChild(toast);

                // 2秒后移除提示
                setTimeout(function() {
                    toast.classList.add('fade-out');
                    setTimeout(function() {
                        document.body.removeChild(toast);
                    }, 500);
                }, 2000);

                // 恢复按钮状态
                favoriteBtn.innerHTML = originalText;
            })
            .finally(() => {
                // 恢复按钮状态
                favoriteBtn.disabled = false;
            });
        });
    }
});
</script>
{% endblock %} 