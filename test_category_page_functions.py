#!/usr/bin/env python3
"""
测试分类页面的收藏和购物车功能
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from users.models import User
from goods.models import Product, Category
from account.models import Favorite
from order.models import Cart

def test_category_page_data():
    """测试分类页面数据"""
    print("🧪 测试分类页面数据...")
    print("=" * 60)
    
    # 检查分类297是否存在
    try:
        category = Category.objects.get(id=297, is_active=True)
        print(f"✅ 找到分类: {category.name} (ID: {category.id})")
    except Category.DoesNotExist:
        print("❌ 分类297不存在或未激活")
        # 查找其他可用分类
        categories = Category.objects.filter(is_active=True)[:5]
        print(f"📋 可用分类:")
        for cat in categories:
            print(f"   - {cat.name} (ID: {cat.id})")
        return None
    
    # 检查该分类下的商品
    if category.children.exists():
        category_ids = [category.id] + list(category.children.values_list('id', flat=True))
        products = Product.objects.filter(category_id__in=category_ids, is_active=True)
    else:
        products = Product.objects.filter(category=category, is_active=True)
    
    print(f"📦 分类下商品数量: {products.count()}")
    
    if products.exists():
        for product in products[:3]:  # 显示前3个商品
            print(f"   - {product.name} (ID: {product.id}) - ¥{product.price}")
    else:
        print("⚠️ 该分类下没有激活的商品")
    
    return category, products

def test_user_authentication():
    """测试用户认证"""
    print("\n👤 测试用户认证...")
    print("=" * 60)
    
    # 查找测试用户
    test_users = User.objects.filter(username__in=['testuser', '1218', 'admin'])
    
    if test_users.exists():
        for user in test_users:
            print(f"✅ 找到用户: {user.username}")
            print(f"   邮箱: {user.email}")
            print(f"   是否激活: {'是' if user.is_active else '否'}")
            print(f"   是否认证: {'是' if user.is_authenticated else '否'}")
            
            # 检查用户的收藏
            favorites = Favorite.objects.filter(user=user)
            print(f"   收藏数量: {favorites.count()}")
            
            # 检查用户的购物车
            cart_items = Cart.objects.filter(user=user)
            print(f"   购物车商品: {cart_items.count()}")
            print()
        
        return test_users.first()
    else:
        print("❌ 没有找到测试用户")
        return None

def test_favorite_functionality():
    """测试收藏功能"""
    print("❤️ 测试收藏功能...")
    print("=" * 60)
    
    user = test_user_authentication()
    if not user:
        return False
    
    category_data = test_category_page_data()
    if not category_data:
        return False
    
    category, products = category_data
    
    if not products.exists():
        print("❌ 没有商品可以测试收藏功能")
        return False
    
    test_product = products.first()
    print(f"🧪 测试商品: {test_product.name}")
    
    # 检查是否已收藏
    is_favorite = Favorite.objects.filter(user=user, product=test_product).exists()
    print(f"当前收藏状态: {'已收藏' if is_favorite else '未收藏'}")
    
    # 测试添加收藏
    if not is_favorite:
        try:
            favorite = Favorite.objects.create(user=user, product=test_product)
            print(f"✅ 成功添加收藏")
        except Exception as e:
            print(f"❌ 添加收藏失败: {e}")
            return False
    
    # 检查收藏数量
    favorite_count = Favorite.objects.filter(product=test_product).count()
    print(f"商品收藏数量: {favorite_count}")
    
    return True

def test_cart_functionality():
    """测试购物车功能"""
    print("\n🛒 测试购物车功能...")
    print("=" * 60)
    
    user = test_user_authentication()
    if not user:
        return False
    
    category_data = test_category_page_data()
    if not category_data:
        return False
    
    category, products = category_data
    
    if not products.exists():
        print("❌ 没有商品可以测试购物车功能")
        return False
    
    test_product = products.first()
    print(f"🧪 测试商品: {test_product.name}")
    print(f"商品库存: {test_product.stock}")
    
    # 检查是否已在购物车
    cart_item = Cart.objects.filter(user=user, product=test_product).first()
    if cart_item:
        print(f"当前购物车数量: {cart_item.quantity}")
    else:
        print("当前购物车状态: 未添加")
    
    # 测试添加到购物车
    if test_product.stock > 0:
        try:
            cart_item, created = Cart.objects.get_or_create(
                user=user,
                product=test_product,
                defaults={'quantity': 1}
            )
            
            if created:
                print(f"✅ 成功添加到购物车")
            else:
                cart_item.quantity += 1
                cart_item.save()
                print(f"✅ 购物车数量更新为: {cart_item.quantity}")
                
        except Exception as e:
            print(f"❌ 添加到购物车失败: {e}")
            return False
    else:
        print("⚠️ 商品库存不足，无法添加到购物车")
    
    return True

def check_urls():
    """检查URL配置"""
    print("\n🔗 检查URL配置...")
    print("=" * 60)
    
    from django.urls import reverse
    
    try:
        # 检查收藏相关URL
        add_favorite_url = reverse('account:add_favorite', args=[1])
        print(f"✅ 添加收藏URL: {add_favorite_url}")
        
        remove_favorite_url = reverse('account:remove_from_favorite', args=[1])
        print(f"✅ 移除收藏URL: {remove_favorite_url}")
        
        # 检查购物车相关URL
        cart_add_url = reverse('order:cart_add')
        print(f"✅ 添加购物车URL: {cart_add_url}")
        
        cart_url = reverse('order:cart')
        print(f"✅ 购物车页面URL: {cart_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ URL配置错误: {e}")
        return False

def show_test_instructions():
    """显示测试说明"""
    print("\n📋 手动测试说明:")
    print("=" * 60)
    print("1. 确保前台服务器运行: python manage.py runserver 127.0.0.1:8001")
    print("2. 访问分类页面: http://127.0.0.1:8001/goods/category/297/")
    print("3. 登录用户账户 (如果未登录)")
    print("4. 测试点击爱心图标 (收藏功能)")
    print("5. 测试点击购物车图标 (添加到购物车)")
    print("6. 检查浏览器控制台是否有JavaScript错误")
    print("7. 检查网络请求是否成功发送")

if __name__ == "__main__":
    print("🚀 开始测试分类页面功能...")
    print("=" * 80)
    
    try:
        # 检查URL配置
        urls_ok = check_urls()
        
        # 测试数据
        category_data = test_category_page_data()
        user = test_user_authentication()
        
        # 测试功能
        favorite_ok = test_favorite_functionality()
        cart_ok = test_cart_functionality()
        
        # 显示测试结果
        print("\n🎯 测试结果总结:")
        print("=" * 60)
        print(f"URL配置: {'✅ 正常' if urls_ok else '❌ 异常'}")
        print(f"分类数据: {'✅ 正常' if category_data else '❌ 异常'}")
        print(f"用户认证: {'✅ 正常' if user else '❌ 异常'}")
        print(f"收藏功能: {'✅ 正常' if favorite_ok else '❌ 异常'}")
        print(f"购物车功能: {'✅ 正常' if cart_ok else '❌ 异常'}")
        
        if all([urls_ok, category_data, user, favorite_ok, cart_ok]):
            print("\n🎉 所有功能测试通过！")
            print("如果页面上的按钮仍然无法点击，可能是前端JavaScript问题。")
        else:
            print("\n⚠️ 部分功能存在问题，请检查上述错误信息。")
        
        # 显示测试说明
        show_test_instructions()
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
