#!/usr/bin/env python3
"""
MARS BUY 前台功能测试脚本
以普通用户身份测试所有前台功能
"""

import requests
import time
import json
from urllib.parse import urljoin

class FrontendTester:
    def __init__(self, base_url="http://127.0.0.1:8001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, success, message="", response_code=None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'response_code': response_code,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        print(f"{status} {test_name} - {message}")
        
    def test_homepage(self):
        """测试首页"""
        try:
            response = self.session.get(self.base_url)
            if response.status_code == 200:
                content = response.text
                # 检查关键元素
                checks = [
                    ("MARS BUY" in content, "网站标题"),
                    ("轮播图" in content or "carousel" in content, "轮播图"),
                    ("限时秒杀" in content or "flash" in content, "限时秒杀"),
                    ("热门商品" in content, "热门商品"),
                    ("新品上市" in content, "新品上市"),
                ]
                
                all_passed = all(check[0] for check in checks)
                failed_checks = [check[1] for check in checks if not check[0]]
                
                if all_passed:
                    self.log_test("首页加载", True, "所有关键元素正常显示", response.status_code)
                else:
                    self.log_test("首页加载", False, f"缺失元素: {', '.join(failed_checks)}", response.status_code)
            else:
                self.log_test("首页加载", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("首页加载", False, f"请求异常: {str(e)}")
    
    def test_product_list(self):
        """测试商品列表页面"""
        try:
            response = self.session.get(urljoin(self.base_url, "/goods/products/"))
            if response.status_code == 200:
                content = response.text
                checks = [
                    ("商品" in content, "商品显示"),
                    ("分页" in content or "page" in content, "分页功能"),
                    ("排序" in content or "sort" in content, "排序功能"),
                ]
                
                all_passed = all(check[0] for check in checks)
                if all_passed:
                    self.log_test("商品列表页", True, "页面正常加载", response.status_code)
                else:
                    failed_checks = [check[1] for check in checks if not check[0]]
                    self.log_test("商品列表页", False, f"缺失功能: {', '.join(failed_checks)}", response.status_code)
            else:
                self.log_test("商品列表页", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("商品列表页", False, f"请求异常: {str(e)}")
    
    def test_category_pages(self):
        """测试分类页面"""
        # 测试几个常见的分类ID
        category_ids = [290, 291, 292, 293, 294]  # 根据实际分类ID调整
        
        for cat_id in category_ids:
            try:
                response = self.session.get(urljoin(self.base_url, f"/goods/category/{cat_id}/"))
                if response.status_code == 200:
                    self.log_test(f"分类页面 {cat_id}", True, "页面正常加载", response.status_code)
                elif response.status_code == 404:
                    self.log_test(f"分类页面 {cat_id}", True, "分类不存在(正常)", response.status_code)
                else:
                    self.log_test(f"分类页面 {cat_id}", False, f"HTTP错误", response.status_code)
            except Exception as e:
                self.log_test(f"分类页面 {cat_id}", False, f"请求异常: {str(e)}")
    
    def test_search_functionality(self):
        """测试搜索功能"""
        search_terms = ["手机", "电脑", "衣服", "食品"]
        
        for term in search_terms:
            try:
                response = self.session.get(urljoin(self.base_url, f"/goods/search/?q={term}"))
                if response.status_code == 200:
                    self.log_test(f"搜索 '{term}'", True, "搜索正常", response.status_code)
                else:
                    self.log_test(f"搜索 '{term}'", False, f"HTTP错误", response.status_code)
            except Exception as e:
                self.log_test(f"搜索 '{term}'", False, f"请求异常: {str(e)}")
    
    def test_user_pages(self):
        """测试用户相关页面"""
        user_pages = [
            ("/users/login/", "登录页面"),
            ("/users/register/", "注册页面"),
            ("/users/center/", "用户中心"),
            ("/users/profile/", "个人资料"),
            ("/users/orders/", "订单列表"),
            ("/users/address/", "地址管理"),
            ("/users/password/", "修改密码"),
        ]
        
        for url, name in user_pages:
            try:
                response = self.session.get(urljoin(self.base_url, url))
                if response.status_code in [200, 302]:  # 302可能是重定向到登录
                    self.log_test(name, True, "页面可访问", response.status_code)
                else:
                    self.log_test(name, False, f"HTTP错误", response.status_code)
            except Exception as e:
                self.log_test(name, False, f"请求异常: {str(e)}")
    
    def test_order_pages(self):
        """测试订单相关页面"""
        order_pages = [
            ("/order/cart/", "购物车"),
            ("/order/list/", "订单列表"),
        ]
        
        for url, name in order_pages:
            try:
                response = self.session.get(urljoin(self.base_url, url))
                if response.status_code in [200, 302]:
                    self.log_test(name, True, "页面可访问", response.status_code)
                else:
                    self.log_test(name, False, f"HTTP错误", response.status_code)
            except Exception as e:
                self.log_test(name, False, f"请求异常: {str(e)}")
    
    def test_account_pages(self):
        """测试账户相关页面"""
        account_pages = [
            ("/account/favorites/", "收藏夹"),
        ]
        
        for url, name in account_pages:
            try:
                response = self.session.get(urljoin(self.base_url, url))
                if response.status_code in [200, 302]:
                    self.log_test(name, True, "页面可访问", response.status_code)
                else:
                    self.log_test(name, False, f"HTTP错误", response.status_code)
            except Exception as e:
                self.log_test(name, False, f"请求异常: {str(e)}")
    
    def test_api_endpoints(self):
        """测试API端点"""
        api_endpoints = [
            ("/goods/api/check_updates/", "商品更新检查API"),
            ("/goods/api/latest_products/", "最新商品API"),
        ]
        
        for url, name in api_endpoints:
            try:
                response = self.session.get(urljoin(self.base_url, url))
                if response.status_code == 200:
                    try:
                        json.loads(response.text)
                        self.log_test(name, True, "API正常返回JSON", response.status_code)
                    except json.JSONDecodeError:
                        self.log_test(name, False, "API返回非JSON格式", response.status_code)
                else:
                    self.log_test(name, False, f"HTTP错误", response.status_code)
            except Exception as e:
                self.log_test(name, False, f"请求异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始MARS BUY前台功能测试...")
        print("=" * 60)
        
        # 运行各项测试
        self.test_homepage()
        self.test_product_list()
        self.test_category_pages()
        self.test_search_functionality()
        self.test_user_pages()
        self.test_order_pages()
        self.test_account_pages()
        self.test_api_endpoints()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if "✅" in r['status']])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 测试结果统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if "❌" in result['status']:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎉 测试完成!")
        return passed_tests, failed_tests

if __name__ == "__main__":
    tester = FrontendTester()
    passed, failed = tester.run_all_tests()
    
    # 如果有失败的测试，退出码为1
    exit(0 if failed == 0 else 1)
