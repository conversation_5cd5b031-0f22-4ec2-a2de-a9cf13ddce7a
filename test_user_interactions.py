#!/usr/bin/env python3
"""
MARS BUY 用户交互功能测试脚本
测试用户登录后的各种交互功能
"""

import requests
import time
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

class UserInteractionTester:
    def __init__(self, base_url="http://127.0.0.1:8001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.csrf_token = None
        
    def log_test(self, test_name, success, message="", response_code=None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = {
            'test': test_name,
            'status': status,
            'message': message,
            'response_code': response_code,
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        self.test_results.append(result)
        print(f"{status} {test_name} - {message}")
    
    def get_csrf_token(self, url):
        """获取CSRF令牌"""
        try:
            response = self.session.get(url)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
                if csrf_input:
                    return csrf_input.get('value')
        except Exception as e:
            print(f"获取CSRF令牌失败: {e}")
        return None
    
    def test_user_login(self):
        """测试用户登录功能"""
        try:
            # 获取登录页面和CSRF令牌
            login_url = urljoin(self.base_url, "/users/login/")
            csrf_token = self.get_csrf_token(login_url)
            
            if not csrf_token:
                self.log_test("用户登录", False, "无法获取CSRF令牌")
                return False
            
            # 尝试使用测试用户登录
            login_data = {
                'username': 'testuser',  # 使用我们创建的测试用户
                'password': '123456',
                'user_type': 'normal',
                'csrfmiddlewaretoken': csrf_token
            }
            
            response = self.session.post(login_url, data=login_data)
            
            if response.status_code == 302:  # 重定向表示登录成功
                self.log_test("用户登录", True, "登录成功", response.status_code)
                return True
            elif response.status_code == 200:
                # 检查是否有错误信息
                if "错误" in response.text or "失败" in response.text:
                    self.log_test("用户登录", False, "登录失败 - 用户名或密码错误", response.status_code)
                else:
                    self.log_test("用户登录", True, "登录页面正常显示", response.status_code)
                return False
            else:
                self.log_test("用户登录", False, f"HTTP错误", response.status_code)
                return False
        except Exception as e:
            self.log_test("用户登录", False, f"请求异常: {str(e)}")
            return False
    
    def test_product_detail_access(self):
        """测试商品详情页面访问"""
        try:
            # 先获取商品列表，找到一个商品ID
            products_response = self.session.get(urljoin(self.base_url, "/goods/products/"))
            if products_response.status_code == 200:
                soup = BeautifulSoup(products_response.text, 'html.parser')
                # 查找商品链接
                product_links = soup.find_all('a', href=lambda x: x and '/goods/product/' in x)
                
                if product_links:
                    product_url = urljoin(self.base_url, product_links[0]['href'])
                    detail_response = self.session.get(product_url)
                    
                    if detail_response.status_code == 200:
                        self.log_test("商品详情页", True, "商品详情页正常访问", detail_response.status_code)
                        return True
                    else:
                        self.log_test("商品详情页", False, f"HTTP错误", detail_response.status_code)
                else:
                    self.log_test("商品详情页", False, "未找到商品链接")
            else:
                self.log_test("商品详情页", False, "无法获取商品列表")
        except Exception as e:
            self.log_test("商品详情页", False, f"请求异常: {str(e)}")
        return False
    
    def test_cart_functionality(self):
        """测试购物车功能"""
        try:
            cart_url = urljoin(self.base_url, "/order/cart/")
            response = self.session.get(cart_url)
            
            if response.status_code == 200:
                content = response.text
                if "购物车" in content:
                    self.log_test("购物车功能", True, "购物车页面正常显示", response.status_code)
                else:
                    self.log_test("购物车功能", False, "购物车页面内容异常", response.status_code)
            else:
                self.log_test("购物车功能", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("购物车功能", False, f"请求异常: {str(e)}")
    
    def test_favorites_functionality(self):
        """测试收藏功能"""
        try:
            favorites_url = urljoin(self.base_url, "/account/favorites/")
            response = self.session.get(favorites_url)
            
            if response.status_code == 200:
                content = response.text
                if "收藏" in content:
                    self.log_test("收藏功能", True, "收藏页面正常显示", response.status_code)
                else:
                    self.log_test("收藏功能", False, "收藏页面内容异常", response.status_code)
            else:
                self.log_test("收藏功能", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("收藏功能", False, f"请求异常: {str(e)}")
    
    def test_user_profile(self):
        """测试用户资料功能"""
        try:
            profile_url = urljoin(self.base_url, "/users/profile/")
            response = self.session.get(profile_url)
            
            if response.status_code == 200:
                content = response.text
                if "个人资料" in content or "用户信息" in content:
                    self.log_test("用户资料", True, "用户资料页面正常显示", response.status_code)
                else:
                    self.log_test("用户资料", False, "用户资料页面内容异常", response.status_code)
            else:
                self.log_test("用户资料", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("用户资料", False, f"请求异常: {str(e)}")
    
    def test_order_history(self):
        """测试订单历史功能"""
        try:
            orders_url = urljoin(self.base_url, "/users/orders/")
            response = self.session.get(orders_url)
            
            if response.status_code == 200:
                content = response.text
                if "订单" in content:
                    self.log_test("订单历史", True, "订单历史页面正常显示", response.status_code)
                else:
                    self.log_test("订单历史", False, "订单历史页面内容异常", response.status_code)
            else:
                self.log_test("订单历史", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("订单历史", False, f"请求异常: {str(e)}")
    
    def test_address_management(self):
        """测试地址管理功能"""
        try:
            address_url = urljoin(self.base_url, "/users/address/")
            response = self.session.get(address_url)
            
            if response.status_code == 200:
                content = response.text
                if "地址" in content:
                    self.log_test("地址管理", True, "地址管理页面正常显示", response.status_code)
                else:
                    self.log_test("地址管理", False, "地址管理页面内容异常", response.status_code)
            else:
                self.log_test("地址管理", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("地址管理", False, f"请求异常: {str(e)}")
    
    def test_search_with_filters(self):
        """测试搜索和筛选功能"""
        search_tests = [
            ("手机", "搜索手机"),
            ("电脑", "搜索电脑"),
            ("", "空搜索"),
        ]
        
        for query, test_name in search_tests:
            try:
                search_url = urljoin(self.base_url, f"/goods/search/?q={query}")
                response = self.session.get(search_url)
                
                if response.status_code == 200:
                    self.log_test(test_name, True, "搜索功能正常", response.status_code)
                else:
                    self.log_test(test_name, False, f"HTTP错误", response.status_code)
            except Exception as e:
                self.log_test(test_name, False, f"请求异常: {str(e)}")
    
    def test_responsive_design(self):
        """测试响应式设计"""
        try:
            # 模拟移动设备访问
            mobile_headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
            }
            
            response = self.session.get(self.base_url, headers=mobile_headers)
            if response.status_code == 200:
                content = response.text
                if "viewport" in content and "responsive" in content.lower():
                    self.log_test("响应式设计", True, "页面支持响应式设计", response.status_code)
                else:
                    self.log_test("响应式设计", True, "页面可在移动设备访问", response.status_code)
            else:
                self.log_test("响应式设计", False, f"HTTP错误", response.status_code)
        except Exception as e:
            self.log_test("响应式设计", False, f"请求异常: {str(e)}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始MARS BUY用户交互功能测试...")
        print("=" * 60)
        
        # 首先尝试登录
        login_success = self.test_user_login()
        
        # 运行其他测试
        self.test_product_detail_access()
        self.test_cart_functionality()
        self.test_favorites_functionality()
        self.test_user_profile()
        self.test_order_history()
        self.test_address_management()
        self.test_search_with_filters()
        self.test_responsive_design()
        
        # 统计结果
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if "✅" in r['status']])
        failed_tests = total_tests - passed_tests
        
        print("\n" + "=" * 60)
        print("📊 用户交互测试结果统计:")
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if "❌" in result['status']:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n🎉 用户交互测试完成!")
        return passed_tests, failed_tests

if __name__ == "__main__":
    tester = UserInteractionTester()
    passed, failed = tester.run_all_tests()
    
    # 如果有失败的测试，退出码为1
    exit(0 if failed == 0 else 1)
