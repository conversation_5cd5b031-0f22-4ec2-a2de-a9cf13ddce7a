from django.core.management.base import BaseCommand
from django.db import models
from goods.models import Category

class Command(BaseCommand):
    help = '重置分类数据，按照新的分类结构创建'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('开始重置分类数据...'))

        # 禁用所有现有分类
        old_categories_count = Category.objects.count()
        if old_categories_count > 0:
            # 逐个更新分类名称
            for category in Category.objects.all():
                category.name = category.name + '_旧'
                category.is_active = False
                category.save()
            self.stdout.write(self.style.SUCCESS(f'已禁用 {old_categories_count} 个旧分类'))
        
        # 新的分类结构
        categories_data = [
            {
                'name': '时尚穿搭',
                'sort_order': 1,
                'children': [
                    {'name': '男装', 'sort_order': 1},
                    {'name': '女装', 'sort_order': 2},
                    {'name': '童装', 'sort_order': 3},
                    {'name': '鞋类', 'sort_order': 4},
                    {'name': '箱包', 'sort_order': 5},
                    {'name': '服饰配件', 'sort_order': 6},
                ]
            },
            {
                'name': '美妆护肤',
                'sort_order': 2,
                'children': [
                    {'name': '面部护理', 'sort_order': 1},
                    {'name': '彩妆', 'sort_order': 2},
                    {'name': '身体护理', 'sort_order': 3},
                    {'name': '美发产品', 'sort_order': 4},
                    {'name': '香水香氛', 'sort_order': 5},
                ]
            },
            {
                'name': '家居日用',
                'sort_order': 3,
                'children': [
                    {'name': '家纺床品', 'sort_order': 1},
                    {'name': '厨房用品', 'sort_order': 2},
                    {'name': '卫浴清洁', 'sort_order': 3},
                    {'name': '家居装饰', 'sort_order': 4},
                    {'name': '收纳用品', 'sort_order': 5},
                ]
            },
            {
                'name': '数码科技',
                'sort_order': 4,
                'children': [
                    {'name': '手机及配件', 'sort_order': 1},
                    {'name': '电脑办公设备', 'sort_order': 2},
                    {'name': '智能穿戴', 'sort_order': 3},
                    {'name': '摄影摄像器材', 'sort_order': 4},
                ]
            },
            {
                'name': '家用电器',
                'sort_order': 5,
                'children': [
                    {'name': '大家电', 'sort_order': 1},
                    {'name': '厨房小家电', 'sort_order': 2},
                    {'name': '生活小家电', 'sort_order': 3},
                ]
            },
            {
                'name': '食品生鲜',
                'sort_order': 6,
                'children': [
                    {'name': '休闲零食', 'sort_order': 1},
                    {'name': '粮油副食', 'sort_order': 2},
                    {'name': '新鲜果蔬', 'sort_order': 3},
                    {'name': '肉禽蛋奶', 'sort_order': 4},
                    {'name': '酒水饮料', 'sort_order': 5},
                ]
            },
            {
                'name': '母婴亲子',
                'sort_order': 7,
                'children': [
                    {'name': '母婴用品', 'sort_order': 1},
                    {'name': '儿童玩具', 'sort_order': 2},
                    {'name': '童装童鞋', 'sort_order': 3},
                    {'name': '早教学习产品', 'sort_order': 4},
                ]
            },
            {
                'name': '运动户外',
                'sort_order': 8,
                'children': [
                    {'name': '运动服饰', 'sort_order': 1},
                    {'name': '健身器材', 'sort_order': 2},
                    {'name': '户外装备', 'sort_order': 3},
                    {'name': '球类运动用品', 'sort_order': 4},
                ]
            },
            {
                'name': '汽车相关',
                'sort_order': 9,
                'children': [
                    {'name': '汽车装饰', 'sort_order': 1},
                    {'name': '汽车保养', 'sort_order': 2},
                    {'name': '汽车电子', 'sort_order': 3},
                    {'name': '车载用品', 'sort_order': 4},
                ]
            },
            {
                'name': '生活服务',
                'sort_order': 10,
                'children': [
                    {'name': '虚拟商品', 'sort_order': 1},
                    {'name': '家政服务', 'sort_order': 2},
                    {'name': '本地生活团购', 'sort_order': 3},
                ]
            },
        ]
        
        # 创建分类
        for category_data in categories_data:
            # 创建父分类
            parent_category = Category.objects.create(
                name=category_data['name'],
                sort_order=category_data['sort_order'],
                is_active=True
            )
            self.stdout.write(f'创建父分类: {parent_category.name}')
            
            # 创建子分类
            for child_data in category_data['children']:
                child_category = Category.objects.create(
                    name=child_data['name'],
                    parent=parent_category,
                    sort_order=child_data['sort_order'],
                    is_active=True
                )
                self.stdout.write(f'  创建子分类: {child_category.name}')
        
        # 将商品重新分配到合适的分类（默认分配到第一个子分类）
        from goods.models import Product
        products_with_old_category = Product.objects.filter(category__name__endswith='_旧')
        if products_with_old_category.exists():
            # 获取第一个子分类作为默认分类
            default_category = Category.objects.filter(parent__isnull=False, is_active=True).first()
            if default_category:
                count = products_with_old_category.count()
                products_with_old_category.update(category=default_category)
                self.stdout.write(self.style.SUCCESS(f'已将 {count} 个商品分配到 "{default_category.name}" 分类'))

        self.stdout.write(self.style.SUCCESS('分类数据重置完成！'))
        self.stdout.write(self.style.SUCCESS(f'共创建了 {Category.objects.filter(parent=None).count()} 个父分类'))
        self.stdout.write(self.style.SUCCESS(f'共创建了 {Category.objects.filter(parent__isnull=False).count()} 个子分类'))
