/* 全局重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

/* 清除浮动 */
.clearfix:after {
    content: "";
    display: table;
    clear: both;
}

.leftfix {
    float: left;
}

.rightfix {
    float: right;
}

/* 基础容器 */
.container {
    width: 1190px;
    margin: 0 auto;
}

/* 顶部导航条 */
.topbar {
    height: 30px;
    background-color: #ECECEC;
}

.welcome {
    height: 30px;
    line-height: 30px;
    font-size: 0;
    color: #666666;
}

.welcome span,
.welcome a {
    font-size: 12px;
    text-decoration: none;
    color: #666666;
}

.welcome .hello {
    margin-right: 28px;
}

.welcome .login {
    padding-right: 10px;
    border-right: 1px solid #666666;
    color: #dd302d;
}

.welcome .register {
    padding-left: 10px;
    color: #dd302d;
}

.topbar-nav .list {
    height: 30px;
    line-height: 30px;
    list-style: none;
}

.topbar-nav .list li {
    float: left;
}

.topbar-nav .list li a {
    padding: 0 15px;
    border-right: 1px solid #666666;
    text-decoration: none;
    color: #666666;
    font-size: 12px;
}

.topbar-nav .list li:first-child a {
    padding-left: 0;
}

.topbar-nav .list li:last-child a {
    padding-right: 0;
    border: 0;
}

.topbar-nav .list li a:hover {
    color: #dd302d;
}

/* 头部 */
.header {
    height: 120px;
    background-color: #ffffff;
}

.logo {
    margin-top: 25px;
}

.logo h1 {
    font-size: 48px;
    color: #dd302d;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(221, 48, 45, 0.3);
}

.search {
    margin-top: 42px;
}

.search form {
    font-size: 0;
}

.search input {
    width: 508px;
    height: 34px;
    border: 2px solid #dd302d;
    padding: 0 15px;
    font-size: 14px;
    outline: none;
}

.search input:focus {
    border-color: #c82333;
    box-shadow: 0 0 5px rgba(221, 48, 45, 0.3);
}

.search button {
    width: 80px;
    height: 38px;
    background-color: #dd302d;
    border: none;
    color: white;
    font-size: 14px;
    cursor: pointer;
    vertical-align: top;
    transition: background-color 0.3s;
}

.search button:hover {
    background-color: #c82333;
}

/* 主导航区 */
.main-nav {
    height: 40px;
    line-height: 40px;
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.2);
}
.main-nav .all-types {
    width: 200px;
    background: linear-gradient(135deg, #b21e2f 0%, #8b0000 100%);
    text-align: center;
    color: #fff;
    font-weight: bold;
    transition: all 0.3s ease;
}

.main-nav .all-types:hover {
    background: linear-gradient(135deg, #8b0000 0%, #b21e2f 100%);
    transform: translateY(-1px);
}

.main-nav-list {
    display: flex;
}
.main-nav-list li {
    margin-left: 20px;
    position: relative;
}
.main-nav-list li a {
    color: #fff;
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 6px;
}

.main-nav-list li a:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.main-nav-list li a:hover:before {
    left: 100%;
}

.main-nav-list li a:hover {
    background-color: rgba(255,255,255,0.15);
    transform: translateY(-1px);
}

.main-nav-list li a i {
    font-size: 14px;
    color: #ffeb3b;
    transition: all 0.3s ease;
}

.main-nav-list li a:hover i {
    color: #fff;
    transform: scale(1.1);
}

.main-nav-list li a span {
    font-size: 13px;
    font-weight: 500;
}

/* 分类侧边栏 */
.category-sidebar {
    width: 200px;
    flex-shrink: 0;
    margin-right: 20px;
}

/* 分类导航样式 */
.category-dropdown {
    position: relative;
    width: 200px;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 8px;
    overflow: hidden;
    z-index: 100;
}

.all-category-title {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    padding: 12px 15px;
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

.all-category-title i {
    margin-right: 8px;
    font-size: 14px;
}

.category-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.category-list li {
    position: relative;
    border-bottom: 1px solid #f5f5f5;
}

.category-list li:last-child {
    border-bottom: none;
}

.category-list li a {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 15px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;
}

.category-list li a i.fas:not(.arrow-right) {
    margin-right: 10px;
    color: #dc3545;
    font-size: 16px;
    width: 20px;
    text-align: center;
}

.category-list li a .arrow-right {
    color: #999;
    font-size: 12px;
    margin-left: auto;
}

.category-list li:hover {
    background-color: #f8f8f8;
}

.category-list li:hover > a {
    color: #dc3545;
    transform: translateX(3px);
}

.category-list li:hover .sub-categories {
    display: block;
}

.sub-categories {
    display: none;
    position: absolute;
    left: 200px;
    top: 0;
    width: 650px;
    min-height: 100%;
    background: #fff;
    box-shadow: 2px 0 15px rgba(0,0,0,0.15);
    padding: 20px;
    z-index: 10;
    border-radius: 0 8px 8px 0;
    border: 1px solid #f0f0f0;
}

.sub-category-section {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;
}

.sub-category-column {
    flex: 1;
    min-width: 150px;
    margin-right: 15px;
}

.sub-category-column:last-child {
    margin-right: 0;
}

.sub-category-group {
    margin-bottom: 15px;
}

.sub-category-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    font-size: 14px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 5px;
}

.sub-category-items {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.sub-category-items a {
    display: block;
    padding: 6px 0;
    color: #666;
    font-size: 13px;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 3px;
    position: relative;
}

.sub-category-items a:hover {
    color: #dc3545;
    transform: translateX(5px);
}

.sub-category-items a:before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 2px;
    background-color: #dc3545;
    transition: width 0.3s ease;
}

.sub-category-items a:hover:before {
    width: 6px;
}

/* 主要内容区 */
.main-content {
    margin-top: 15px;
    margin-bottom: 15px;
    animation: fadeInUp 0.8s ease-out;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    position: relative;
}

/* 内容包装器 */
.content-wrapper {
    display: flex;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* 主区域 */
.main-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.main-content:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    z-index: -1;
    border-radius: 20px;
}

.main-content .container {
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* 消息框样式 */
.message {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    padding: 15px;
    margin-bottom: 15px;
    height: 180px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-radius: 12px;
    border: 1px solid rgba(220, 53, 69, 0.1);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.message:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(220, 53, 69, 0.15);
}

.message:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, #dc3545, #ff6b6b);
}

.message .title {
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
    margin-bottom: 12px;
    font-weight: 600;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.message .title .fl {
    position: relative;
    padding-left: 8px;
    font-size: 16px;
}

.message .title .fr {
    font-size: 12px;
    color: #999;
    transition: all 0.3s ease;
    display: inline-block;
    padding: 5px 10px;
    background-color: #f8f9fa;
    border-radius: 15px;
    text-decoration: none;
}

.message .title .fr:hover {
    color: #fff;
    background-color: #dc3545;
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(220, 53, 69, 0.2);
}

.message-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.message-list li {
    height: 32px;
    line-height: 32px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: relative;
    padding-left: 15px;
    color: #666;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin-bottom: 4px;
}

.message-list li:hover {
    background-color: rgba(220, 53, 69, 0.05);
    padding-left: 18px;
    color: #dc3545;
}

.message-list li:before {
    display: none; /* 隐藏原来的小圆点，因为我们使用了图标 */
}

.message-list li i {
    margin-right: 8px;
    font-size: 14px;
}

.message-list li .text-danger {
    color: #dc3545;
}

.message-list li .text-primary {
    color: #007bff;
}

.message-list li .text-success {
    color: #28a745;
}

.message-list li .text-warning {
    color: #ffc107;
}

/* 右侧功能区 */
.slide-other {
    width: 270px;
    flex-shrink: 0;
}

.other-nav {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    padding: 15px;
    height: 180px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    border-radius: 12px;
    border: 1px solid rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.other-nav:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.other-nav-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 10px;
    padding: 0;
    margin: 0;
    list-style: none;
}

.other-nav-list li {
    margin-bottom: 10px;
}

.other-nav-list li a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: #666;
    padding: 8px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.other-nav-list li a:hover {
    background-color: rgba(220, 53, 69, 0.05);
    transform: translateY(-2px);
    color: #dc3545;
}

.other-nav-list .picture {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    border: 2px solid rgba(255,255,255,0.8);
    flex-shrink: 0;
}

.other-nav-list li a:hover .picture {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
}

.other-nav-list .picture i {
    font-size: 16px;
    color: #dc3545;
    transition: all 0.3s ease;
}

.other-nav-list li a:hover .picture i {
    color: white;
}

.other-nav-list span {
    font-size: 13px;
    font-weight: 500;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 秒杀区域 */
.seckill {
    margin: 30px 0;
    background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
    padding: 20px 0;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.08);
    position: relative;
    overflow: hidden;
}

.seckill:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.seckill-header {
    padding: 0 15px 10px;
    border-bottom: 1px solid #f5f5f5;
    margin-bottom: 15px;
}

.seckill-title {
    font-size: 24px;
    font-weight: bold;
    color: #DD302D;
    position: relative;
    display: inline-block;
}

.seckill-title:before {
    content: '🔥';
    margin-right: 8px;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.seckill-timer {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: #fff;
    padding: 8px 16px;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 4px 15px rgba(221, 48, 45, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(221, 48, 45, 0.5); }
    100% { box-shadow: 0 4px 15px rgba(221, 48, 45, 0.3); }
}

.seckill-countdown strong {
    display: inline-block;
    width: 24px;
    height: 24px;
    line-height: 24px;
    text-align: center;
    background-color: #fff;
    color: #dc3545;
    border-radius: 3px;
    margin: 0 2px;
}

.seckill-content {
    display: flex;
    justify-content: space-between;
    padding: 0 15px;
}

.seckill-item {
    width: 23%;
    padding: 15px;
    background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    border-radius: 12px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    text-decoration: none;
    color: #333;
    position: relative;
    overflow: hidden;
}

.seckill-item:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s;
}

.seckill-item:hover:before {
    left: 100%;
}

.seckill-item:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 12px 40px rgba(221, 48, 45, 0.15);
    border: 2px solid rgba(221, 48, 45, 0.1);
}

.seckill-item img {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 10px;
}

.seckill-info {
    text-align: center;
}

.seckill-name {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.seckill-price {
    color: #DD302D;
    font-size: 16px;
    margin-bottom: 5px;
}

.seckill-price span {
    font-weight: bold;
    font-size: 18px;
}

.original-price {
    color: #999;
    font-size: 12px;
}

/* 潮流服饰区域样式 */
.fashion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 25px;
    margin-top: 30px;
}

.fashion-card {
    background: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    position: relative;
}

.fashion-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(220,53,69,0.15);
}

.fashion-image {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.fashion-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.5s ease;
}

.fashion-card:hover .fashion-img {
    transform: scale(1.1);
}

.fashion-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    z-index: 2;
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 4px 10px rgba(0,0,0,0.1);
}

.fashion-badge.hot {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
}

.fashion-badge.new {
    background: linear-gradient(135deg, #4481eb 0%, #04befe 100%);
    color: white;
}

.fashion-actions {
    position: absolute;
    bottom: -50px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 10px;
    background: rgba(255,255,255,0.9);
    backdrop-filter: blur(5px);
    transition: all 0.3s ease;
}

.fashion-card:hover .fashion-actions {
    bottom: 0;
}

.fashion-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.fashion-action-btn:hover {
    background: #dc3545;
    color: white;
    transform: translateY(-5px);
}

.fashion-info {
    padding: 20px;
}

.fashion-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.fashion-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.fashion-price {
    font-size: 18px;
    font-weight: 700;
    color: #dc3545;
}

.fashion-sales {
    font-size: 12px;
    color: #666;
}

.fashion-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.fashion-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(220,53,69,0.3);
    color: white;
    text-decoration: none;
}

/* 分类标题和查看全部按钮 */
.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.category-title {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.category-title i {
    color: #dc3545;
    font-size: 24px;
}

.category-more {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #666;
    border-radius: 20px;
    text-decoration: none;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.category-more:hover {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220,53,69,0.2);
    text-decoration: none;
}

.category-more i {
    font-size: 12px;
    transition: transform 0.3s ease;
}

.category-more:hover i {
    transform: translateX(3px);
}

/* 查看全部按钮样式 */
.view-all-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 5px 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #666;
    border-radius: 15px;
    text-decoration: none;
    font-weight: 500;
    font-size: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.view-all-btn:hover {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220,53,69,0.2);
    text-decoration: none;
}

.view-all-btn i {
    font-size: 10px;
    transition: transform 0.3s ease;
}

.view-all-btn:hover i {
    transform: translateX(3px);
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    animation: slideInRight 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.message-toast.success {
    background-color: #28a745;
}

.message-toast.error {
    background-color: #dc3545;
}

.message-toast.info {
    background-color: #17a2b8;
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes gradientMove {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* 响应式设计 */
@media (max-width: 1280px) {
    .container {
        width: 100%;
    }
}

@media (max-width: 992px) {
    .content-wrapper {
        flex-direction: column;
    }
    
    .category-sidebar {
        width: 100%;
        margin-bottom: 15px;
    }
    
    .main-area {
        flex-direction: column;
    }
    
    .slide-other {
        width: 100%;
        display: flex;
        gap: 15px;
    }
    
    .message, .other-nav {
        flex: 1;
        height: auto;
        min-height: 180px;
    }
}

@media (max-width: 768px) {
    .slide-other {
        flex-direction: column;
    }
    
    .fashion-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }
    
    .fashion-image {
        height: 200px;
    }
    
    .fashion-name {
        font-size: 14px;
    }
    
    .fashion-price {
        font-size: 16px;
    }
    
    .fashion-action-btn {
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .fashion-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 10px;
    }
    
    .category-more {
        padding: 6px 12px;
        font-size: 12px;
    }
    
    .category-title {
        font-size: 20px;
    }
} 