<!DOCTYPE html>
<html>
<head>
    <title>测试支付密码</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .container { max-width: 500px; margin: 0 auto; }
        .password-display { display: flex; gap: 10px; margin: 20px 0; }
        .password-dot { 
            width: 20px; height: 20px; border: 2px solid #ddd; 
            border-radius: 50%; background: #f8f9fa; 
        }
        .password-dot.filled { background: #28a745; border-color: #28a745; }
        .keyboard { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; max-width: 300px; }
        .num-btn { 
            padding: 15px; border: 1px solid #ddd; background: white; 
            cursor: pointer; font-size: 18px; 
        }
        .num-btn:hover { background: #f8f9fa; }
        .result { margin: 20px 0; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>测试支付密码验证</h1>
        
        <div class="password-display">
            <span class="password-dot" data-index="0"></span>
            <span class="password-dot" data-index="1"></span>
            <span class="password-dot" data-index="2"></span>
            <span class="password-dot" data-index="3"></span>
            <span class="password-dot" data-index="4"></span>
            <span class="password-dot" data-index="5"></span>
        </div>
        
        <div class="keyboard">
            <button class="num-btn" data-num="1">1</button>
            <button class="num-btn" data-num="2">2</button>
            <button class="num-btn" data-num="3">3</button>
            <button class="num-btn" data-num="4">4</button>
            <button class="num-btn" data-num="5">5</button>
            <button class="num-btn" data-num="6">6</button>
            <button class="num-btn" data-num="7">7</button>
            <button class="num-btn" data-num="8">8</button>
            <button class="num-btn" data-num="9">9</button>
            <button class="num-btn" onclick="clearPassword()">清除</button>
            <button class="num-btn" data-num="0">0</button>
            <button class="num-btn" onclick="deleteLastDigit()">删除</button>
        </div>
        
        <button onclick="testPassword()" style="margin: 20px 0; padding: 10px 20px;">测试密码</button>
        
        <div class="result" id="result">
            请输入6位密码进行测试<br>
            默认密码：123456, 888888, 666666, 000000
        </div>
    </div>

    {% csrf_token %}
    
    <script>
        let currentPassword = '';
        const passwordDots = document.querySelectorAll('.password-dot');
        const numBtns = document.querySelectorAll('.num-btn[data-num]');
        
        // 数字按钮事件
        numBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const num = this.dataset.num;
                if (currentPassword.length < 6) {
                    currentPassword += num;
                    updatePasswordDisplay();
                    
                    if (currentPassword.length === 6) {
                        setTimeout(testPassword, 500);
                    }
                }
            });
        });
        
        function updatePasswordDisplay() {
            passwordDots.forEach((dot, index) => {
                if (index < currentPassword.length) {
                    dot.classList.add('filled');
                } else {
                    dot.classList.remove('filled');
                }
            });
        }
        
        function clearPassword() {
            currentPassword = '';
            updatePasswordDisplay();
            document.getElementById('result').innerHTML = '密码已清除';
        }
        
        function deleteLastDigit() {
            if (currentPassword.length > 0) {
                currentPassword = currentPassword.slice(0, -1);
                updatePasswordDisplay();
            }
        }
        
        async function testPassword() {
            if (currentPassword.length !== 6) {
                document.getElementById('result').innerHTML = '请输入6位密码';
                return;
            }
            
            document.getElementById('result').innerHTML = '正在验证密码...';
            
            try {
                const response = await fetch('/payment/verify-password/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        password: currentPassword,
                        order_number: 'TEST123456'
                    })
                });
                
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (data.success) {
                    document.getElementById('result').innerHTML = 
                        `<span style="color: green;">✓ 密码验证成功！</span><br>密码: ${currentPassword}`;
                } else {
                    document.getElementById('result').innerHTML = 
                        `<span style="color: red;">✗ 密码验证失败</span><br>错误: ${data.msg}<br>密码: ${currentPassword}`;
                }
            } catch (error) {
                console.error('验证错误:', error);
                document.getElementById('result').innerHTML = 
                    `<span style="color: red;">✗ 网络错误</span><br>错误: ${error.message}<br>密码: ${currentPassword}`;
            }
        }
    </script>
</body>
</html>
