"""
公共工具函数模块
"""
from django.core.paginator import Paginator
from django.http import JsonResponse
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)


def get_client_ip(request):
    """获取客户端IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def paginate_queryset(queryset, page_number, per_page=20):
    """
    分页工具函数
    
    Args:
        queryset: 查询集
        page_number: 页码
        per_page: 每页数量
    
    Returns:
        page_obj: 分页对象
    """
    paginator = Paginator(queryset, per_page)
    page_obj = paginator.get_page(page_number)
    return page_obj


def success_response(message, data=None):
    """成功响应"""
    response_data = {'success': True, 'message': message}
    if data:
        response_data['data'] = data
    return JsonResponse(response_data)


def error_response(message, data=None):
    """错误响应"""
    response_data = {'success': False, 'message': message}
    if data:
        response_data['data'] = data
    return JsonResponse(response_data)


def log_admin_action(admin_user, action, content_type=None, object_id=None, 
                    object_repr=None, change_message=None, request=None):
    """
    记录管理员操作日志
    
    Args:
        admin_user: 管理员用户
        action: 操作类型
        content_type: 内容类型
        object_id: 对象ID
        object_repr: 对象表示
        change_message: 变更消息
        request: 请求对象
    """
    try:
        from .models import AdminLog
        
        AdminLog.objects.create(
            admin=admin_user,
            action=action,
            content_type=content_type or '',
            object_id=str(object_id) if object_id else '',
            object_repr=object_repr or '',
            change_message=change_message or '',
            ip_address=get_client_ip(request) if request else '',
            user_agent=request.META.get('HTTP_USER_AGENT', '') if request else ''
        )
    except Exception as e:
        logger.error(f"记录管理员操作日志失败: {e}")


def is_admin(user):
    """检查用户是否为管理员"""
    return user.is_authenticated and (user.is_staff or user.is_superuser)


def format_file_size(size_bytes):
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.1f}{size_names[i]}"


def safe_int(value, default=0):
    """安全转换为整数"""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default


def safe_float(value, default=0.0):
    """安全转换为浮点数"""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def truncate_string(text, max_length=50, suffix='...'):
    """截断字符串"""
    if not text:
        return ''
    
    if len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix
