#!/usr/bin/env python3
"""
创建测试数据脚本
"""

import os
import sys
import django

# 设置Django环境
sys.path.append('shop_front')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.contrib.auth import get_user_model
from goods.models import Product, Category
from decimal import Decimal
import random

User = get_user_model()

def create_test_user():
    """创建测试用户"""
    print("🔧 创建测试用户...")
    
    # 检查是否已存在
    if User.objects.filter(username='testuser').exists():
        print("   测试用户已存在")
        return User.objects.get(username='testuser')
    
    # 创建测试用户
    user = User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='123456',
        phone='13800138000'
    )
    print(f"   ✅ 创建测试用户: {user.username}")
    return user

def create_test_products():
    """创建测试商品"""
    print("🔧 创建测试商品...")
    
    # 获取分类
    categories = Category.objects.filter(parent__isnull=False)[:10]  # 获取子分类
    if not categories:
        print("   ❌ 没有找到分类，无法创建商品")
        return
    
    # 商品数据
    products_data = [
        {
            'name': 'iPhone 15 Pro Max',
            'description': '苹果最新旗舰手机，搭载A17 Pro芯片，钛金属机身，专业摄影系统。',
            'price': Decimal('9999.00'),
            'stock': 50,
            'sales_count': 120
        },
        {
            'name': 'MacBook Pro 16英寸',
            'description': '专业级笔记本电脑，M3 Max芯片，32GB内存，1TB存储。',
            'price': Decimal('25999.00'),
            'stock': 30,
            'sales_count': 85
        },
        {
            'name': 'Nike Air Jordan 1',
            'description': '经典篮球鞋，复古设计，舒适透气，时尚百搭。',
            'price': Decimal('1299.00'),
            'stock': 100,
            'sales_count': 200
        },
        {
            'name': '小米13 Ultra',
            'description': '徕卡影像旗舰，骁龙8 Gen2处理器，专业摄影体验。',
            'price': Decimal('5999.00'),
            'stock': 80,
            'sales_count': 150
        },
        {
            'name': 'Sony WH-1000XM5',
            'description': '降噪耳机，30小时续航，Hi-Res音质，智能降噪。',
            'price': Decimal('2399.00'),
            'stock': 60,
            'sales_count': 95
        },
        {
            'name': '戴森V15吸尘器',
            'description': '无线吸尘器，激光显尘，强劲吸力，智能感应。',
            'price': Decimal('4990.00'),
            'stock': 40,
            'sales_count': 75
        },
        {
            'name': 'iPad Pro 12.9英寸',
            'description': 'M2芯片平板电脑，Liquid Retina XDR显示屏，支持Apple Pencil。',
            'price': Decimal('8999.00'),
            'stock': 45,
            'sales_count': 110
        },
        {
            'name': '华为Mate 60 Pro',
            'description': '鸿蒙智能手机，卫星通话，超感知影像，麒麟芯片。',
            'price': Decimal('6999.00'),
            'stock': 70,
            'sales_count': 180
        },
        {
            'name': 'Tesla Model Y',
            'description': '纯电动SUV，长续航版本，自动驾驶，环保出行。',
            'price': Decimal('299900.00'),
            'stock': 10,
            'sales_count': 25
        },
        {
            'name': 'Switch OLED游戏机',
            'description': '任天堂游戏主机，OLED屏幕，便携游戏，丰富游戏库。',
            'price': Decimal('2599.00'),
            'stock': 90,
            'sales_count': 160
        }
    ]
    
    created_count = 0
    for i, product_data in enumerate(products_data):
        # 检查是否已存在
        if Product.objects.filter(name=product_data['name']).exists():
            continue
        
        # 随机选择分类
        category = random.choice(categories)
        
        # 创建商品
        product = Product.objects.create(
            name=product_data['name'],
            description=product_data['description'],
            price=product_data['price'],
            stock=product_data['stock'],
            sales_count=product_data['sales_count'],
            category=category,
            is_active=True,
            is_featured=i < 5,  # 前5个设为推荐商品
            is_new=i < 3  # 前3个设为新品
        )
        created_count += 1
        print(f"   ✅ 创建商品: {product.name} (分类: {category.name})")
    
    print(f"   总共创建了 {created_count} 个商品")

def main():
    print("🚀 开始创建测试数据...")
    print("=" * 50)
    
    # 创建测试用户
    create_test_user()
    
    # 创建测试商品
    create_test_products()
    
    # 统计数据
    user_count = User.objects.count()
    product_count = Product.objects.count()
    category_count = Category.objects.count()
    
    print("\n" + "=" * 50)
    print("📊 数据统计:")
    print(f"用户数量: {user_count}")
    print(f"商品数量: {product_count}")
    print(f"分类数量: {category_count}")
    print("\n🎉 测试数据创建完成!")
    
    print("\n📝 测试账号信息:")
    print("用户名: testuser")
    print("密码: 123456")
    print("邮箱: <EMAIL>")

if __name__ == "__main__":
    main()
