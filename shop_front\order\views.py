from django.shortcuts import render, get_object_or_404, redirect
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.http import JsonResponse
from django.utils.translation import gettext as _
from django.contrib import messages
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import Order, OrderItem, Cart
from goods.models import Product
import uuid
from django.contrib.auth.decorators import login_required
from django.utils import timezone

# Create your views here.

def generate_order_number():
    """生成唯一的订单号"""
    return uuid.uuid4().hex[:16]

class CartView(LoginRequiredMixin, View):
    """购物车视图"""
    def get(self, request):
        """显示用户的购物车"""
        cart_items = Cart.objects.filter(user=request.user)
        total_price = sum(item.total_price for item in cart_items)
        return render(request, 'order/cart.html', {
            'cart_items': cart_items,
            'total_price': total_price
        })

class CartAddView(LoginRequiredMixin, View):
    """添加商品到购物车"""
    def post(self, request):
        product_id = request.POST.get('product_id')
        quantity = int(request.POST.get('quantity', 1))

        # 验证商品
        try:
            product = Product.objects.get(id=product_id, is_active=True)
        except Product.DoesNotExist:
            return JsonResponse({'code': 400, 'msg': '商品不存在或已下架'})

        # 检查购物车中是否已有该商品
        cart_item, created = Cart.objects.get_or_create(
            user=request.user,
            product_id=product_id,
            defaults={'quantity': quantity}
        )

        # 如果已存在，则增加数量
        if not created:
            new_quantity = cart_item.quantity + quantity
        else:
            new_quantity = quantity

        # 检查库存
        if product.stock < new_quantity:
            return JsonResponse({'code': 400, 'msg': f'库存不足，当前库存：{product.stock}'})

        if not created:
            cart_item.quantity = new_quantity
            cart_item.save()

        return JsonResponse({'code': 200, 'msg': _('商品已添加到购物车')})

class CartUpdateView(LoginRequiredMixin, View):
    """更新购物车商品数量"""
    def post(self, request):
        product_id = request.POST.get('product_id')
        quantity = int(request.POST.get('quantity', 1))

        try:
            cart_item = Cart.objects.get(user=request.user, product_id=product_id)
            if quantity <= 0:
                cart_item.delete()
                return JsonResponse({'success': True, 'msg': '商品已删除'})
            else:
                # 检查库存
                if cart_item.product.stock < quantity:
                    return JsonResponse({
                        'success': False,
                        'msg': f'库存不足，当前库存：{cart_item.product.stock}'
                    })

                cart_item.quantity = quantity
                cart_item.save()
                return JsonResponse({
                    'success': True,
                    'msg': '数量已更新',
                    'total_price': cart_item.total_price
                })
        except Cart.DoesNotExist:
            return JsonResponse({'success': False, 'msg': '商品不存在'})

class CartDeleteView(LoginRequiredMixin, View):
    """删除购物车商品"""
    def post(self, request):
        import json
        data = json.loads(request.body)
        product_ids = data.get('product_ids', [])

        if not product_ids:
            return JsonResponse({'success': False, 'msg': '请选择要删除的商品'})

        try:
            Cart.objects.filter(user=request.user, product_id__in=product_ids).delete()
            return JsonResponse({'success': True, 'msg': '商品已删除'})
        except Exception as e:
            return JsonResponse({'success': False, 'msg': '删除失败'})

class BuyNowView(LoginRequiredMixin, View):
    """立即购买视图"""
    def post(self, request):
        print(f"立即购买请求 - 用户: {request.user}, POST数据: {request.POST}")

        try:
            product_id = request.POST.get('product_id')
            quantity = int(request.POST.get('quantity', 1))

            print(f"商品ID: {product_id}, 数量: {quantity}")

            if not product_id:
                return JsonResponse({'success': False, 'msg': '商品ID不能为空'})

            # 验证商品
            try:
                product = Product.objects.get(id=product_id, is_active=True)
                print(f"找到商品: {product.name}, 价格: {product.price}, 库存: {product.stock}")
            except Product.DoesNotExist:
                print(f"商品不存在或已下架: {product_id}")
                return JsonResponse({'success': False, 'msg': '商品不存在或已下架'})

            # 检查库存
            if product.stock < quantity:
                print(f"库存不足: 需要 {quantity}, 库存 {product.stock}")
                return JsonResponse({'success': False, 'msg': f'库存不足，当前库存：{product.stock}'})

            # 创建订单
            order_number = generate_order_number()
            print(f"生成订单号: {order_number}")

            order = Order.objects.create(
                user=request.user,
                order_number=order_number,
                total_amount=product.price * quantity,
                pay_amount=product.price * quantity,
                shipping_address=request.POST.get('address', '默认地址'),
                receiver=request.POST.get('receiver', request.user.username),
                contact_phone=request.POST.get('receiver_mobile', ''),
                remark=request.POST.get('remark', '')
            )
            print(f"订单创建成功: {order.id}")

            # 创建订单项
            order_item = OrderItem.objects.create(
                order=order,
                product=product,
                price=product.price,
                quantity=quantity
            )
            print(f"订单项创建成功: {order_item.id}")

            redirect_url = f'/payment/pay/{order.order_number}/'
            print(f"返回跳转URL: {redirect_url}")

            # 检查是否是AJAX请求
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'order_number': order.order_number,
                    'redirect_url': redirect_url
                })
            else:
                # 表单提交，直接重定向到简化支付页面
                messages.success(request, '订单创建成功！')
                return redirect('payment:pay_simple', order_number=order.order_number)

        except ValueError as e:
            print(f"数值错误: {e}")
            return JsonResponse({'success': False, 'msg': '数量必须是有效数字'})
        except Exception as e:
            print(f"立即购买异常: {e}")
            import traceback
            traceback.print_exc()
            return JsonResponse({'success': False, 'msg': f'创建订单失败: {str(e)}'})

class OrderCreateView(LoginRequiredMixin, View):
    """创建订单视图"""
    def get(self, request):
        """处理GET请求，显示结算页面"""
        # 获取购物车商品
        cart_items = Cart.objects.filter(user=request.user)
        
        if not cart_items.exists():
            messages.error(request, _('购物车为空，无法创建订单'))
            return redirect('order:cart')
            
        # 检查所有商品的库存
        for cart_item in cart_items:
            if cart_item.product.stock < cart_item.quantity:
                messages.error(request, f'商品 {cart_item.product.name} 库存不足，当前库存：{cart_item.product.stock}')
                return redirect('order:cart')
                
        # 计算总价
        total_price = sum(item.total_price for item in cart_items)
        
        # 计算满减优惠
        discount = 0
        if total_price >= 1000:
            discount = 200
        elif total_price >= 300:
            discount = 50
        elif total_price >= 100:
            discount = 10
            
        final_price = total_price - discount
        
        context = {
            'cart_items': cart_items,
            'total_price': total_price,
            'discount': discount,
            'final_price': final_price,
            'user': request.user
        }
        
        return render(request, 'order/checkout.html', context)
        
    def post(self, request):
        # 从购物车创建订单
        cart_items = Cart.objects.filter(user=request.user)

        if not cart_items.exists():
            messages.error(request, _('购物车为空，无法创建订单'))
            return redirect('order:cart')

        # 检查所有商品的库存
        for cart_item in cart_items:
            if cart_item.product.stock < cart_item.quantity:
                messages.error(request, f'商品 {cart_item.product.name} 库存不足，当前库存：{cart_item.product.stock}')
                return redirect('order:cart')

        # 计算满减优惠
        total_amount = sum(item.total_price for item in cart_items)
        discount = 0
        if total_amount >= 1000:
            discount = 200
        elif total_amount >= 300:
            discount = 50
        elif total_amount >= 100:
            discount = 10
            
        pay_amount = total_amount - discount

        # 创建订单
        order = Order.objects.create(
            user=request.user,
            order_number=generate_order_number(),
            total_amount=total_amount,
            pay_amount=pay_amount,
            discount_amount=discount,
            shipping_address=request.POST.get('address'),
            receiver=request.POST.get('receiver'),
            contact_phone=request.POST.get('contact_phone'),
            remark=request.POST.get('remark', '')
        )

        # 创建订单项
        for cart_item in cart_items:
            OrderItem.objects.create(
                order=order,
                product=cart_item.product,
                price=cart_item.product.price,
                quantity=cart_item.quantity
            )

        # 清空购物车
        cart_items.delete()

        messages.success(request, _('订单创建成功'))
        return redirect('order:detail', order_number=order.order_number)

class OrderListView(LoginRequiredMixin, View):
    """订单列表视图"""
    def get(self, request):
        orders = Order.objects.filter(user=request.user).order_by('-created_time')
        return render(request, 'order/list.html', {'orders': orders})

class OrderDetailView(LoginRequiredMixin, View):
    """订单详情视图"""
    def get(self, request, order_number):
        order = get_object_or_404(Order, order_number=order_number, user=request.user)
        return render(request, 'order/detail.html', {'order': order})

@login_required
def cancel_order(request, order_number):
    """取消订单"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)
    
    if order.status == 'pending':
        order.status = 'cancelled'
        order.save()
        messages.success(request, _('订单已取消'))
    else:
        messages.error(request, _('只有待支付的订单可以取消'))
    
    return redirect('order:detail', order_number=order.order_number)

@login_required
def pay_order(request, order_number):
    """支付订单 - 跳转到支付页面"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)

    if order.status != 'pending':
        messages.error(request, _('该订单不是待支付状态'))
        return redirect('order:detail', order_number=order.order_number)

    # 跳转到简化支付页面
    return redirect('payment:pay_simple', order_number=order.order_number)

@login_required
def confirm_order(request, order_number):
    """确认收货 - 更新订单状态并跳转到评价页面"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)
    
    if order.status != 'shipped':
        messages.error(request, _('只有已发货的订单可以确认收货'))
        return redirect('order:detail', order_number=order.order_number)
    
    # 更新订单状态
    order.status = 'received'
    order.received_time = timezone.now()
    order.save()
    
    messages.success(request, _('确认收货成功'))
    
    # 获取订单中第一个商品的ID用于评价
    if order.items.exists():
        first_product_id = order.items.first().product.id
        return redirect('reviews:add', product_id=first_product_id)
    else:
        return redirect('order:list')
