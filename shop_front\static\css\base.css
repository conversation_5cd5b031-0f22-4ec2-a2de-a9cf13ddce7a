/* 基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font: 12px/1.5 "Microsoft YaHei", Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
    color: #333;
}

a {
    text-decoration: none;
    color: #666;
}

a:hover {
    color: #DD302D;
}

/* 容器样式 */
.container {
    width: 1190px;
    margin: 0 auto;
}

/* 清除浮动 */
.clearfix::after {
    content: '';
    display: block;
    clear: both;
}

.leftfix {
    float: left;
}

.rightfix {
    float: right;
}

/* 顶部导航条样式 */
.topbar {
    height: 30px;
    background-color: #ECECEC;
}

.welcome {
    height: 30px;
    line-height: 30px;
    font-size: 0;
    color: #666666;
    position: relative;
}

.welcome span,
.welcome a {
    font-size: 12px;
}

.welcome .hello {
    margin-right: 28px;
}

.welcome .login {
    padding-right: 10px;
    border-right: 1px solid #666666;
}

.welcome .register {
    padding-left: 10px;
}

/* 用户信息样式 */
.user-info {
    display: inline-block;
    position: relative;
    padding: 0 10px;
    cursor: pointer;
}

.user-info .avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    vertical-align: middle;
    margin-right: 5px;
}

.user-info .username {
    display: inline-block;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

.user-info .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 120px;
    background: #fff;
    border: 1px solid #eee;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    z-index: 1000;
}

.user-info .dropdown-menu a {
    display: block;
    padding: 8px 15px;
    color: #666;
    font-size: 12px;
    transition: all 0.3s;
}

.user-info .dropdown-menu a:hover {
    background: #f5f5f5;
    color: #DD302D;
}

.user-info .dropdown-menu i {
    margin-right: 5px;
    width: 14px;
}

.user-info .dropdown-menu .logout {
    border-top: 1px solid #eee;
    color: #DD302D;
}

.topbar-nav .list {
    height: 30px;
    line-height: 30px;
}

.topbar-nav .list li {
    float: left;
}

.topbar-nav .list li a {
    padding: 0 15px;
    border-right: 1px solid #666666;
}

.topbar-nav .list li:first-child a {
    padding-left: 0;
}

.topbar-nav .list li:last-child a {
    padding-right: 0;
    border: 0;
}

/* 头部样式 */
.header {
    height: 120px;
}

.header .logo {
    margin-top: 20px;
}

.header .logo img {
    height: 80px;
}

.header .search {
    margin-top: 42px;
}

.header .search form {
    display: flex;
    align-items: center;
}

.header .search input {
    width: 508px;
    height: 34px;
    padding: 0 10px;
    border: 2px solid #DD302D;
    border-right: none;
    font-size: 14px;
    outline: none;
}

.header .search button {
    width: 80px;
    height: 38px;
    border: none;
    background-color: #DD302D;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

.header .search button:hover {
    background-color: #c62828;
}

/* 主导航区样式 */
.main-nav {
    height: 48px;
    border-bottom: 1px solid #DD302D;
    background: #fff;
}

.main-nav .container {
    position: relative;
}

.all-types {
    position: absolute;
    left: 0;
    top: 0;
    width: 190px;
    height: 48px;
    line-height: 48px;
    background-color: #DD302D;
    color: #fff;
    text-align: center;
    font-size: 16px;
    cursor: pointer;
}

.main-nav-list {
    margin-left: 190px;
    height: 48px;
    line-height: 48px;
}

.main-nav-list li {
    float: left;
    margin: 0 10px;
}

.main-nav-list li a {
    display: block;
    padding: 0 15px;
    font-size: 16px;
    color: #333;
    transition: all 0.3s;
}

.main-nav-list li a:hover {
    color: #DD302D;
}

/* 页面主体 */
.main-content {
    min-height: 400px;
    margin: 20px auto;
}

/* 页脚样式 */
.footer {
    margin-top: 48px;
    height: 440px;
    background-color: #EAEAEA;
    padding-top: 28px;
}

.top-links {
    height: 198px;
    border-bottom: 1px solid #E1E1E1;
}

.serve-list {
    width: 190px;
    float: left;
}

.serve-list li {
    height: 22px;
    line-height: 22px;
}

.serve-list li:first-child {
    height: 34px;
    line-height: 34px;
    font-size: 14px;
}

.serve-list:last-child {
    width: 242px;
}

.bottom-links {
    margin-top: 20px;
    text-align: center;
}

.cooperation-list {
    display: inline-block;
}

.cooperation-list li {
    float: left;
}

.cooperation-list li a {
    padding: 0 26px;
    border-right: 1px solid #E1E1E1;
}

.cooperation-list li:last-child a {
    border: 0;
    padding-right: 0;
}

.bottom-links-aside {
    margin-top: 10px;
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #DD302D;
    color: #fff;
    border-radius: 2px;
    cursor: pointer;
    text-align: center;
}

.btn:hover {
    background-color: #c82333;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e2e6ea;
    color: #333;
}

/* 表单样式 */
.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 2px;
}

.form-control:focus {
    border-color: #DD302D;
    outline: none;
}

/* 卡片样式 */
.card {
    border: 1px solid #ddd;
    margin-bottom: 20px;
}

.card-header {
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.card-body {
    padding: 15px;
}

/* 提示信息 */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #3c763d;
    background-color: #dff0d8;
    border-color: #d6e9c6;
}

.alert-danger {
    color: #a94442;
    background-color: #f2dede;
    border-color: #ebccd1;
}

/* 分页 */
.pagination {
    text-align: center;
    margin: 20px 0;
}

.pagination li {
    display: inline-block;
    margin: 0 5px;
}

.pagination li a {
    display: block;
    padding: 5px 10px;
    border: 1px solid #ddd;
    color: #666;
}

.pagination li.active a {
    background-color: #DD302D;
    color: #fff;
    border-color: #DD302D;
}

/* 工具类 */
.text-primary {
    color: #DD302D !important;
}

.bg-primary {
    background-color: #DD302D !important;
    color: #fff;
}

.text-center {
    text-align: center;
}

/* 收藏按钮样式 */
.add-to-favorite {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #e41c3c;
    background-color: #fff;
    color: #e41c3c;
    padding: 10px 18px;
    margin-left: 10px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 4px 8px rgba(228, 28, 60, 0.1);
}

.add-to-favorite:hover {
    background-color: #fff5f7;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(228, 28, 60, 0.2);
    color: #e41c3c;
}

.add-to-favorite:active {
    transform: translateY(-1px);
    box-shadow: 0 4px 10px rgba(228, 28, 60, 0.15);
}

.add-to-favorite i {
    margin-right: 8px;
    font-size: 18px;
    transition: all 0.3s ease;
}

.add-to-favorite:hover i {
    transform: scale(1.2);
}

/* 已收藏状态 */
.add-to-favorite .fas.fa-heart {
    color: #e41c3c;
    animation: heartBeat 0.5s ease-in-out;
}

@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.3); }
    50% { transform: scale(1); }
    75% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* 未收藏状态 */
.add-to-favorite .far.fa-heart {
    color: #e41c3c;
}

/* 收藏成功提示 */
.toast-message {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    padding: 12px 20px;
    border-radius: 10px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
    font-weight: 500;
    animation: fadeIn 0.3s ease;
    color: #fff;
    display: flex;
    align-items: center;
}

.toast-message::before {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    margin-right: 10px;
    font-size: 18px;
}

.toast-message.success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.toast-message.success::before {
    content: "\f00c";
}

.toast-message.info {
    background: linear-gradient(135deg, #17a2b8, #0dcaf0);
}

.toast-message.info::before {
    content: "\f129";
}

.toast-message.error {
    background: linear-gradient(135deg, #e41c3c, #ff6b6b);
}

.toast-message.error::before {
    content: "\f00d";
}

.toast-message.fade-out {
    animation: fadeOut 0.5s ease forwards;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
    }
} 