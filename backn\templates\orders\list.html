{% extends 'base.html' %}

{% block title %}订单管理 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}订单管理{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
        <i class="fas fa-filter me-1"></i>筛选
    </button>
    <ul class="dropdown-menu">
        {% for status_code, status_name in status_choices %}
        <li><a class="dropdown-item" href="?status={{ status_code }}">{{ status_name }}</a></li>
        {% endfor %}
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="?date=today">今日订单</a></li>
        <li><a class="dropdown-item" href="?date=week">本周订单</a></li>
        <li><a class="dropdown-item" href="?date=month">本月订单</a></li>
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{% url 'orders:list' %}">全部订单</a></li>
    </ul>
</div>
<a href="{% url 'orders:export' %}{% if status_filter %}?status={{ status_filter }}{% endif %}{% if date_filter %}{% if status_filter %}&{% else %}?{% endif %}date={{ date_filter }}{% endif %}" 
   class="btn btn-outline-success ms-2">
    <i class="fas fa-download me-1"></i>导出
</a>
{% endblock %}

{% block content %}
<!-- 搜索栏 -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="get" class="d-flex">
            <input type="text" class="form-control me-2" name="search" 
                   placeholder="搜索订单号、用户名、收货人" value="{{ search_query }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <span class="text-muted">共 {{ page_obj.paginator.count }} 个订单</span>
    </div>
</div>

<!-- 订单列表 -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>用户</th>
                        <th>收货信息</th>
                        <th>订单金额</th>
                        <th>支付方式</th>
                        <th>状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for order in page_obj %}
                    <tr>
                        <td>
                            <strong>{{ order.order_no }}</strong>
                        </td>
                        <td>
                            <div>{{ order.user.username }}</div>
                            <small class="text-muted">{{ order.user.email }}</small>
                        </td>
                        <td>
                            <div>{{ order.receiver_name }}</div>
                            <small class="text-muted">{{ order.receiver_phone }}</small>
                            <div class="small text-muted">{{ order.receiver_address|truncatechars:30 }}</div>
                        </td>
                        <td>
                            <div class="fw-bold">¥{{ order.final_amount }}</div>
                            {% if order.discount_amount > 0 %}
                            <small class="text-muted">
                                原价: ¥{{ order.total_amount }}<br>
                                优惠: -¥{{ order.discount_amount }}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            {% if order.payment_method %}
                                <span class="badge bg-info">{{ order.get_payment_method_display }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ order.get_status_display_color }}">
                                {{ order.get_status_display }}
                            </span>
                            {% if order.status == 'pending' %}
                            <div class="small text-muted mt-1">
                                <i class="fas fa-clock"></i>
                                {{ order.created_time|timesince }}前
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <div>{{ order.created_time|date:"Y-m-d H:i" }}</div>
                            {% if order.paid_time %}
                            <small class="text-success">
                                付款: {{ order.paid_time|date:"m-d H:i" }}
                            </small>
                            {% endif %}
                            {% if order.shipped_time %}
                            <small class="text-info">
                                发货: {{ order.shipped_time|date:"m-d H:i" }}
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'orders:detail' order.id %}" 
                                   class="btn btn-outline-primary" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                {% if order.status == 'paid' %}
                                <button type="button" class="btn btn-outline-success update-status" 
                                        data-order-id="{{ order.id }}" 
                                        data-status="shipped"
                                        title="发货">
                                    <i class="fas fa-shipping-fast"></i>
                                </button>
                                {% elif order.status == 'shipped' %}
                                <button type="button" class="btn btn-outline-info update-status" 
                                        data-order-id="{{ order.id }}" 
                                        data-status="delivered"
                                        title="确认送达">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                                {% if order.status == 'pending' %}
                                <button type="button" class="btn btn-outline-danger update-status" 
                                        data-order-id="{{ order.id }}" 
                                        data-status="cancelled"
                                        title="取消订单">
                                    <i class="fas fa-times"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="text-center text-muted py-4">
                            <i class="fas fa-shopping-bag fa-3x mb-3"></i>
                            <p>暂无订单数据</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 分页 -->
{% if page_obj.has_other_pages %}
<div class="row mt-4">
    <div class="col-md-6">
        <p class="text-muted">
            显示第 {{ page_obj.start_index }} - {{ page_obj.end_index }} 条，
            共 {{ page_obj.paginator.count }} 条记录
        </p>
    </div>
    <div class="col-md-6">
        <nav aria-label="订单列表分页">
            <ul class="pagination justify-content-end">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                {% endif %}

                <!-- 页码显示 -->
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>
{% else %}
<div class="row mt-4">
    <div class="col-12">
        <p class="text-muted text-center">
            共 {{ page_obj.paginator.count }} 条记录
        </p>
    </div>
</div>
{% endif %}

<!-- 状态更新模态框 -->
<div class="modal fade" id="statusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">更新订单状态</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="statusForm">
                    {% csrf_token %}
                    <input type="hidden" id="orderId" name="order_id">
                    <div class="mb-3">
                        <label for="orderStatus" class="form-label">选择新状态</label>
                        <select class="form-select" id="orderStatus" name="status" required>
                            {% for status_code, status_name in status_choices %}
                            <option value="{{ status_code }}">{{ status_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmUpdate">确认更新</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 快速状态更新
    $('.update-status').click(function() {
        const orderId = $(this).data('order-id');
        const status = $(this).data('status');
        
        let confirmText = '';
        switch(status) {
            case 'shipped':
                confirmText = '确定要将此订单标记为已发货吗？';
                break;
            case 'delivered':
                confirmText = '确定要将此订单标记为已送达吗？';
                break;
            case 'cancelled':
                confirmText = '确定要取消此订单吗？';
                break;
            default:
                confirmText = '确定要更新此订单状态吗？';
        }
        
        if (confirm(confirmText)) {
            updateOrderStatus(orderId, status);
        }
    });
    
    // 更新订单状态
    function updateOrderStatus(orderId, status) {
        $.post('{% url "orders:update_status" 0 %}'.replace('0', orderId), {
            'status': status,
            'csrfmiddlewaretoken': '{{ csrf_token }}'
        })
        .done(function(data) {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '更新失败，请重试');
            }
        })
        .fail(function() {
            alert('更新失败，请重试');
        });
    }
});
</script>
{% endblock %}
