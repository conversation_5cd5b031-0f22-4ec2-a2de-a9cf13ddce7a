#!/usr/bin/env python3
"""
测试地址API功能
"""

import requests
import json
from urllib.parse import urljoin
from bs4 import BeautifulSoup

def test_address_api():
    base_url = "http://127.0.0.1:8001"
    session = requests.Session()
    
    print("🔍 测试地址API功能...")
    print("=" * 50)
    
    # 1. 先登录
    print("1. 用户登录...")
    try:
        # 获取登录页面和CSRF令牌
        login_url = urljoin(base_url, "/users/login/")
        response = session.get(login_url)
        
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
            
            if csrf_input:
                csrf_token = csrf_input.get('value')
                
                # 尝试登录
                login_data = {
                    'username': 'testuser',
                    'password': '123456',
                    'user_type': 'normal',
                    'csrfmiddlewaretoken': csrf_token
                }
                
                login_response = session.post(login_url, data=login_data)
                if login_response.status_code == 302:
                    print("   ✅ 登录成功")
                else:
                    print("   ❌ 登录失败")
                    return
            else:
                print("   ❌ 无法获取CSRF令牌")
                return
        else:
            print("   ❌ 无法访问登录页面")
            return
    except Exception as e:
        print(f"   ❌ 登录异常: {e}")
        return
    
    # 2. 测试获取地址列表
    print("\n2. 测试获取地址列表...")
    try:
        response = session.get(urljoin(base_url, "/users/api/addresses/"))
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 获取地址列表成功，共 {len(data)} 个地址")
        else:
            print(f"   ❌ 获取地址列表失败: {response.status_code}")
            print(f"   响应内容: {response.text[:200]}")
    except Exception as e:
        print(f"   ❌ 获取地址列表异常: {e}")
    
    # 3. 测试添加地址
    print("\n3. 测试添加地址...")
    try:
        # 获取CSRF令牌
        csrf_token = None
        for cookie in session.cookies:
            if cookie.name == 'csrftoken':
                csrf_token = cookie.value
                break
        
        if not csrf_token:
            # 从页面获取CSRF令牌
            address_page = session.get(urljoin(base_url, "/users/address/"))
            if address_page.status_code == 200:
                soup = BeautifulSoup(address_page.text, 'html.parser')
                csrf_input = soup.find('input', {'name': 'csrfmiddlewaretoken'})
                if csrf_input:
                    csrf_token = csrf_input.get('value')
        
        if csrf_token:
            address_data = {
                'receiver': '张三',
                'phone': '13800138000',
                'province': '北京市',
                'city': '北京市',
                'district': '朝阳区',
                'detail': '三里屯街道1号',
                'is_default': True
            }
            
            headers = {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrf_token
            }
            
            response = session.post(
                urljoin(base_url, "/users/api/addresses/"),
                data=json.dumps(address_data),
                headers=headers
            )
            
            print(f"   状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
            if response.status_code in [200, 201]:
                print("   ✅ 添加地址成功")
            else:
                print(f"   ❌ 添加地址失败: {response.status_code}")
        else:
            print("   ❌ 无法获取CSRF令牌")
    except Exception as e:
        print(f"   ❌ 添加地址异常: {e}")
    
    # 4. 再次获取地址列表，验证添加是否成功
    print("\n4. 验证地址添加结果...")
    try:
        response = session.get(urljoin(base_url, "/users/api/addresses/"))
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 当前地址数量: {len(data)}")
            if data:
                print("   地址列表:")
                for addr in data:
                    print(f"     - {addr.get('receiver', 'N/A')} {addr.get('phone', 'N/A')}")
                    print(f"       {addr.get('province', '')} {addr.get('city', '')} {addr.get('district', '')} {addr.get('detail', '')}")
        else:
            print(f"   ❌ 获取地址列表失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 验证地址异常: {e}")
    
    # 5. 测试地址页面访问
    print("\n5. 测试地址页面访问...")
    try:
        response = session.get(urljoin(base_url, "/users/address/"))
        if response.status_code == 200:
            print("   ✅ 地址页面访问正常")
            # 检查页面内容
            if "收货地址" in response.text:
                print("   ✅ 页面内容正常")
            else:
                print("   ⚠️  页面内容可能异常")
        else:
            print(f"   ❌ 地址页面访问失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 地址页面访问异常: {e}")
    
    print("\n🎉 地址API测试完成!")

if __name__ == "__main__":
    test_address_api()
