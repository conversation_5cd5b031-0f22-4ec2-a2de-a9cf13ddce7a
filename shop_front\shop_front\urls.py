"""
URL configuration for shop_front project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from shop_front.api_views import confirm_order_receipt

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('home.urls', namespace='home')),  # 首页
    path('users/', include('users.urls', namespace='users')),
    path('goods/', include('goods.urls', namespace='goods')),
    path('order/', include('order.urls', namespace='order')),
    path('payment/', include('payment.urls', namespace='payment')),
    path('reviews/', include('reviews.urls', namespace='reviews')),
    path('account/', include('account.urls', namespace='account')),
    path('common/', include('common.urls', namespace='common')),
    
    # API端点
    path('api/orders/<str:order_number>/receive/', confirm_order_receipt, name='confirm_order_receipt'),
]

# 添加媒体文件URL配置
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# 在DEBUG模式下添加静态文件URL配置
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
