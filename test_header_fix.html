<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>顶栏修复测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        
        /* 顶部导航条 */
        .topbar {
            height: 35px;
            line-height: 35px;
            background: #f5f5f5;
            border-bottom: 1px solid #e5e5e5;
            font-size: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        
        .topbar .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1300px;
            margin: 0 auto;
        }
        
        .topbar .welcome {
            margin-right: 10px;
            color: #666;
            display: flex;
            align-items: center;
        }
        
        .topbar .hello {
            color: #666;
            margin-right: 15px;
            font-weight: 500;
        }
        
        .topbar-nav {
            display: flex;
            align-items: center;
        }
        
        .topbar-nav .list {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
            align-items: center;
        }
        
        .topbar-nav .list li {
            margin-left: 15px;
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .topbar-nav .list li a {
            color: #666;
            text-decoration: none;
            padding: 0 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            white-space: nowrap;
        }
        
        .topbar-nav .list li a:hover {
            color: #dc3545;
        }
        
        .topbar-nav .list li a i {
            margin-right: 3px;
            color: #dc3545;
        }
        
        /* 头部 */
        .header {
            height: 120px;
            padding: 10px 0 20px;
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            margin-bottom: 15px;
        }
        
        .header .container {
            display: flex;
            align-items: center;
            max-width: 1300px;
            margin: 0 auto;
        }
        
        .header .logo {
            width: 190px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 0;
            padding-top: 0;
        }
        
        .header .logo a {
            display: block;
            text-decoration: none;
        }
        
        .header .logo img {
            max-height: 75px;
            border-radius: 8px;
            margin: 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .header .logo img:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        
        .header .search-container {
            flex: 1;
            margin: 0 30px 0 40px;
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .header .search {
            width: 100%;
            max-width: 520px;
            height: 46px;
            position: relative;
            margin: 12px 0 6px 0;
            border-radius: 23px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: #fff;
            border: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
        }
        
        .header .search input {
            flex: 1;
            height: 100%;
            padding: 0 70px 0 20px;
            border: none;
            outline: none;
            font-size: 14px;
            border-radius: 23px;
            background: transparent;
            transition: all 0.3s ease;
            line-height: normal;
            box-sizing: border-box;
            text-align: left;
            color: #333;
        }
        
        .header .search button {
            position: absolute;
            right: 4px;
            top: 4px;
            width: 60px;
            height: 38px;
            background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%);
            color: #fff;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            border-radius: 19px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
        }
        
        .header .header-right {
            width: 200px;
            text-align: right;
        }
        
        .user-info {
            display: inline-flex;
            align-items: center;
            margin-right: 15px;
            padding: 2px 8px;
            background: transparent;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 8px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }
        
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .user-details {
            display: flex;
            align-items: center;
            line-height: 1;
        }
        
        .user-greeting {
            font-size: 12px;
            color: #666;
            margin-right: 3px;
        }
        
        .username {
            font-size: 12px;
            font-weight: bold;
            color: #333;
        }
        
        .test-content {
            max-width: 1300px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dc3545;
        }
        
        .test-header h1 {
            color: #dc3545;
            margin: 0;
        }
        
        .fix-list {
            list-style: none;
            padding: 0;
        }
        
        .fix-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .fix-list li:last-child {
            border-bottom: none;
        }
        
        .fix-list li i {
            color: #28a745;
            margin-right: 10px;
            font-size: 16px;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .comparison-item {
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        
        .comparison-item.before {
            background: #fff5f5;
            border-color: #dc3545;
        }
        
        .comparison-item.after {
            background: #f0fff4;
            border-color: #28a745;
        }
        
        .comparison-item h3 {
            margin-top: 0;
            color: #333;
        }
        
        .comparison-item.before h3 {
            color: #dc3545;
        }
        
        .comparison-item.after h3 {
            color: #28a745;
        }
    </style>
</head>
<body>
    <!-- 顶部导航条 -->
    <div class="topbar">
        <div class="container">
            <div class="welcome">
                <span class="hello">欢迎来到MARS BUY！</span>
            </div>
            <div class="topbar-nav">
                <ul class="list">
                    <li><a href="#"><i class="fas fa-shopping-bag"></i> 我的订单</a></li>
                    <li><a href="#"><i class="fas fa-shopping-cart"></i> 购物车</a></li>
                    <li><a href="#"><i class="fas fa-heart"></i> 收藏夹</a></li>
                    <li><a href="#"><i class="fas fa-star"></i> 我的评价</a></li>
                    <li><a href="#"><i class="fas fa-user-circle"></i> 个人中心</a></li>
                    <li><a href="#"><i class="fas fa-headset"></i> 在线客服</a></li>
                    <li>
                        <div class="user-info">
                            <div class="user-avatar">
                                <img src="https://via.placeholder.com/24x24/dc3545/ffffff?text=U" alt="用户头像">
                            </div>
                            <div class="user-details">
                                <span class="user-greeting">欢迎，</span>
                                <span class="username">测试用户</span>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 头部 -->
    <div class="header">
        <div class="container">
            <div class="logo">
                <a href="#">
                    <img src="https://via.placeholder.com/150x75/dc3545/ffffff?text=MARS+BUY" alt="MARS BUY Logo">
                </a>
            </div>
            <div class="search-container">
                <div class="search">
                    <input type="text" placeholder="搜索 商品/品牌/店铺">
                    <button type="submit">搜索</button>
                </div>
            </div>
            <div class="header-right">
                <div style="text-align: center; font-size: 12px;">
                    <div class="user-avatar" style="margin: 0 auto 5px; width: 40px; height: 40px;">
                        <img src="https://via.placeholder.com/40x40/dc3545/ffffff?text=U" alt="用户头像" style="border-radius: 50%; width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div>测试用户</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 测试内容 -->
    <div class="test-content">
        <div class="test-header">
            <h1><i class="fas fa-tools"></i> 顶栏修复测试</h1>
            <p>验证MARS BUY logo位置和顶栏竖线问题的修复效果</p>
        </div>

        <div class="comparison">
            <div class="comparison-item before">
                <h3><i class="fas fa-times-circle"></i> 修复前问题</h3>
                <ul class="fix-list">
                    <li><i class="fas fa-exclamation-triangle"></i> MARS BUY logo位置过低，"掉下来"了</li>
                    <li><i class="fas fa-exclamation-triangle"></i> 顶栏导航项目之间有不需要的竖线分隔</li>
                    <li><i class="fas fa-exclamation-triangle"></i> logo使用了过多的负边距调整</li>
                    <li><i class="fas fa-exclamation-triangle"></i> 整体布局不够协调</li>
                </ul>
            </div>
            
            <div class="comparison-item after">
                <h3><i class="fas fa-check-circle"></i> 修复后效果</h3>
                <ul class="fix-list">
                    <li><i class="fas fa-check"></i> MARS BUY logo垂直居中对齐，位置正常</li>
                    <li><i class="fas fa-check"></i> 移除了顶栏导航的竖线分隔符</li>
                    <li><i class="fas fa-check"></i> 简化了logo的CSS定位，使用标准的flex布局</li>
                    <li><i class="fas fa-check"></i> 整体布局更加协调美观</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
            <h3><i class="fas fa-info-circle"></i> 修复说明</h3>
            <p><strong>1. Logo位置修复：</strong></p>
            <ul>
                <li>移除了 <code>top: -10px</code> 和 <code>margin-top: -15px</code> 的负边距设置</li>
                <li>将 <code>align-items: flex-start</code> 改为 <code>align-items: center</code> 实现垂直居中</li>
                <li>简化了CSS结构，使用标准的flex布局</li>
            </ul>
            
            <p><strong>2. 顶栏竖线移除：</strong></p>
            <ul>
                <li>移除了 <code>border-right: 1px solid #ddd</code> 样式</li>
                <li>删除了相关的 <code>:last-child</code> 特殊处理</li>
                <li>保持了导航项目之间的适当间距</li>
            </ul>
            
            <p><strong>3. 整体效果：</strong></p>
            <ul>
                <li>✅ Logo位置正常，不再"掉下来"</li>
                <li>✅ 顶栏导航简洁清爽，无多余竖线</li>
                <li>✅ 布局更加协调统一</li>
                <li>✅ 保持了原有的功能和交互效果</li>
            </ul>
        </div>
    </div>
</body>
</html>
