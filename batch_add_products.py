#!/usr/bin/env python3
"""
批量添加商品脚本
根据图片文件夹和商品信息批量创建商品
"""

import os
import sys
import django
import shutil
from decimal import Decimal
from pathlib import Path

# 设置Django环境
sys.path.append('D:/python/shop/backn')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backn.settings')
django.setup()

from goods.models import Category, Product
from django.core.files import File
from django.db import transaction

# 商品数据
PRODUCTS_DATA = [
    {
        'name': '潮流工装风多口袋休闲裤',
        'category': '时尚穿搭',
        'subcategory': '男装',
        'description': '潮流工装风多口袋休闲裤，耐磨牛仔面料，立体剪裁，适合日常出行与户外探索。',
        'price': 199.00,
        'stock': 100,
        'image_name': '潮流工装风多口袋休闲裤.png'
    },
    {
        'name': '法式复古方领碎花连衣裙',
        'category': '时尚穿搭',
        'subcategory': '女装',
        'description': '法式复古方领碎花连衣裙，轻盈雪纺材质，收腰设计，尽显优雅气质。',
        'price': 299.00,
        'stock': 80,
        'image_name': '法式复古方领碎花连衣裙.png'
    },
    {
        'name': '卡通恐龙图案连帽卫衣',
        'category': '时尚穿搭',
        'subcategory': '童装',
        'description': '卡通恐龙图案连帽卫衣，加绒内里，柔软亲肤，让孩子温暖又可爱。',
        'price': 129.00,
        'stock': 120,
        'image_name': '卡通恐龙图案连帽卫衣.png'
    },
    {
        'name': '玻尿酸保湿补水面膜',
        'category': '美妆护肤',
        'subcategory': '面部护理',
        'description': '玻尿酸保湿补水面膜，富含高浓度玻尿酸精华，深层滋润，让肌肤水润透亮。',
        'price': 89.00,
        'stock': 200,
        'image_name': '玻尿酸保湿补水面膜.png'
    },
    {
        'name': '雾面哑光口红',
        'category': '美妆护肤',
        'subcategory': '彩妆',
        'description': '雾面哑光口红，显色度高，不易掉色，20+色号可选，满足不同妆容需求。',
        'price': 59.00,
        'stock': 300,
        'image_name': '雾面哑光口红.png'
    },
    {
        'name': '磨砂去角质沐浴露',
        'category': '美妆护肤',
        'subcategory': '身体护理',
        'description': '磨砂去角质沐浴露，含天然核桃壳颗粒，温和去角质，肌肤光滑细腻。',
        'price': 39.00,
        'stock': 150,
        'image_name': '磨砂去角质沐浴露.png'
    },
    {
        'name': '全棉磨毛四件套',
        'category': '家居日用',
        'subcategory': '家纺床品',
        'description': '全棉磨毛四件套，柔软亲肤，保暖性佳，秋冬睡眠更舒适。',
        'price': 259.00,
        'stock': 60,
        'image_name': '全棉磨毛四件套.png'
    },
    {
        'name': '不粘涂层平底锅',
        'category': '家居日用',
        'subcategory': '厨房用品',
        'description': '不粘涂层平底锅，麦饭石材质，轻油烟易清洗，煎炒烹炸皆可。',
        'price': 159.00,
        'stock': 90,
        'image_name': '不粘涂层平底锅.png'
    },
    {
        'name': '抽屉式塑料收纳箱',
        'category': '家居日用',
        'subcategory': '收纳用品',
        'description': '抽屉式塑料收纳箱，透明可视设计，分格收纳，整理衣物杂物更方便。',
        'price': 79.00,
        'stock': 200,
        'image_name': '抽屉式塑料收纳箱.png'
    },
    {
        'name': '磁吸无线充电宝',
        'category': '数码科技',
        'subcategory': '手机及配件',
        'description': '磁吸无线充电宝，10000mAh大容量，轻薄便携，随时为手机续航。',
        'price': 199.00,
        'stock': 150,
        'image_name': '磁吸无线充电宝.png'
    },
    {
        'name': '便携式蓝牙键盘',
        'category': '数码科技',
        'subcategory': '电脑办公设备',
        'description': '便携式蓝牙键盘，小巧轻便，兼容多系统，出差办公更高效。',
        'price': 129.00,
        'stock': 100,
        'image_name': '便携式蓝牙键盘.png'
    },
    {
        'name': '微单相机',
        'category': '数码科技',
        'subcategory': '摄影摄像器材',
        'description': '微单相机，2400万像素，4K视频拍摄，满足专业摄影与日常记录。',
        'price': 3999.00,
        'stock': 30,
        'image_name': '微单相机.png'
    },
    {
        'name': '双开门风冷无霜冰箱',
        'category': '家用电器',
        'subcategory': '大家电',
        'description': '双开门风冷无霜冰箱，500L超大容量，分区合理，保鲜效果出色。',
        'price': 2999.00,
        'stock': 20,
        'image_name': '双开门风冷无霜冰箱.png'
    },
    {
        'name': '多功能空气炸锅',
        'category': '家用电器',
        'subcategory': '厨房小家电',
        'description': '多功能空气炸锅，无油烹饪，智能触控，轻松制作健康美食。',
        'price': 399.00,
        'stock': 80,
        'image_name': '多功能空气炸锅.png'
    },
    {
        'name': '静音加湿器',
        'category': '家用电器',
        'subcategory': '生活小家电',
        'description': '静音加湿器，大容量水箱，超声波雾化，缓解干燥，呵护肌肤。',
        'price': 199.00,
        'stock': 120,
        'image_name': '静音加湿器.png'
    },
    {
        'name': '混合坚果大礼包',
        'category': '食品生鲜',
        'subcategory': '休闲零食',
        'description': '混合坚果大礼包，含巴旦木、腰果、核桃等多种坚果，营养丰富，开袋即食。',
        'price': 89.00,
        'stock': 200,
        'image_name': '混合坚果大礼包.png'
    },
    {
        'name': '五常大米',
        'category': '食品生鲜',
        'subcategory': '粮油副食',
        'description': '五常大米，颗粒饱满，蒸煮后香气四溢，口感软糯有嚼劲。',
        'price': 59.00,
        'stock': 300,
        'image_name': '五常大米.png'
    },
    {
        'name': '精酿啤酒礼盒',
        'category': '食品生鲜',
        'subcategory': '酒水饮料',
        'description': '精酿啤酒礼盒，6种不同风味，麦芽香气浓郁，口感醇厚。',
        'price': 199.00,
        'stock': 100,
        'image_name': '精酿啤酒礼盒.png'
    },
    {
        'name': '婴儿恒温调奶器',
        'category': '母婴亲子',
        'subcategory': '母婴用品',
        'description': '婴儿恒温调奶器，30秒快速调温，精准控温，冲泡奶粉更便捷。',
        'price': 299.00,
        'stock': 80,
        'image_name': '婴儿恒温调奶器.png'
    },
    {
        'name': '益智积木桌套装',
        'category': '母婴亲子',
        'subcategory': '儿童玩具',
        'description': '益智积木桌套装，多种形状积木，锻炼孩子动手能力与创造力。',
        'price': 399.00,
        'stock': 60,
        'image_name': '益智积木桌套装.png'
    },
    {
        'name': '点读笔',
        'category': '母婴亲子',
        'subcategory': '早教学习产品',
        'description': '点读笔，涵盖海量绘本资源，双语教学，助力孩子启蒙学习。',
        'price': 199.00,
        'stock': 100,
        'image_name': '点读笔.png'
    },
    {
        'name': '速干运动T恤',
        'category': '运动户外',
        'subcategory': '运动服饰',
        'description': '速干运动T恤，透气排汗面料，立体剪裁，运动时无束缚感。',
        'price': 89.00,
        'stock': 200,
        'image_name': '速干运动 T 恤.png'
    },
    {
        'name': '家用可折叠跑步机',
        'category': '运动户外',
        'subcategory': '健身器材',
        'description': '家用可折叠跑步机，减震静音设计，满足家庭健身需求。',
        'price': 1999.00,
        'stock': 25,
        'image_name': '家用可折叠跑步机.png'
    },
    {
        'name': '专业羽毛球拍',
        'category': '运动户外',
        'subcategory': '球类运动用品',
        'description': '专业羽毛球拍，碳纤维材质，重量轻，弹性好，提升击球手感。',
        'price': 299.00,
        'stock': 80,
        'image_name': '专业羽毛球拍.png'
    },
    {
        'name': '网红车载香薰',
        'category': '汽车相关',
        'subcategory': '汽车装饰',
        'description': '网红车载香薰，ins风设计，多种香型可选，持久散发迷人香气。',
        'price': 39.00,
        'stock': 300,
        'image_name': '网红车载香薰.png'
    },
    {
        'name': '全合成机油',
        'category': '汽车相关',
        'subcategory': '汽车保养',
        'description': '全合成机油，适配多种车型，抗磨损，延长发动机寿命。',
        'price': 199.00,
        'stock': 100,
        'image_name': '全合成机油.png'
    },
    {
        'name': '手机车载支架',
        'category': '汽车相关',
        'subcategory': '车载用品',
        'description': '手机车载支架，360°旋转，重力感应，稳固夹持手机。',
        'price': 59.00,
        'stock': 200,
        'image_name': '手机车载支架.png'
    },
    {
        'name': '视频平台年度会员',
        'category': '生活服务',
        'subcategory': '虚拟商品',
        'description': '视频平台年度会员，畅享海量影视资源，无广告观看体验。',
        'price': 199.00,
        'stock': 1000,
        'image_name': '视频平台年度会员.png'
    },
    {
        'name': '深度家庭保洁服务',
        'category': '生活服务',
        'subcategory': '家政服务',
        'description': '深度家庭保洁服务，专业团队上门，全方位清洁，让家焕然一新。',
        'price': 299.00,
        'stock': 50,
        'image_name': '深度家庭保洁服务.png'
    },
    {
        'name': '热门火锅双人套餐',
        'category': '生活服务',
        'subcategory': '本地生活团购',
        'description': '热门火锅双人套餐，新鲜食材，地道锅底，享受超值美食体验。',
        'price': 159.00,
        'stock': 100,
        'image_name': '热门火锅双人套餐.png'
    }
]

# 图片源文件夹
SOURCE_IMAGE_DIR = Path('D:/python/shop/media/products/hcy')
# 目标图片文件夹
TARGET_IMAGE_DIR = Path('D:/python/shop/media/products')

def create_categories():
    """创建分类结构"""
    print("🏗️ 创建分类结构...")
    
    # 分类数据
    categories_data = {
        '时尚穿搭': ['男装', '女装', '童装'],
        '美妆护肤': ['面部护理', '彩妆', '身体护理'],
        '家居日用': ['家纺床品', '厨房用品', '收纳用品'],
        '数码科技': ['手机及配件', '电脑办公设备', '摄影摄像器材'],
        '家用电器': ['大家电', '厨房小家电', '生活小家电'],
        '食品生鲜': ['休闲零食', '粮油副食', '酒水饮料'],
        '母婴亲子': ['母婴用品', '儿童玩具', '早教学习产品'],
        '运动户外': ['运动服饰', '健身器材', '球类运动用品'],
        '汽车相关': ['汽车装饰', '汽车保养', '车载用品'],
        '生活服务': ['虚拟商品', '家政服务', '本地生活团购']
    }
    
    created_categories = {}
    
    for parent_name, children_names in categories_data.items():
        # 创建或获取父分类
        parent_category, created = Category.objects.get_or_create(
            name=parent_name,
            parent=None,
            defaults={
                'is_active': True,
                'sort_order': len(created_categories) + 1
            }
        )
        
        if created:
            print(f"✅ 创建父分类: {parent_name}")
        else:
            print(f"📁 使用现有父分类: {parent_name}")
        
        created_categories[parent_name] = parent_category
        
        # 创建子分类
        for i, child_name in enumerate(children_names):
            child_category, created = Category.objects.get_or_create(
                name=child_name,
                parent=parent_category,
                defaults={
                    'is_active': True,
                    'sort_order': i + 1
                }
            )
            
            if created:
                print(f"  ✅ 创建子分类: {child_name}")
            else:
                print(f"  📁 使用现有子分类: {child_name}")
            
            created_categories[f"{parent_name}-{child_name}"] = child_category
    
    return created_categories


def copy_and_create_products(categories):
    """复制图片并创建商品"""
    print(f"\n📦 开始批量创建商品...")

    created_count = 0
    error_count = 0

    for product_data in PRODUCTS_DATA:
        try:
            # 获取分类
            category_key = f"{product_data['category']}-{product_data['subcategory']}"
            category = categories.get(category_key)

            if not category:
                print(f"❌ 找不到分类: {category_key}")
                error_count += 1
                continue

            # 检查商品是否已存在
            if Product.objects.filter(name=product_data['name']).exists():
                print(f"⚠️ 商品已存在，跳过: {product_data['name']}")
                continue

            # 处理图片
            source_image_path = SOURCE_IMAGE_DIR / product_data['image_name']
            if not source_image_path.exists():
                print(f"❌ 图片文件不存在: {source_image_path}")
                error_count += 1
                continue

            # 复制图片到目标目录
            target_image_path = TARGET_IMAGE_DIR / product_data['image_name']
            shutil.copy2(source_image_path, target_image_path)

            # 创建商品
            product = Product.objects.create(
                name=product_data['name'],
                category=category,
                description=product_data['description'],
                price=Decimal(str(product_data['price'])),
                stock=product_data['stock'],
                is_hot=created_count < 5,  # 前5个商品设为热门
                is_new=True,
                is_active=True
            )

            # 设置商品图片
            with open(target_image_path, 'rb') as f:
                product.main_image.save(
                    product_data['image_name'],
                    File(f),
                    save=True
                )

            print(f"✅ 创建商品: {product.name} (分类: {category.parent.name} > {category.name})")
            created_count += 1

        except Exception as e:
            print(f"❌ 创建商品失败 {product_data['name']}: {e}")
            error_count += 1

    return created_count, error_count


def update_product_stats():
    """更新商品统计信息"""
    print(f"\n📊 更新商品统计信息...")

    import random

    products = Product.objects.all()
    for product in products:
        # 随机设置销量
        product.sales = random.randint(0, 500)
        product.save()

    print(f"✅ 更新了 {products.count()} 个商品的统计信息")


def show_summary():
    """显示创建结果摘要"""
    print(f"\n📋 创建结果摘要:")
    print("=" * 60)

    # 分类统计
    total_categories = Category.objects.count()
    parent_categories = Category.objects.filter(parent=None).count()
    child_categories = Category.objects.filter(parent__isnull=False).count()

    print(f"分类统计:")
    print(f"  总分类数: {total_categories}")
    print(f"  主分类数: {parent_categories}")
    print(f"  子分类数: {child_categories}")

    # 商品统计
    total_products = Product.objects.count()
    active_products = Product.objects.filter(is_active=True).count()
    hot_products = Product.objects.filter(is_hot=True).count()
    new_products = Product.objects.filter(is_new=True).count()

    print(f"\n商品统计:")
    print(f"  总商品数: {total_products}")
    print(f"  已上架商品: {active_products}")
    print(f"  热门商品: {hot_products}")
    print(f"  新品商品: {new_products}")

    # 按分类统计商品
    print(f"\n按分类统计商品:")
    parent_categories = Category.objects.filter(parent=None)
    for parent in parent_categories:
        total_in_category = Product.objects.filter(category__parent=parent).count()
        print(f"  {parent.name}: {total_in_category} 个商品")

        for child in parent.children.all():
            child_products = Product.objects.filter(category=child).count()
            if child_products > 0:
                print(f"    - {child.name}: {child_products} 个")


def main():
    """主函数"""
    print("🚀 开始批量添加商品...")
    print("=" * 80)

    try:
        with transaction.atomic():
            # 1. 创建分类结构
            categories = create_categories()

            # 2. 复制图片并创建商品
            created_count, error_count = copy_and_create_products(categories)

            # 3. 更新商品统计信息
            update_product_stats()

            # 4. 显示结果摘要
            show_summary()

            print(f"\n🎉 批量添加完成!")
            print(f"✅ 成功创建 {created_count} 个商品")
            if error_count > 0:
                print(f"❌ 失败 {error_count} 个商品")

            print(f"\n📝 后续操作建议:")
            print("1. 访问后台管理系统查看创建的分类和商品")
            print("2. 可以在分类管理中添加自定义子分类")
            print("3. 可以编辑商品信息、价格、库存等")
            print("4. 可以上传更多商品图片")

            print(f"\n🌐 访问链接:")
            print("后台商品管理: http://127.0.0.1:8003/goods/")
            print("后台分类管理: http://127.0.0.1:8003/goods/categories/")
            print("前台商品展示: http://127.0.0.1:8001/goods/")

    except Exception as e:
        print(f"❌ 批量添加过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
