{% extends 'base.html' %}

{% block title %}添加分类 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}添加分类{% endblock %}

{% block page_actions %}
<a href="{% url 'goods:category_list' %}" class="btn btn-outline-secondary">
    <i class="fas fa-arrow-left me-1"></i>返回列表
</a>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-plus me-2"></i>分类信息
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">分类名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="form-text">请输入分类名称，不能为空</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="parent" class="form-label">父分类</label>
                        <select class="form-select" id="parent" name="parent">
                            <option value="">顶级分类</option>
                            {% for category in parent_categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text">选择父分类，留空则为顶级分类</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="icon" class="form-label">分类图标</label>
                        <input type="file" class="form-control" id="icon" name="icon" accept="image/*">
                        <div class="form-text">支持 JPG、PNG、GIF 格式，建议尺寸 64x64 像素</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">排序</label>
                        <input type="number" class="form-control" id="sort_order" name="sort_order" value="0" min="0">
                        <div class="form-text">数字越小排序越靠前</div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">
                            启用分类
                        </label>
                        <div class="form-text">禁用后该分类将不会在前台显示</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存
                        </button>
                        <a href="{% url 'goods:category_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>操作说明
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        分类名称为必填项
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        可以设置父分类创建多级分类
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        排序数字越小越靠前
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        禁用的分类不会在前台显示
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        建议上传 64x64 像素的图标
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
