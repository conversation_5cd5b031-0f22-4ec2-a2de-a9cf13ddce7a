<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轮播图修复测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dc3545;
        }
        
        .test-header h1 {
            color: #dc3545;
            margin: 0;
        }
        
        /* 轮播图样式 - 修复版本 */
        .banner-box {
            width: 100%;
            height: 400px;
            position: relative;
            overflow: hidden;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .banner-list {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .banner-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: none;
            opacity: 0;
            transition: all 0.5s ease-in-out;
        }

        .banner-item.active {
            display: block;
            opacity: 1;
        }

        .banner-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .banner-dots {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 10;
        }

        .banner-dots span {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .banner-dots span.active {
            background: #dc3545;
            transform: scale(1.2);
        }

        .banner-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 50px;
            height: 50px;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            z-index: 10;
        }

        .banner-box:hover .banner-arrow {
            opacity: 1;
        }

        .banner-arrow:hover {
            background: rgba(220, 53, 69, 0.8);
        }

        .banner-arrow.prev {
            left: 20px;
        }

        .banner-arrow.next {
            right: 20px;
        }
        
        .status-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: bold;
            color: #495057;
        }
        
        .status-value {
            color: #28a745;
            font-family: monospace;
        }
        
        .log-panel {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            height: 150px;
            overflow-y: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-header">
            <h1><i class="fas fa-tools"></i> 轮播图修复测试</h1>
            <p>测试修复后的轮播图自动切换功能</p>
        </div>

        <!-- 轮播图 -->
        <div class="banner-box">
            <div class="banner-list">
                <div class="banner-item active">
                    <img src="https://via.placeholder.com/800x400/dc3545/ffffff?text=轮播图1" alt="轮播图1">
                </div>
                <div class="banner-item">
                    <img src="https://via.placeholder.com/800x400/28a745/ffffff?text=轮播图2" alt="轮播图2">
                </div>
                <div class="banner-item">
                    <img src="https://via.placeholder.com/800x400/007bff/ffffff?text=轮播图3" alt="轮播图3">
                </div>
                <div class="banner-item">
                    <img src="https://via.placeholder.com/800x400/ffc107/000000?text=轮播图4" alt="轮播图4">
                </div>
            </div>
            <!-- 轮播图指示点 -->
            <div class="banner-dots">
                <span class="active"></span>
                <span></span>
                <span></span>
                <span></span>
            </div>
            <!-- 轮播图箭头 -->
            <div class="banner-arrow prev"><i class="fas fa-chevron-left"></i></div>
            <div class="banner-arrow next"><i class="fas fa-chevron-right"></i></div>
        </div>

        <!-- 状态面板 -->
        <div class="status-panel">
            <h3><i class="fas fa-info-circle"></i> 轮播图状态</h3>
            <div class="status-item">
                <span class="status-label">当前图片:</span>
                <span class="status-value" id="current-index">1 / 4</span>
            </div>
            <div class="status-item">
                <span class="status-label">自动轮播:</span>
                <span class="status-value" id="auto-status">运行中</span>
            </div>
            <div class="status-item">
                <span class="status-label">总切换次数:</span>
                <span class="status-value" id="switch-count">0</span>
            </div>
        </div>

        <!-- 日志面板 -->
        <div class="log-panel" id="log-panel">
            <div>🎠 轮播图修复测试控制台</div>
        </div>
    </div>

    <script>
        // 轮播图脚本 - 修复版本
        $(document).ready(function() {
            console.log('🎠 轮播图初始化开始...');

            // 轮播图变量
            var bannerBox = $('.banner-box');
            var bannerItems = $('.banner-item');
            var dots = $('.banner-dots span');
            var arrowPrev = $('.banner-arrow.prev');
            var arrowNext = $('.banner-arrow.next');
            var currentIndex = 0;
            var autoTimer = null;
            var isPlaying = false;
            var switchCount = 0;

            // 日志函数
            function log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logPanel = document.getElementById('log-panel');
                const logEntry = document.createElement('div');
                logEntry.textContent = `[${timestamp}] ${message}`;
                logPanel.appendChild(logEntry);
                logPanel.scrollTop = logPanel.scrollHeight;
                console.log(message);
            }

            // 更新状态显示
            function updateStatus() {
                document.getElementById('current-index').textContent = `${currentIndex + 1} / ${bannerItems.length}`;
                document.getElementById('auto-status').textContent = isPlaying ? '运行中' : '已暂停';
                document.getElementById('switch-count').textContent = switchCount;
            }

            log(`找到 ${bannerItems.length} 个轮播图项目`);
            log(`找到 ${dots.length} 个指示点`);

            // 如果没有轮播图项目，直接返回
            if (bannerItems.length <= 1) {
                log('❌ 轮播图项目不足，无需轮播');
                return;
            }

            // 初始化轮播图
            function initBanner() {
                log('🔧 初始化轮播图状态');
                bannerItems.removeClass('active').hide();
                bannerItems.eq(0).addClass('active').show();
                dots.removeClass('active');
                dots.eq(0).addClass('active');
                currentIndex = 0;
                updateStatus();
            }

            // 显示指定索引的轮播图
            function showSlide(index) {
                if (index < 0 || index >= bannerItems.length) {
                    log(`❌ 无效的索引: ${index}`);
                    return;
                }

                log(`🖼️ 切换到轮播图 ${index + 1}/${bannerItems.length}`);
                
                // 隐藏当前显示的图片
                bannerItems.removeClass('active').fadeOut(300);
                dots.removeClass('active');
                
                // 显示新图片
                setTimeout(function() {
                    bannerItems.eq(index).addClass('active').fadeIn(500);
                    dots.eq(index).addClass('active');
                    currentIndex = index;
                    switchCount++;
                    updateStatus();
                }, 300);
            }

            // 下一张
            function nextSlide() {
                var nextIndex = (currentIndex + 1) % bannerItems.length;
                log(`➡️ 下一张: ${currentIndex} -> ${nextIndex}`);
                showSlide(nextIndex);
            }

            // 上一张
            function prevSlide() {
                var prevIndex = (currentIndex - 1 + bannerItems.length) % bannerItems.length;
                log(`⬅️ 上一张: ${currentIndex} -> ${prevIndex}`);
                showSlide(prevIndex);
            }

            // 开始自动播放
            function startAutoPlay() {
                if (isPlaying) {
                    log('⚠️ 自动播放已在运行');
                    return;
                }
                
                log('▶️ 开始自动播放');
                isPlaying = true;
                autoTimer = setInterval(function() {
                    log(`🔄 自动播放: 当前 ${currentIndex}, 切换到下一张`);
                    nextSlide();
                }, 3000);
                updateStatus();
            }

            // 停止自动播放
            function stopAutoPlay() {
                if (!isPlaying) {
                    return;
                }
                
                log('⏸️ 停止自动播放');
                isPlaying = false;
                if (autoTimer) {
                    clearInterval(autoTimer);
                    autoTimer = null;
                }
                updateStatus();
            }

            // 重启自动播放
            function restartAutoPlay() {
                stopAutoPlay();
                setTimeout(startAutoPlay, 1000);
            }

            // 绑定事件
            arrowNext.off('click').on('click', function(e) {
                e.preventDefault();
                log('👆 点击下一张按钮');
                stopAutoPlay();
                nextSlide();
                restartAutoPlay();
            });

            arrowPrev.off('click').on('click', function(e) {
                e.preventDefault();
                log('👆 点击上一张按钮');
                stopAutoPlay();
                prevSlide();
                restartAutoPlay();
            });

            dots.off('click').on('click', function(e) {
                e.preventDefault();
                var index = $(this).index();
                log(`👆 点击指示点: ${index}`);
                if (index !== currentIndex) {
                    stopAutoPlay();
                    showSlide(index);
                    restartAutoPlay();
                }
            });

            // 鼠标悬停控制
            bannerBox.off('mouseenter mouseleave').on({
                mouseenter: function() {
                    log('🐭 鼠标进入，暂停自动播放');
                    stopAutoPlay();
                },
                mouseleave: function() {
                    log('🐭 鼠标离开，恢复自动播放');
                    startAutoPlay();
                }
            });

            // 初始化并启动
            initBanner();
            
            // 延迟启动自动播放
            setTimeout(function() {
                log('🚀 启动轮播图自动播放');
                startAutoPlay();
            }, 2000);

            log('✅ 轮播图初始化完成');
        });
    </script>
</body>
</html>
