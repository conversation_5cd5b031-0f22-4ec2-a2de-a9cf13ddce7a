from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.urls import reverse
from order.models import Order

@login_required
def confirm_order_receipt(request, order_number):
    """确认收货API"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': '请求方法不允许'}, status=405)
    
    # 获取订单并验证所有权
    order = get_object_or_404(Order, order_number=order_number, user=request.user)
    
    # 验证订单状态
    if order.status != 'shipped':
        return JsonResponse({'success': False, 'message': '只有已发货的订单可以确认收货'}, status=400)
    
    # 更新订单状态
    order.status = 'received'
    order.received_time = timezone.now()
    order.save()
    
    # 获取订单中第一个商品的ID用于评价
    first_product_id = None
    if order.items.exists():
        first_product_id = order.items.first().product.id
        review_url = reverse('reviews:add', args=[first_product_id])
    else:
        review_url = reverse('users:orders')
    
    return JsonResponse({
        'success': True,
        'message': '确认收货成功',
        'order_number': order.order_number,
        'status': order.status_display,
        'review_url': review_url  # 添加评价页面URL
    }) 