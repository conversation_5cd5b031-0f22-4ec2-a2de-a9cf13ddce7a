from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth import get_user_model
from orders.models import Order, OrderItem
from django.apps import apps

User = get_user_model()


class Command(BaseCommand):
    help = '删除所有用户和订单数据'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='确认删除所有数据',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    '⚠️  这将删除所有用户和订单数据！\n'
                    '请使用 --confirm 参数确认操作：\n'
                    'python manage.py clear_all_users_orders --confirm'
                )
            )
            return

        self.stdout.write('🗑️  开始删除所有用户和订单数据...')
        
        try:
            with transaction.atomic():
                # 统计删除前的数据
                user_count = User.objects.count()
                order_count = Order.objects.count()
                orderitem_count = OrderItem.objects.count()
                
                self.stdout.write(f'删除前统计:')
                self.stdout.write(f'  - 用户数量: {user_count}')
                self.stdout.write(f'  - 订单数量: {order_count}')
                self.stdout.write(f'  - 订单项数量: {orderitem_count}')
                
                # 删除订单项
                OrderItem.objects.all().delete()
                self.stdout.write('✅ 删除了所有订单项')
                
                # 删除订单
                Order.objects.all().delete()
                self.stdout.write('✅ 删除了所有订单')
                
                # 删除收藏数据（如果存在）
                try:
                    Favorite = apps.get_model('account', 'Favorite')
                    favorite_count = Favorite.objects.count()
                    Favorite.objects.all().delete()
                    self.stdout.write(f'✅ 删除了 {favorite_count} 个收藏记录')
                except:
                    pass
                
                # 删除购物车数据（如果存在）
                try:
                    Cart = apps.get_model('order', 'Cart')
                    CartItem = apps.get_model('order', 'CartItem')
                    cart_count = Cart.objects.count()
                    cartitem_count = CartItem.objects.count()
                    CartItem.objects.all().delete()
                    Cart.objects.all().delete()
                    self.stdout.write(f'✅ 删除了 {cart_count} 个购物车和 {cartitem_count} 个购物车项')
                except:
                    pass
                
                # 删除地址数据（如果存在）
                try:
                    Address = apps.get_model('account', 'Address')
                    address_count = Address.objects.count()
                    Address.objects.all().delete()
                    self.stdout.write(f'✅ 删除了 {address_count} 个地址记录')
                except:
                    pass
                
                # 删除评论数据（如果存在）
                try:
                    Review = apps.get_model('reviews', 'Review')
                    review_count = Review.objects.count()
                    Review.objects.all().delete()
                    self.stdout.write(f'✅ 删除了 {review_count} 个评论记录')
                except:
                    pass
                
                # 删除所有用户
                User.objects.all().delete()
                self.stdout.write('✅ 删除了所有用户')
                
                # 验证删除结果
                remaining_users = User.objects.count()
                remaining_orders = Order.objects.count()
                remaining_orderitems = OrderItem.objects.count()
                
                self.stdout.write(f'\n📊 删除后统计:')
                self.stdout.write(f'  - 剩余用户: {remaining_users}')
                self.stdout.write(f'  - 剩余订单: {remaining_orders}')
                self.stdout.write(f'  - 剩余订单项: {remaining_orderitems}')
                
                if remaining_users == 0 and remaining_orders == 0 and remaining_orderitems == 0:
                    self.stdout.write(self.style.SUCCESS('\n🎉 所有用户和订单数据删除成功！'))
                else:
                    self.stdout.write(self.style.WARNING('\n⚠️  部分数据可能未完全删除'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'❌ 删除数据时出错: {e}'))
            
        self.stdout.write('\n🏁 数据清理操作完成！')
