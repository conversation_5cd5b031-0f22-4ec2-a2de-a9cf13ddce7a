{% extends 'base.html' %}
{% load static %}

{% block title %}我的订单 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 全局样式 */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    /* 订单列表整体样式 */
    .orders-container {
        max-width: 1200px;
        margin: 30px auto;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 20px;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
        overflow: hidden;
    }

    /* 订单头部 */
    .orders-header {
        padding: 25px 30px;
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .orders-title {
        font-size: 24px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .orders-title i {
        font-size: 28px;
        color: #ffeb3b;
    }

    .orders-filter {
        display: flex;
        gap: 10px;
    }

    .filter-btn {
        padding: 8px 15px;
        border: none;
        border-radius: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-weight: 600;
        background: rgba(255,255,255,0.2);
        color: white;
        backdrop-filter: blur(10px);
    }

    .filter-btn.active {
        background: white;
        color: #dc3545;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .filter-btn:hover {
        background: rgba(255,255,255,0.3);
        transform: translateY(-2px);
    }

    .filter-btn.active:hover {
        background: white;
    }

    /* 订单列表 */
    .order-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .order-item {
        margin: 20px;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 20px rgba(0,0,0,0.05);
        background: white;
        transition: all 0.3s ease;
    }

    .order-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(220,53,69,0.1);
    }

    /* 订单头部信息 */
    .order-header {
        padding: 15px 20px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid rgba(0,0,0,0.05);
    }

    .order-info {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
    }

    .order-sn {
        font-weight: 600;
        color: #333;
    }

    .order-date {
        color: #666;
    }

    .order-status {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .status-badge {
        padding: 5px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
    }

    .status-pending {
        background: rgba(255, 193, 7, 0.2);
        color: #ff9800;
    }

    .status-paid {
        background: rgba(0, 123, 255, 0.2);
        color: #007bff;
    }

    .status-shipped {
        background: rgba(23, 162, 184, 0.2);
        color: #17a2b8;
    }

    .status-completed {
        background: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }

    .status-cancelled {
        background: rgba(108, 117, 125, 0.2);
        color: #6c757d;
    }

    /* 订单商品 */
    .order-products {
        padding: 20px;
        display: flex;
        overflow-x: auto;
        gap: 15px;
    }

    .product-item {
        min-width: 200px;
        max-width: 200px;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s ease;
    }

    .product-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .product-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 10px;
    }

    .product-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .product-info {
        width: 100%;
        text-align: center;
    }

    .product-name {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .product-price {
        font-size: 12px;
        color: #dc3545;
    }

    /* 订单底部 */
    .order-footer {
        padding: 15px 20px;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid rgba(0,0,0,0.05);
    }

    .order-total {
        font-size: 16px;
        color: #666;
    }

    .total-price {
        font-size: 18px;
        font-weight: 700;
        color: #dc3545;
    }

    .order-actions {
        display: flex;
        gap: 10px;
    }

    .order-btn {
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: 600;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 5px;
    }

    .btn-detail {
        background: rgba(0, 123, 255, 0.1);
        color: #007bff;
        border: 1px solid #007bff;
    }

    .btn-pay {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        border: 1px solid #dc3545;
    }

    .btn-cancel {
        background: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border: 1px solid #6c757d;
    }

    .btn-confirm {
        background: rgba(40, 167, 69, 0.1);
        color: #28a745;
        border: 1px solid #28a745;
    }

    .btn-review {
        background: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border: 1px solid #ffc107;
    }

    .order-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .btn-detail:hover {
        background: #007bff;
        color: white;
    }

    .btn-pay:hover {
        background: #dc3545;
        color: white;
    }

    .btn-cancel:hover {
        background: #6c757d;
        color: white;
    }

    .btn-confirm:hover {
        background: #28a745;
        color: white;
    }

    .btn-review:hover {
        background: #ffc107;
        color: white;
    }

    /* 空订单样式 */
    .empty-order {
        padding: 80px 40px;
        text-align: center;
        background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
        border-radius: 20px;
        margin: 40px 0;
    }

    .empty-order i {
        font-size: 120px;
        color: #dc3545;
        margin-bottom: 30px;
        opacity: 0.7;
        animation: float 3s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .empty-order p {
        color: #666;
        margin-bottom: 30px;
        font-size: 18px;
        line-height: 1.6;
    }

    .go-shopping {
        padding: 15px 40px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border-radius: 30px;
        text-decoration: none;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
        transition: all 0.3s ease;
        display: inline-block;
    }

    .go-shopping:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .orders-header {
            flex-direction: column;
            gap: 15px;
            align-items: flex-start;
        }

        .orders-filter {
            width: 100%;
            overflow-x: auto;
            padding-bottom: 10px;
        }

        .order-header {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
        }

        .order-info {
            flex-direction: column;
            gap: 5px;
        }

        .order-footer {
            flex-direction: column;
            gap: 15px;
        }

        .order-total {
            text-align: center;
            width: 100%;
        }

        .order-actions {
            width: 100%;
            justify-content: center;
            flex-wrap: wrap;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="orders-container">
    <div class="orders-header">
        <div class="orders-title">
            <i class="fas fa-clipboard-list"></i>
            我的订单
        </div>
        <div class="orders-filter">
            <button type="button" class="filter-btn active" data-status="all">全部订单</button>
            <button type="button" class="filter-btn" data-status="pending">待付款</button>
            <button type="button" class="filter-btn" data-status="paid">已付款</button>
            <button type="button" class="filter-btn" data-status="shipped">已发货</button>
            <button type="button" class="filter-btn" data-status="completed">已完成</button>
            <button type="button" class="filter-btn" data-status="cancelled">已取消</button>
        </div>
    </div>
    
    {% if orders %}
    <div class="order-list">
        {% for order in orders %}
        <div class="order-item" data-status="{{ order.status }}">
            <div class="order-header">
                <div class="order-info">
                    <span class="order-sn">订单号：{{ order.order_number }}</span>
                    <span class="order-date">下单时间：{{ order.created_time|date:"Y-m-d H:i" }}</span>
                </div>
                <div class="order-status">
                    {% if order.status == 'pending' %}
                    <span class="status-badge status-pending">待付款</span>
                    {% elif order.status == 'paid' %}
                    <span class="status-badge status-paid">已付款</span>
                    {% elif order.status == 'shipped' %}
                    <span class="status-badge status-shipped">已发货</span>
                    {% elif order.status == 'completed' %}
                    <span class="status-badge status-completed">已完成</span>
                    {% elif order.status == 'cancelled' %}
                    <span class="status-badge status-cancelled">已取消</span>
                    {% endif %}
                </div>
            </div>
            
            <div class="order-products">
                {% for item in order.items.all %}
                <div class="product-item">
                    <div class="product-image">
                        <img src="{{ item.product.main_image.url }}" alt="{{ item.product.name }}" onerror="this.src='/media/products/default.jpg'">
                    </div>
                    <div class="product-info">
                        <div class="product-name" title="{{ item.product.name }}">{{ item.product.name }}</div>
                        <div class="product-price">¥{{ item.price }} × {{ item.quantity }}</div>
                    </div>
                </div>
                {% endfor %}
            </div>
            
            <div class="order-footer">
                <div class="order-total">
                    共 {{ order.items.count }} 件商品，实付款：<span class="total-price">¥{{ order.pay_amount }}</span>
                </div>
                <div class="order-actions">
                    <a href="{% url 'order:detail' order.order_number %}" class="order-btn btn-detail">
                        <i class="fas fa-eye"></i> 查看详情
                    </a>
                    {% if order.status == 'pending' %}
                    <a href="{% url 'order:pay' order.order_number %}" class="order-btn btn-pay">
                        <i class="fas fa-credit-card"></i> 去付款
                    </a>
                    <a href="{% url 'order:cancel' order.order_number %}" class="order-btn btn-cancel" onclick="return confirm('确认取消此订单？')">
                        <i class="fas fa-times"></i> 取消订单
                    </a>
                    {% elif order.status == 'paid' %}
                        {% if order.can_review %}
                        <a href="{% url 'reviews:add_review' %}?order_id={{ order.id }}" class="order-btn btn-review">
                            <i class="fas fa-star"></i> 去评价
                        </a>
                        {% else %}
                        <span class="order-btn btn-review" style="opacity: 0.6; cursor: not-allowed;">
                            <i class="fas fa-check"></i> 已评价
                        </span>
                        {% endif %}
                    {% elif order.status == 'shipped' %}
                    <a href="javascript:confirmReceive('{{ order.order_number }}')" class="order-btn btn-confirm">
                        <i class="fas fa-check"></i> 确认收货
                    </a>
                    {% elif order.status == 'received' %}
                        {% if order.can_review %}
                        <a href="{% url 'reviews:add_review' %}?order_id={{ order.id }}" class="order-btn btn-review">
                            <i class="fas fa-star"></i> 去评价
                        </a>
                        {% else %}
                        <span class="order-btn btn-review" style="opacity: 0.6; cursor: not-allowed;">
                            <i class="fas fa-check"></i> 已评价
                        </span>
                        {% endif %}
                    {% elif order.status == 'completed' %}
                        {% if order.can_review %}
                        <a href="{% url 'reviews:add_review' %}?order_id={{ order.id }}" class="order-btn btn-review">
                            <i class="fas fa-star"></i> 去评价
                        </a>
                        {% else %}
                        <span class="order-btn btn-review" style="opacity: 0.6; cursor: not-allowed;">
                            <i class="fas fa-check"></i> 已评价
                        </span>
                        {% endif %}
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="empty-order">
        <i class="fas fa-clipboard-list"></i>
        <p>您还没有订单，快去购物吧~</p>
        <a href="{% url 'goods:list' %}" class="go-shopping">
            <i class="fas fa-shopping-bag"></i> 去购物
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 订单筛选功能
        const filterButtons = document.querySelectorAll('.filter-btn');
        const orderItems = document.querySelectorAll('.order-item');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                // 更新按钮状态
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                const status = this.dataset.status;
                
                // 筛选订单
                orderItems.forEach(item => {
                    if (status === 'all' || item.dataset.status === status) {
                        item.style.display = 'block';
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // 检查是否有显示的订单
                const visibleOrders = document.querySelectorAll('.order-item[style="display: block"]');
                const emptyOrder = document.querySelector('.empty-order');
                
                if (visibleOrders.length === 0 && orderItems.length > 0) {
                    // 创建临时的空订单提示
                    if (!document.querySelector('.temp-empty-notice')) {
                        const notice = document.createElement('div');
                        notice.className = 'empty-order temp-empty-notice';
                        notice.innerHTML = `
                            <i class="fas fa-search"></i>
                            <p>没有${getStatusText(status)}的订单</p>
                        `;
                        document.querySelector('.order-list').appendChild(notice);
                    }
                } else {
                    // 移除临时的空订单提示
                    const tempNotice = document.querySelector('.temp-empty-notice');
                    if (tempNotice) {
                        tempNotice.remove();
                    }
                }
            });
        });
        
        // 获取状态文本
        function getStatusText(status) {
            switch(status) {
                case 'pending': return '待付款';
                case 'paid': return '已付款';
                case 'shipped': return '已发货';
                case 'completed': return '已完成';
                case 'cancelled': return '已取消';
                default: return '';
            }
        }
    });
    
    // 确认收货
    function confirmReceive(orderNumber) {
        if (confirm('确认已收到商品？')) {
            fetch(`/api/orders/${orderNumber}/receive/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 显示确认成功消息
                    alert('确认收货成功！即将跳转到商品评价页面');
                    // 跳转到评价页面
                    if (data.review_url) {
                        window.location.href = data.review_url;
                    } else {
                        location.reload();
                    }
                } else {
                    alert(data.message || '操作失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败，请重试');
            });
        }
    }

    // 获取CSRF Token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %} 