/* 清除默认样式的代码 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 去掉列表的项目符号 */
ul,
ol {
    list-style: none;
}

/* 去掉超链接的下划线 */
a {
    text-decoration: none;
    color: #666666;
}

/* 去掉input的轮廓线 */
input {
    outline: none;
    border: none;
}

/* 去掉button的边框和轮廓线 */
button {
    border: none;
    outline: none;
    cursor: pointer;
}

/* 去掉图片的边框 */
img {
    border: none;
    vertical-align: middle;
}

/* 清除浮动 */
.clearfix::after {
    content: '';
    display: block;
    clear: both;
}

/* 左浮动 */
.leftfix {
    float: left;
}

/* 右浮动 */
.rightfix {
    float: right;
}

/* 基础设置 */
body,h1,h2,h3,h4,h5,h6,hr,p,blockquote,dl,dt,dd,ul,ol,li,pre,form,fieldset,legend,button,input,textarea,th,td{
    margin: 0;
    padding: 0;
}

b,strong {
    font-weight: 400;
}

h1,h2,h3,h4,h5,h6 {
    /* 父元素字号的百分比 */
    font-size: 100%;
    font-weight: normal;
}

i,em {
    /* 不倾斜 */
    font-style: normal;
}

u,ins,s,del {
    /* 去掉中划线和下划线 */
    text-decoration: none;
}

table {
    border: 1px solid #999;
    /* 相当于是cellspacing */
    border-spacing: 0;
    /* 1px边框 */
    border-collapse: collapse;
}

td,th {
    border: 1px solid #999;
}

a:hover {
    color:#DD302D;
    text-decoration: none;
}

/* 风格设置 */
body {
    font: 12px/1.5 "Microsoft YaHei", Tahoma, Helvetica, Arial, "\5b8b\4f53", sans-serif;
    color: #333;
}
