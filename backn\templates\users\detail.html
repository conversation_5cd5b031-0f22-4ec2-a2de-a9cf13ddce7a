{% extends 'base.html' %}

{% block title %}{{ user.username }} - 用户详情 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}用户详情{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <a href="{% url 'users:list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
    {% if not user.is_superuser %}
    <button type="button" class="btn btn-outline-warning toggle-status" 
            data-user-id="{{ user.id }}" 
            data-current-status="{{ user.is_active }}">
        {% if user.is_active %}
            <i class="fas fa-ban me-1"></i>禁用用户
        {% else %}
            <i class="fas fa-check me-1"></i>激活用户
        {% endif %}
    </button>
    {% endif %}
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 用户基本信息 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>用户信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        {% if user.avatar %}
                            <img src="{{ user.avatar.url }}" alt="{{ user.username }}" 
                                 class="img-fluid rounded-circle shadow-sm mb-3" 
                                 style="width: 150px; height: 150px; object-fit: cover;">
                        {% else %}
                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" 
                                 style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x text-white"></i>
                            </div>
                        {% endif %}
                        
                        <div class="mb-2">
                            {% if user.is_active %}
                                <span class="badge bg-success">活跃用户</span>
                            {% else %}
                                <span class="badge bg-danger">已禁用</span>
                            {% endif %}
                        </div>
                        
                        {% if user.is_vip %}
                            <div class="mb-2">
                                <span class="badge bg-warning">VIP用户</span>
                            </div>
                        {% endif %}
                        
                        {% if user.is_superuser %}
                            <div class="mb-2">
                                <span class="badge bg-danger">超级管理员</span>
                            </div>
                        {% elif user.is_staff %}
                            <div class="mb-2">
                                <span class="badge bg-warning">管理员</span>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-9">
                        <h3 class="mb-3">{{ user.username }}</h3>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>用户ID：</strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.id }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>邮箱地址：</strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.email|default:"未设置" }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>手机号码：</strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.phone|default:"未设置" }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>账户余额：</strong>
                            </div>
                            <div class="col-sm-9">
                                <span class="h5 text-success">¥{{ user.balance|floatformat:2 }}</span>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>注册时间：</strong>
                            </div>
                            <div class="col-sm-9">
                                {{ user.date_joined|date:"Y年m月d日 H:i" }}
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-sm-3">
                                <strong>最后登录：</strong>
                            </div>
                            <div class="col-sm-9">
                                {% if user.last_login %}
                                    {{ user.last_login|date:"Y年m月d日 H:i" }}
                                {% else %}
                                    <span class="text-muted">从未登录</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 用户订单 -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-bag me-2"></i>最近订单
                </h5>
                <a href="{% url 'orders:list' %}?search={{ user.username }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                {% if orders %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>金额</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in orders %}
                            <tr>
                                <td>{{ order.order_no }}</td>
                                <td>¥{{ order.final_amount }}</td>
                                <td>
                                    <span class="badge bg-{{ order.get_status_display_color }}">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ order.created_time|date:"m-d H:i" }}</td>
                                <td>
                                    <a href="{% url 'orders:detail' order.id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        查看
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-shopping-bag fa-3x mb-3"></i>
                    <p>该用户暂无订单</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- 侧边栏统计 -->
    <div class="col-md-4">
        <!-- 用户统计 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>用户统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-6">
                        <div class="border-end">
                            <h5 class="text-primary mb-1">{{ total_orders }}</h5>
                            <small class="text-muted">总订单数</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h5 class="text-success mb-1">{{ paid_orders }}</h5>
                        <small class="text-muted">已付款</small>
                    </div>
                </div>
                
                <div class="text-center">
                    <h5 class="text-warning mb-1">¥{{ total_spent|floatformat:2 }}</h5>
                    <small class="text-muted">累计消费</small>
                </div>
                
                <hr>
                
                <div class="row text-center">
                    <div class="col-12">
                        <h6 class="text-info mb-1">¥{{ user.balance|floatformat:2 }}</h6>
                        <small class="text-muted">账户余额</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if not user.is_superuser %}
                    <button type="button" class="btn btn-outline-warning toggle-status" 
                            data-user-id="{{ user.id }}" 
                            data-current-status="{{ user.is_active }}">
                        <i class="fas fa-{% if user.is_active %}ban{% else %}check{% endif %} me-2"></i>
                        {% if user.is_active %}禁用用户{% else %}激活用户{% endif %}
                    </button>
                    {% endif %}
                    
                    <button type="button" class="btn btn-outline-info" id="toggleVip">
                        <i class="fas fa-crown me-2"></i>
                        {% if user.is_vip %}取消VIP{% else %}设为VIP{% endif %}
                    </button>
                    
                    <button type="button" class="btn btn-outline-success" id="adjustBalance">
                        <i class="fas fa-wallet me-2"></i>调整余额
                    </button>
                    
                    <a href="{% url 'orders:list' %}?search={{ user.username }}" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-shopping-bag me-2"></i>查看订单
                    </a>
                </div>
            </div>
        </div>
        
        <!-- 用户活动 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>活动记录
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">用户注册</h6>
                            <p class="timeline-text">{{ user.date_joined|date:"Y年m月d日 H:i" }}</p>
                        </div>
                    </div>
                    
                    {% if user.last_login %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">最后登录</h6>
                            <p class="timeline-text">{{ user.last_login|date:"Y年m月d日 H:i" }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if total_orders > 0 %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">首次下单</h6>
                            <p class="timeline-text">已下单 {{ total_orders }} 次</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 0;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 切换用户状态
    $('.toggle-status').click(function() {
        const userId = $(this).data('user-id');
        const currentStatus = $(this).data('current-status');
        
        if (confirm(currentStatus ? '确定要禁用此用户吗？' : '确定要激活此用户吗？')) {
            $.post('{% url "users:toggle_status" 0 %}'.replace('0', userId), {
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert('操作失败，请重试');
                }
            })
            .fail(function() {
                alert('操作失败，请重试');
            });
        }
    });
    
    // 其他功能待实现
    $('#toggleVip, #adjustBalance').click(function() {
        alert('功能待实现');
    });
});
</script>
{% endblock %}
