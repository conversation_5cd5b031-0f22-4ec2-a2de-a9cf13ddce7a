from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.views.decorators.http import require_POST
from django.conf import settings
import os

from .models import Product, Category, ProductImage
from admin_panel.models import AdminLog
from common.utils import get_client_ip, is_admin

@login_required
@user_passes_test(is_admin)
def product_list(request):
    """商品列表"""
    search_query = request.GET.get('search', '')
    category_filter = request.GET.get('category', '')
    status_filter = request.GET.get('status', '')

    products = Product.objects.select_related('category').all()

    # 搜索过滤
    if search_query:
        products = products.filter(name__icontains=search_query)

    # 分类过滤
    if category_filter:
        products = products.filter(category_id=category_filter)

    # 状态过滤
    if status_filter == 'active':
        products = products.filter(is_active=True)
    elif status_filter == 'inactive':
        products = products.filter(is_active=False)
    elif status_filter == 'hot':
        products = products.filter(is_hot=True)
    elif status_filter == 'new':
        products = products.filter(is_new=True)

    products = products.order_by('-created_time')

    # 分页
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # 获取分类列表
    categories = Category.objects.filter(is_active=True).order_by('sort_order')

    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search_query': search_query,
        'category_filter': category_filter,
        'status_filter': status_filter,
    }

    return render(request, 'goods/list.html', context)

@login_required
@user_passes_test(is_admin)
def product_detail(request, product_id):
    """商品详情"""
    product = get_object_or_404(Product, id=product_id)
    images = ProductImage.objects.filter(product=product).order_by('sort_order')

    context = {
        'product': product,
        'images': images,
    }

    return render(request, 'goods/detail.html', context)

@login_required
@user_passes_test(is_admin)
def product_add(request):
    """添加商品"""
    if request.method == 'POST':
        try:
            # 获取表单数据
            name = request.POST.get('name')
            description = request.POST.get('description')
            price = request.POST.get('price')
            stock = request.POST.get('stock', 0)
            category_id = request.POST.get('category')
            is_hot = request.POST.get('is_hot') == 'on'
            is_new = request.POST.get('is_new') == 'on'
            is_active = request.POST.get('is_active') == 'on'

            # 验证必填字段
            if not all([name, description, price, category_id]):
                messages.error(request, '请填写所有必填字段')
                return redirect('goods:add')

            # 创建商品
            category = get_object_or_404(Category, id=category_id)
            product = Product.objects.create(
                name=name,
                description=description,
                price=float(price),
                stock=int(stock),
                category=category,
                is_hot=is_hot,
                is_new=is_new,
                is_active=is_active
            )

            # 处理主图
            if 'main_image' in request.FILES:
                product.main_image = request.FILES['main_image']
                product.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=request.user,
                action='添加商品',
                content_type='Product',
                object_id=str(product.id),
                object_repr=str(product),
                change_message=f'添加商品: {product.name}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, f'商品 {product.name} 添加成功')
            return redirect('goods:detail', product_id=product.id)

        except Exception as e:
            messages.error(request, f'添加失败：{str(e)}')

    # 获取分类列表
    categories = Category.objects.filter(is_active=True).order_by('sort_order')

    return render(request, 'goods/add.html', {'categories': categories})

@login_required
@user_passes_test(is_admin)
def product_edit(request, product_id):
    """编辑商品"""
    product = get_object_or_404(Product, id=product_id)

    if request.method == 'POST':
        try:
            # 获取表单数据
            product.name = request.POST.get('name')
            product.description = request.POST.get('description')
            product.price = float(request.POST.get('price'))
            product.stock = int(request.POST.get('stock', 0))
            product.category_id = request.POST.get('category')
            product.is_hot = request.POST.get('is_hot') == 'on'
            product.is_new = request.POST.get('is_new') == 'on'
            product.is_active = request.POST.get('is_active') == 'on'

            # 处理主图
            if 'main_image' in request.FILES:
                product.main_image = request.FILES['main_image']

            product.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=request.user,
                action='编辑商品',
                content_type='Product',
                object_id=str(product.id),
                object_repr=str(product),
                change_message=f'编辑商品: {product.name}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, f'商品 {product.name} 更新成功')
            return redirect('goods:detail', product_id=product.id)

        except Exception as e:
            messages.error(request, f'更新失败：{str(e)}')

    # 获取分类列表
    categories = Category.objects.filter(is_active=True).order_by('sort_order')

    context = {
        'product': product,
        'categories': categories,
    }

    return render(request, 'goods/edit.html', context)

@login_required
@user_passes_test(is_admin)
def product_delete(request, product_id):
    """删除商品"""
    if request.method == 'POST':
        product = get_object_or_404(Product, id=product_id)
        product_name = product.name

        # 记录操作日志
        AdminLog.objects.create(
            admin=request.user,
            action='删除商品',
            content_type='Product',
            object_id=str(product.id),
            object_repr=str(product),
            change_message=f'删除商品: {product_name}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        product.delete()
        messages.success(request, f'商品 {product_name} 删除成功')

        return JsonResponse({'success': True})

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def toggle_product_status(request, product_id):
    """切换商品状态"""
    if request.method == 'POST':
        product = get_object_or_404(Product, id=product_id)
        product.is_active = not product.is_active
        product.save()

        # 记录操作日志
        action = '上架商品' if product.is_active else '下架商品'
        AdminLog.objects.create(
            admin=request.user,
            action=action,
            content_type='Product',
            object_id=str(product.id),
            object_repr=str(product),
            change_message=f'{action}: {product.name}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        status = '上架' if product.is_active else '下架'
        messages.success(request, f'商品 {product.name} 已{status}')

        return JsonResponse({'success': True, 'status': product.is_active})

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def batch_action(request):
    """批量操作"""
    if request.method == 'POST':
        action = request.POST.get('action')
        product_ids = request.POST.getlist('product_ids')

        if not product_ids:
            return JsonResponse({'success': False, 'message': '请选择要操作的商品'})

        products = Product.objects.filter(id__in=product_ids)

        if action == 'activate':
            products.update(is_active=True)
            message = f'已批量上架 {len(product_ids)} 个商品'
        elif action == 'deactivate':
            products.update(is_active=False)
            message = f'已批量下架 {len(product_ids)} 个商品'
        elif action == 'set_hot':
            products.update(is_hot=True)
            message = f'已批量设置 {len(product_ids)} 个商品为热门'
        elif action == 'unset_hot':
            products.update(is_hot=False)
            message = f'已批量取消 {len(product_ids)} 个商品的热门标记'
        else:
            return JsonResponse({'success': False, 'message': '无效的操作'})

        # 记录操作日志
        AdminLog.objects.create(
            admin=request.user,
            action=f'批量操作商品',
            change_message=f'{action}: {len(product_ids)} 个商品',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({'success': True, 'message': message})

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def category_list(request):
    """分类列表"""
    search_query = request.GET.get('search', '')
    parent_filter = request.GET.get('parent', '')
    status_filter = request.GET.get('status', '')
    sort_by = request.GET.get('sort', 'sort_order')

    # 获取所有父分类及其子分类
    parent_categories = Category.objects.filter(parent=None).prefetch_related('children').order_by('sort_order')

    # 搜索过滤
    if search_query:
        parent_categories = parent_categories.filter(
            Q(name__icontains=search_query) |
            Q(children__name__icontains=search_query)
        ).distinct()

    # 状态过滤
    if status_filter == 'active':
        parent_categories = parent_categories.filter(is_active=True)
    elif status_filter == 'inactive':
        parent_categories = parent_categories.filter(is_active=False)

    # 排序
    valid_sorts = [
        'name', '-name',                    # 按名称排序
        'created_time', '-created_time',    # 按创建时间排序
        'sort_order', '-sort_order',        # 按排序字段排序
    ]

    if sort_by in valid_sorts:
        parent_categories = parent_categories.order_by(sort_by)
    else:
        parent_categories = parent_categories.order_by('sort_order')

    # 统计信息
    total_categories = Category.objects.count()
    total_parent_categories = parent_categories.count()

    context = {
        'parent_categories': parent_categories,
        'search_query': search_query,
        'parent_filter': parent_filter,
        'status_filter': status_filter,
        'sort_by': sort_by,
        'total_categories': total_categories,
        'total_parent_categories': total_parent_categories,
    }

    return render(request, 'goods/category_list_new.html', context)

@login_required
@user_passes_test(is_admin)
def category_add(request):
    """添加分类"""
    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            parent_id = request.POST.get('parent')
            sort_order = request.POST.get('sort_order', 0)
            is_active = request.POST.get('is_active') == 'on'

            if not name:
                messages.error(request, '分类名称不能为空')
                return redirect('goods:category_add')

            # 创建分类
            category = Category.objects.create(
                name=name,
                parent_id=parent_id if parent_id else None,
                sort_order=int(sort_order) if sort_order else 0,
                is_active=is_active
            )

            # 处理图标上传
            if 'icon' in request.FILES:
                category.icon = request.FILES['icon']
                category.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=request.user,
                action='添加分类',
                content_type='Category',
                object_id=str(category.id),
                object_repr=str(category),
                change_message=f'添加分类: {category.name}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, f'分类 "{category.name}" 添加成功')
            return redirect('goods:category_list')

        except Exception as e:
            messages.error(request, f'添加失败：{str(e)}')

    # 获取父分类列表
    parent_categories = Category.objects.filter(is_active=True).order_by('sort_order')

    context = {
        'parent_categories': parent_categories,
    }

    return render(request, 'goods/category_add.html', context)

@login_required
@user_passes_test(is_admin)
def category_edit(request, category_id):
    """编辑分类"""
    category = get_object_or_404(Category, id=category_id)

    if request.method == 'POST':
        try:
            name = request.POST.get('name')
            parent_id = request.POST.get('parent')
            sort_order = request.POST.get('sort_order', 0)
            is_active = request.POST.get('is_active') == 'on'

            if not name:
                messages.error(request, '分类名称不能为空')
                return redirect('goods:category_edit', category_id=category_id)

            # 检查是否设置自己为父分类
            if parent_id and int(parent_id) == category.id:
                messages.error(request, '不能将自己设置为父分类')
                return redirect('goods:category_edit', category_id=category_id)

            # 更新分类信息
            category.name = name
            category.parent_id = parent_id if parent_id else None
            category.sort_order = int(sort_order) if sort_order else 0
            category.is_active = is_active

            # 处理图标上传
            if 'icon' in request.FILES:
                category.icon = request.FILES['icon']

            category.save()

            # 记录操作日志
            AdminLog.objects.create(
                admin=request.user,
                action='编辑分类',
                content_type='Category',
                object_id=str(category.id),
                object_repr=str(category),
                change_message=f'编辑分类: {category.name}',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            messages.success(request, f'分类 "{category.name}" 更新成功')
            return redirect('goods:category_list')

        except Exception as e:
            messages.error(request, f'更新失败：{str(e)}')

    # 获取父分类列表（排除自己和子分类）
    parent_categories = Category.objects.filter(is_active=True).exclude(id=category.id).order_by('sort_order')

    context = {
        'category': category,
        'parent_categories': parent_categories,
    }

    return render(request, 'goods/category_edit.html', context)

@login_required
@user_passes_test(is_admin)
def category_delete(request, category_id):
    """删除分类"""
    print(f"🗑️ 收到删除请求: category_id={category_id}, method={request.method}")
    print(f"👤 用户: {request.user}, 是否管理员: {is_admin(request.user)}")

    try:
        if request.method == 'POST':
            print(f"📝 POST数据: {request.POST}")
            category = get_object_or_404(Category, id=category_id)
            category_name = category.name
            print(f"📂 找到分类: {category_name}")

            # 简单删除测试
            category.delete()
            print(f"✅ 分类已删除: {category_name}")

            # 记录操作日志
            try:
                AdminLog.objects.create(
                    admin=request.user,
                    action='删除分类',
                    content_type='Category',
                    object_id=str(category_id),
                    object_repr=category_name,
                    change_message=f'删除分类: {category_name}',
                    ip_address=get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', '')
                )
                print("📝 操作日志已记录")
            except Exception as log_error:
                print(f"❌ 日志记录失败: {log_error}")

            response_data = {
                'success': True,
                'message': f'分类 "{category_name}" 删除成功'
            }
            print(f"📤 返回响应: {response_data}")
            return JsonResponse(response_data)

        print("❌ 非POST请求")
        return JsonResponse({'success': False, 'message': '请求方法错误'})

    except Exception as e:
        import traceback
        print(f"❌ 删除异常: {e}")
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'message': f'删除失败：{str(e)}'
        })

@login_required
@user_passes_test(is_admin)
def toggle_category_status(request, category_id):
    """切换分类状态"""
    if request.method == 'POST':
        category = get_object_or_404(Category, id=category_id)
        category.is_active = not category.is_active
        category.save()

        # 记录操作日志
        action = '启用分类' if category.is_active else '禁用分类'
        AdminLog.objects.create(
            admin=request.user,
            action=action,
            content_type='Category',
            object_id=str(category.id),
            object_repr=str(category),
            change_message=f'{action}: {category.name}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        status = '启用' if category.is_active else '禁用'
        return JsonResponse({
            'success': True,
            'status': category.is_active,
            'message': f'分类 "{category.name}" 已{status}'
        })

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def category_batch_delete(request):
    """批量删除分类"""
    if request.method == 'POST':
        category_ids = request.POST.getlist('category_ids')

        if not category_ids:
            return JsonResponse({
                'success': False,
                'message': '请选择要删除的分类'
            })

        try:
            # 获取要删除的分类
            categories = Category.objects.filter(id__in=category_ids)

            if not categories.exists():
                return JsonResponse({
                    'success': False,
                    'message': '未找到要删除的分类'
                })

            # 计算影响范围
            total_categories = 0
            total_products = 0

            def count_category_impact(cat):
                nonlocal total_categories, total_products
                total_categories += 1
                total_products += cat.products.count()
                for child in cat.children.all():
                    count_category_impact(child)

            for category in categories:
                count_category_impact(category)

            # 级联删除函数
            def delete_category_and_products(cat):
                # 删除该分类下的所有商品
                products = cat.products.all()
                for product in products:
                    # 删除商品的所有图片
                    product.images.all().delete()
                    # 删除商品
                    product.delete()

                # 递归删除子分类
                for child in cat.children.all():
                    delete_category_and_products(child)

                # 删除分类本身
                cat.delete()

            # 执行批量删除
            deleted_names = []
            for category in categories:
                deleted_names.append(category.name)
                delete_category_and_products(category)

            # 记录操作日志
            AdminLog.objects.create(
                admin=request.user,
                action='批量删除分类',
                content_type='Category',
                object_id=','.join(category_ids),
                object_repr=', '.join(deleted_names),
                change_message=f'批量删除分类: {", ".join(deleted_names)} (共删除 {total_categories} 个分类, {total_products} 个商品)',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )

            return JsonResponse({
                'success': True,
                'message': f'成功删除 {len(deleted_names)} 个分类及其相关数据 (共删除 {total_categories} 个分类, {total_products} 个商品)'
            })

        except Exception as e:
            return JsonResponse({
                'success': False,
                'message': f'批量删除失败：{str(e)}'
            })

    return JsonResponse({'success': False, 'message': '请求方法错误'})

@login_required
@user_passes_test(is_admin)
def category_batch_action(request):
    """分类批量操作"""
    if request.method == 'POST':
        action = request.POST.get('action')
        category_ids = request.POST.getlist('category_ids')

        if not category_ids:
            return JsonResponse({'success': False, 'message': '请选择要操作的分类'})

        categories = Category.objects.filter(id__in=category_ids)

        if action == 'activate':
            categories.update(is_active=True)
            message = f'已批量启用 {len(category_ids)} 个分类'
        elif action == 'deactivate':
            categories.update(is_active=False)
            message = f'已批量禁用 {len(category_ids)} 个分类'
        elif action == 'delete':
            # 批量删除
            deleted_names = []
            for category in categories:
                deleted_names.append(category.name)
                category.delete()
            message = f'已批量删除 {len(deleted_names)} 个分类'
        else:
            return JsonResponse({'success': False, 'message': '无效的操作'})

        # 记录操作日志
        AdminLog.objects.create(
            admin=request.user,
            action=f'批量操作分类',
            change_message=f'{action}: {len(category_ids)} 个分类',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({'success': True, 'message': message})

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def toggle_category_status(request, category_id):
    """切换分类状态"""
    if request.method == 'POST':
        category = get_object_or_404(Category, id=category_id)
        category.is_active = not category.is_active
        category.save()

        # 记录操作日志
        action = '启用分类' if category.is_active else '禁用分类'
        AdminLog.objects.create(
            admin=request.user,
            action=action,
            content_type='Category',
            object_id=str(category.id),
            object_repr=str(category),
            change_message=f'{action}: {category.name}',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        status = '启用' if category.is_active else '禁用'
        return JsonResponse({
            'success': True,
            'status': category.is_active,
            'message': f'分类 {category.name} 已{status}'
        })

    return JsonResponse({'success': False})

@login_required
@user_passes_test(is_admin)
def category_batch_action(request):
    """分类批量操作"""
    if request.method == 'POST':
        action = request.POST.get('action')
        category_ids = request.POST.getlist('category_ids')

        if not category_ids:
            return JsonResponse({'success': False, 'message': '请选择要操作的分类'})

        categories = Category.objects.filter(id__in=category_ids)

        if action == 'activate':
            categories.update(is_active=True)
            message = f'已批量启用 {len(category_ids)} 个分类'
        elif action == 'deactivate':
            categories.update(is_active=False)
            message = f'已批量禁用 {len(category_ids)} 个分类'
        elif action == 'delete':
            categories.delete()
            message = f'已批量删除 {len(category_ids)} 个分类'
        else:
            return JsonResponse({'success': False, 'message': '无效的操作'})

        # 记录操作日志
        AdminLog.objects.create(
            admin=request.user,
            action=f'批量操作分类',
            change_message=f'{action}: {len(category_ids)} 个分类',
            ip_address=get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        return JsonResponse({'success': True, 'message': message})

    return JsonResponse({'success': False})
