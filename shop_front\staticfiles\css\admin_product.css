/* 商品添加页面样式 */
.product-form-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.product-form-title {
    color: #333;
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
}

.product-form-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, #dc3545, #e74c3c);
    border-radius: 3px;
}

.form-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    overflow: hidden;
    border: none;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.form-card:hover {
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    transform: translateY(-5px);
}

.form-card .card-header {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    color: white;
    font-weight: 600;
    border-bottom: none;
    padding: 20px 25px;
}

.form-card .card-body {
    padding: 30px;
}

.form-label {
    font-weight: 600;
    color: #555;
    margin-bottom: 8px;
}

.form-control, .form-select {
    border-radius: 10px;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.15);
}

.input-group-text {
    border-radius: 10px 0 0 10px;
    background: #dc3545;
    color: white;
    border: none;
    padding: 0 15px;
}

.form-check-input:checked {
    background-color: #dc3545;
    border-color: #dc3545;
}

.form-check-input:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
    border: none;
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

.btn-outline-secondary {
    border-radius: 10px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-secondary:hover {
    transform: translateY(-2px);
}

/* 文件上传美化 */
.file-upload-container {
    position: relative;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.file-upload-container:hover {
    border-color: #dc3545;
    background-color: #fff5f7;
}

.file-upload-container label {
    cursor: pointer;
    width: 100%;
    display: block;
}

.file-upload-container .icon {
    font-size: 40px;
    color: #dc3545;
    margin-bottom: 10px;
}

.file-upload-container .text {
    color: #6c757d;
    font-size: 16px;
}

.file-upload-container input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.image-preview {
    max-width: 100%;
    height: 200px;
    display: none;
    margin-top: 15px;
    object-fit: contain;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

/* 面包屑导航美化 */
.breadcrumb {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 10px;
    padding: 12px 20px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.breadcrumb-item a {
    color: #dc3545;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.breadcrumb-item a:hover {
    color: #c82333;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 600;
}

/* 表单验证样式 */
.is-invalid {
    border-color: #dc3545 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.is-invalid:focus {
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25) !important;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

.form-floating .is-invalid ~ label {
    color: #dc3545;
}

/* 提交按钮加载状态 */
.btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn-loading:after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: calc(50% - 8px);
    left: calc(50% - 8px);
    border-radius: 50%;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-top-color: white;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 成功提交动画 */
.submit-success {
    animation: successPulse 1.5s;
}

@keyframes successPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* 响应式调整 */
@media (max-width: 768px) {
    .product-form-container {
        padding: 20px;
    }
    
    .form-card .card-body {
        padding: 20px;
    }
}

/* 自定义动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-card {
    animation: fadeIn 0.5s ease-out;
}

/* 扁平化提示信息 */
.alert {
    border: none;
    border-radius: 10px;
    padding: 15px 20px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.alert-success {
    background: linear-gradient(135deg, #28a745 0%, #218838 100%);
    color: white;
}

.alert-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.alert-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.alert-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

/* 字段帮助文本 */
.form-text {
    color: #6c757d;
    font-size: 0.875em;
    margin-top: 0.25rem;
} 