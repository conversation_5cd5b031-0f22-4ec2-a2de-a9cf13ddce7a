{% extends 'base.html' %}

{% block title %}系统设置 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}系统设置{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <form method="post" action="{% url 'admin_panel:update_settings' %}">
            {% csrf_token %}
            
            <!-- 基本设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>基本设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="site_name" class="form-label">网站名称</label>
                                <input type="text" class="form-control" id="site_name" name="site_name" 
                                       value="{{ settings.site_name|default:'MARS BUY商城' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">联系邮箱</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                       value="{{ settings.contact_email|default:'' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="contact_phone" class="form-label">联系电话</label>
                                <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                       value="{{ settings.contact_phone|default:'' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="site_description" class="form-label">网站描述</label>
                        <textarea class="form-control" id="site_description" name="site_description" rows="3">{{ settings.site_description|default:'' }}</textarea>
                    </div>
                </div>
            </div>
            
            <!-- 邮件设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-envelope me-2"></i>邮件设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_host" class="form-label">SMTP服务器</label>
                                <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                       value="{{ settings.smtp_host|default:'' }}" placeholder="smtp.example.com">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_port" class="form-label">SMTP端口</label>
                                <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                       value="{{ settings.smtp_port|default:25 }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_user" class="form-label">SMTP用户名</label>
                                <input type="text" class="form-control" id="smtp_user" name="smtp_user" 
                                       value="{{ settings.smtp_user|default:'' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtp_password" class="form-label">SMTP密码</label>
                                <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                       value="{{ settings.smtp_password|default:'' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="smtp_ssl" name="smtp_ssl" 
                               {% if settings.smtp_ssl %}checked{% endif %}>
                        <label class="form-check-label" for="smtp_ssl">
                            使用SSL加密
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 其他设置 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>其他设置
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="order_timeout" class="form-label">订单超时时间（分钟）</label>
                                <input type="number" class="form-control" id="order_timeout" name="order_timeout" 
                                       value="{{ settings.order_timeout|default:30 }}" min="1">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="maintenance_mode" name="maintenance_mode" 
                               {% if settings.maintenance_mode %}checked{% endif %}>
                        <label class="form-check-label" for="maintenance_mode">
                            维护模式
                        </label>
                    </div>
                    
                    <div class="mb-3">
                        <label for="maintenance_message" class="form-label">维护提示信息</label>
                        <textarea class="form-control" id="maintenance_message" name="maintenance_message" rows="3">{{ settings.maintenance_message|default:'' }}</textarea>
                    </div>
                </div>
            </div>
            
            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-1"></i>保存设置
                </button>
            </div>
        </form>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>设置说明
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>配置提示</h6>
                    <ul class="mb-0 small">
                        <li>网站名称会显示在页面标题中</li>
                        <li>联系信息用于客户服务</li>
                        <li>邮件设置用于发送系统通知</li>
                        <li>订单超时设置影响未付款订单</li>
                        <li>维护模式会暂停网站服务</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>注意事项</h6>
                    <ul class="mb-0 small">
                        <li>修改设置后立即生效</li>
                        <li>邮件设置错误可能影响通知发送</li>
                        <li>维护模式会影响用户访问</li>
                        <li>请谨慎修改重要配置</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>系统信息
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>Django版本：</strong></td>
                        <td>4.2.22</td>
                    </tr>
                    <tr>
                        <td><strong>Python版本：</strong></td>
                        <td>3.13.1</td>
                    </tr>
                    <tr>
                        <td><strong>数据库：</strong></td>
                        <td>MySQL</td>
                    </tr>
                    <tr>
                        <td><strong>运行模式：</strong></td>
                        <td>
                            {% if debug %}
                                <span class="badge bg-warning">开发模式</span>
                            {% else %}
                                <span class="badge bg-success">生产模式</span>
                            {% endif %}
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 维护模式切换提示
    $('#maintenance_mode').change(function() {
        if (this.checked) {
            if (!confirm('启用维护模式将暂停网站服务，确定要启用吗？')) {
                this.checked = false;
            }
        }
    });
    
    // 表单提交确认
    $('form').submit(function(e) {
        if ($('#maintenance_mode').is(':checked')) {
            if (!confirm('当前启用了维护模式，这将影响网站正常访问，确定要保存吗？')) {
                e.preventDefault();
                return false;
            }
        }
        return true;
    });
});
</script>
{% endblock %}
