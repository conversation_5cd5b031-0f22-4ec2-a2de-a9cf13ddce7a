<!DOCTYPE html>
<html>
<head>
    <title>调试测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>API调试测试</h1>
    
    <div class="test-section">
        <h3>测试1: 基本API连接</h3>
        <button onclick="testBasicConnection()">测试基本连接</button>
        <div id="result1" class="result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 密码验证API</h3>
        <button onclick="testPasswordAPI()">测试密码验证</button>
        <div id="result2" class="result info">点击按钮开始测试</div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 支付跳转</h3>
        <button onclick="testPaymentRedirect()">测试支付跳转</button>
        <div id="result3" class="result info">点击按钮开始测试</div>
    </div>

    {% csrf_token %}
    
    <script>
        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = message;
            element.className = `result ${type}`;
        }
        
        async function testBasicConnection() {
            showResult('result1', '正在测试基本连接...', 'info');
            
            try {
                const response = await fetch('/payment/verify-password/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        password: 'test',
                        order_number: 'TEST123456'
                    })
                });
                
                console.log('基本连接响应:', response);
                showResult('result1', `连接成功！状态码: ${response.status}`, 'success');
                
                const data = await response.json();
                console.log('基本连接数据:', data);
                
            } catch (error) {
                console.error('基本连接错误:', error);
                showResult('result1', `连接失败: ${error.message}`, 'error');
            }
        }
        
        async function testPasswordAPI() {
            showResult('result2', '正在测试密码验证...', 'info');
            
            try {
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
                console.log('CSRF Token:', csrfToken ? csrfToken.value : 'null');
                
                const response = await fetch('/payment/verify-password/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken ? csrfToken.value : ''
                    },
                    body: JSON.stringify({
                        password: '123456',
                        order_number: 'TEST123456'
                    })
                });
                
                console.log('密码验证响应:', response);
                const data = await response.json();
                console.log('密码验证数据:', data);
                
                if (data.success) {
                    showResult('result2', `密码验证成功！消息: ${data.msg}`, 'success');
                } else {
                    showResult('result2', `密码验证失败: ${data.msg}`, 'error');
                }
                
            } catch (error) {
                console.error('密码验证错误:', error);
                showResult('result2', `验证失败: ${error.message}`, 'error');
            }
        }
        
        async function testPaymentRedirect() {
            showResult('result3', '正在测试支付跳转...', 'info');
            
            try {
                // 先验证密码
                const response = await fetch('/payment/verify-password/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        password: '123456',
                        order_number: 'TEST123456'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult('result3', '密码验证成功，3秒后跳转到支付成功页面...', 'success');
                    
                    setTimeout(() => {
                        // 跳转到模拟支付成功页面
                        window.location.href = '/payment/simulate-success/TEST123456/';
                    }, 3000);
                } else {
                    showResult('result3', `密码验证失败，无法跳转: ${data.msg}`, 'error');
                }
                
            } catch (error) {
                console.error('支付跳转错误:', error);
                showResult('result3', `跳转失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时显示一些基本信息
        window.addEventListener('load', function() {
            console.log('页面加载完成');
            console.log('当前URL:', window.location.href);
            console.log('CSRF Token存在:', !!document.querySelector('[name=csrfmiddlewaretoken]'));
        });
    </script>
</body>
</html>
