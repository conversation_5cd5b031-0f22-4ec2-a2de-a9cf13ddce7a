"""
Django settings for shop_front project.

Generated by 'django-admin startproject' using Django 5.0.3.

For more information on this file, see
https://docs.djangoproject.com/en/5.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.0/ref/settings/
"""

from pathlib import Path
import os
import pymysql

# 配置PyMySQL作为MySQLdb的替代
pymysql.install_as_MySQLdb()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# 添加应用路径到Python路径
import sys
sys.path.insert(0, str(BASE_DIR))


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-new-key-2024-shop-payment-system-secure-key-12345'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = ['127.0.0.1', 'localhost', 'testserver']


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'home',
    'users',
    'goods',
    'account',
    'order',
    'payment',
    'reviews',
    'common',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'shop_front.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'shop_front.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME':'mydatabase',  # 数据库名
        'USER':'myuser',  # 用户名
        'PASSWORD':'mypassword',  # 密码
        'HOST': '***************',  # 数据库地址（默认localhost）
        'PORT': '3306',  # 端口（默认3306）
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",  # 启用严格模式
            'charset': 'utf8mb4',  # 字符集
        },
        'TIME_ZONE': 'Asia/Shanghai',  # 时区（需与Django设置一致）
    }
}



# 修改时区设置以匹配数据库时区
TIME_ZONE = 'Asia/Shanghai'
USE_TZ = True


# Password validation
# https://docs.djangoproject.com/en/5.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.0/topics/i18n/

LANGUAGE_CODE = 'zh-hans'

USE_I18N = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.0/howto/static-files/

STATIC_URL = 'static/'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]
STATIC_ROOT = BASE_DIR / 'staticfiles'

# 媒体文件配置 - 统一媒体目录
MEDIA_URL = '/media/'
# 指向shop根目录的统一媒体目录
MEDIA_ROOT = os.path.join(BASE_DIR.parent, 'media')

# 文件上传配置
FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
FILE_UPLOAD_PERMISSIONS = 0o644

# 允许的图片格式
ALLOWED_IMAGE_EXTENSIONS = [
    '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'
]

# 图片上传大小限制（字节）
MAX_IMAGE_SIZE = 5 * 1024 * 1024  # 5MB

# Default primary key field type
# https://docs.djangoproject.com/en/5.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# 自定义用户模型
AUTH_USER_MODEL = 'users.User'

# 支付宝配置
ALIPAY_APPID = '2021000122671431'  # 沙箱环境AppID
ALIPAY_GATEWAY = 'https://openapi.alipaydev.com/gateway.do'  # 沙箱网关
ALIPAY_RETURN_URL = 'http://127.0.0.1:8001/payment/alipay/return/'
ALIPAY_NOTIFY_URL = 'http://127.0.0.1:8001/payment/alipay/notify/'

# 支付宝密钥配置
APP_PRIVATE_KEY = """-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA2Z8QjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
-----END RSA PRIVATE KEY-----"""

ALIPAY_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2Z8QjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
jXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQjXwQ
QIDAQAB
-----END PUBLIC KEY-----"""

# Elasticsearch配置
ELASTICSEARCH_DSL = {
    'default': {
        'hosts': 'localhost:9200'
    }
}

# 消息设置
MESSAGE_STORAGE = 'django.contrib.messages.storage.session.SessionStorage'

# 登录URL配置
LOGIN_URL = 'users:login'
LOGIN_REDIRECT_URL = 'home:index'
LOGOUT_REDIRECT_URL = 'home:index'

# 商品模块配置
GOODS_PER_PAGE = 20  # 每页显示商品数量
GOODS_HOT_COUNT = 5  # 热门商品显示数量
GOODS_NEW_COUNT = 8  # 新品显示数量
GOODS_IMAGE_SIZES = {
    'thumbnail': (300, 300),
    'medium': (600, 600),
    'large': (800, 800),
}

# 订单模块配置
CART_SESSION_ID = 'cart'  # 购物车session ID
ORDER_NUMBER_PREFIX = 'SPH'  # 订单号前缀
ORDER_NUMBER_LENGTH = 16  # 订单号长度
ORDER_TIMEOUT_MINUTES = 30  # 订单支付超时时间（分钟）
ORDER_STATUS_CHOICES = (
    ('pending', '待支付'),
    ('paid', '已支付'),
    ('shipped', '已发货'),
    ('received', '已收货'),
    ('cancelled', '已取消'),
    ('refunded', '已退款'),
)

# 支付模块配置
ALIPAY_CONFIG = {
    'app_id': 'your-app-id',
    'app_private_key_path': os.path.join(BASE_DIR, 'payment/keys/app_private_key.pem'),
    'alipay_public_key_path': os.path.join(BASE_DIR, 'payment/keys/alipay_public_key.pem'),
    'sign_type': 'RSA2',
    'debug': True,  # 默认使用沙箱环境
    'verbose': True,  # 输出调试信息
    'timeout_express': '30m',  # 订单支付超时时间
}

# 评价模块配置
REVIEW_SCORE_CHOICES = (
    (1, '很差'),
    (2, '差'),
    (3, '一般'),
    (4, '好'),
    (5, '很好'),
)
REVIEW_PER_PAGE = 10  # 每页显示评价数量
REVIEW_CACHE_TTL = 3600  # 评价缓存时间（秒）
REVIEW_ALLOW_ANONYMOUS = False  # 是否允许匿名评价
REVIEW_MODERATION = True  # 是否需要审核
REVIEW_WORD_BLACKLIST = ['违禁词1', '违禁词2']  # 评价违禁词

# Redis配置（用于缓存和Celery）
REDIS_HOST = 'localhost'
REDIS_PORT = 6379
REDIS_DB = 0
REDIS_PASSWORD = ''  # Redis密码，如果有的话

# Celery配置（用于异步任务）
CELERY_BROKER_URL = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'
CELERY_RESULT_BACKEND = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'
CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
CELERY_TIMEZONE = TIME_ZONE

# 缓存配置
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}

# 会话配置
SESSION_ENGINE = 'django.contrib.sessions.backends.db'
SESSION_COOKIE_AGE = 1209600  # 两周
SESSION_COOKIE_NAME = 'sessionid'
SESSION_COOKIE_SECURE = False  # 开发环境设置为False
SESSION_EXPIRE_AT_BROWSER_CLOSE = False
SESSION_SAVE_EVERY_REQUEST = False

# 文件上传配置
FILE_UPLOAD_HANDLERS = [
    'django.core.files.uploadhandler.MemoryFileUploadHandler',
    'django.core.files.uploadhandler.TemporaryFileUploadHandler',
]
MAX_UPLOAD_SIZE = 5242880  # 5MB

# 图片处理配置
THUMBNAIL_PROCESSORS = (
    'easy_thumbnails.processors.colorspace',
    'easy_thumbnails.processors.autocrop',
    'easy_thumbnails.processors.scale_and_crop',
    'easy_thumbnails.processors.filters',
)

# REST framework配置
REST_FRAMEWORK = {
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 10,
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.BasicAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticatedOrReadOnly',
    ),
    'DEFAULT_FILTER_BACKENDS': (
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ),
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs/shop.log'),
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'shop': {
            'handlers': ['file', 'console'],
            'level': 'DEBUG',
            'propagate': True,
        },
    },
}

# 支付宝配置
ALIPAY_APPID = '2021000122671080'  # 沙箱应用ID
ALIPAY_RETURN_URL = 'http://127.0.0.1:8001/payment/alipay/return/'
ALIPAY_NOTIFY_URL = 'http://127.0.0.1:8001/payment/alipay/notify/'

# 支付宝密钥配置（沙箱环境测试密钥）
APP_PRIVATE_KEY = """-----BEGIN RSA PRIVATE KEY-----
MIIEpAIBAAKCAQEA1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdefg
hijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuv
wxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKL
MNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
-----END RSA PRIVATE KEY-----"""

ALIPAY_PUBLIC_KEY = """-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1234567890abcdefghijklmnopqrstuv
wxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKL
MNOPQRSTUVWXYZ1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890abcdef
ghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ
-----END PUBLIC KEY-----"""


