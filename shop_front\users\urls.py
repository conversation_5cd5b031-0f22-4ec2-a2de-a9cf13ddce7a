from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register('api/addresses', views.AddressViewSet, basename='address-api')

# 这行是关键 - 定义应用命名空间
app_name = 'users'

urlpatterns = [
    path('login/', views.user_login, name='login'),
    path('logout/', views.user_logout, name='logout'),
    path('register/', views.register, name='register'),
    path('center/', views.user_center, name='center'),
    path('profile/', views.profile, name='profile'),
    path('address/', views.user_address, name='address'),
    path('password/', views.change_password, name='password'),
    path('membership/', views.membership, name='membership'),
    path('orders/', views.user_orders, name='orders'),

    # 图片上传相关
    path('upload-avatar/', views.upload_avatar, name='upload_avatar'),
    path('upload-image/', views.upload_general_image, name='upload_image'),
    path('image-upload/', views.image_upload_page, name='image_upload'),
    
    # API 路由
    path('', include(router.urls)),
]
