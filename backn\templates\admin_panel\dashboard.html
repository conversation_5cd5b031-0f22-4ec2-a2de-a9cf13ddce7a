{% extends 'base.html' %}

{% block title %}管理首页 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}管理首页{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            今日销售额
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            ¥{{ today_sales|floatformat:2 }}
                        </div>
                        <div class="text-xs text-muted">
                            昨日: ¥{{ yesterday_sales|floatformat:2 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            今日订单
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ today_order_count }}
                        </div>
                        <div class="text-xs text-muted">
                            昨日: {{ yesterday_order_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            总用户数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_users }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stats-card h-100">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            商品总数
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表和数据 -->
<div class="row">
    <!-- 销售趋势图 -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">30天销售趋势</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="salesChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 热门商品 -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">热门商品</h6>
            </div>
            <div class="card-body">
                {% for product in hot_products %}
                <div class="d-flex align-items-center mb-3">
                    <div class="me-3">
                        {% if product.main_image %}
                            <img src="{{ product.main_image.url }}" alt="{{ product.name }}" 
                                 class="rounded" width="50" height="50" style="object-fit: cover;">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="width: 50px; height: 50px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1">
                        <div class="fw-bold">{{ product.name|truncatechars:20 }}</div>
                        <small class="text-muted">销量: {{ product.sales }}</small>
                    </div>
                    <div class="text-end">
                        <div class="fw-bold text-success">¥{{ product.price }}</div>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted text-center">暂无热门商品</p>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- 最新订单 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">最新订单</h6>
                <a href="{% url 'orders:list' %}" class="btn btn-primary btn-sm">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>订单号</th>
                                <th>用户</th>
                                <th>状态</th>
                                <th>金额</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in recent_orders %}
                            <tr>
                                <td>{{ order.order_no }}</td>
                                <td>{{ order.user.username }}</td>
                                <td>
                                    <span class="badge bg-{{ order.get_status_display_color }}">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                                <td>¥{{ order.final_amount }}</td>
                                <td>{{ order.created_time|date:"Y-m-d H:i" }}</td>
                                <td>
                                    <a href="{% url 'orders:detail' order.id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        查看
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="6" class="text-center text-muted">暂无订单</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'goods:add' %}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-2"></i>
                            添加商品
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'goods:category_add' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-tags me-2"></i>
                            添加分类
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'users:list' %}" class="btn btn-outline-success w-100">
                            <i class="fas fa-users me-2"></i>
                            用户管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'orders:list' %}" class="btn btn-outline-info w-100">
                            <i class="fas fa-shopping-bag me-2"></i>
                            订单管理
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{% url 'admin_panel:settings' %}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-cog me-2"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 销售趋势图
const salesData = {{ sales_data|safe }};
const ctx = document.getElementById('salesChart').getContext('2d');
const salesChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: salesData.map(item => item.date),
        datasets: [{
            label: '销售额',
            data: salesData.map(item => item.sales),
            borderColor: '#dc3545',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '¥' + value.toFixed(2);
                    }
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});
</script>
{% endblock %}
