<!DOCTYPE html>
<html>
<head>
    <title>简化支付测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 10px; }
        .password-display { display: flex; gap: 10px; margin: 20px 0; justify-content: center; }
        .password-dot { 
            width: 20px; height: 20px; border: 2px solid #ddd; 
            border-radius: 50%; background: #f8f9fa; 
        }
        .password-dot.filled { background: #28a745; border-color: #28a745; }
        .keyboard { display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; max-width: 300px; margin: 0 auto; }
        .num-btn { 
            padding: 15px; border: 1px solid #ddd; background: white; 
            cursor: pointer; font-size: 18px; border-radius: 5px;
        }
        .num-btn:hover { background: #f8f9fa; }
        .btn { padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .result { margin: 20px 0; padding: 15px; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>简化支付测试页面</h1>
    
    <!-- 支付方式选择 -->
    <div class="section">
        <h3>1. 选择支付方式</h3>
        <label><input type="radio" name="payment" value="alipay"> 支付宝支付</label><br>
        <label><input type="radio" name="payment" value="wechat"> 微信支付</label><br>
        <label><input type="radio" name="payment" value="bank"> 银行卡支付</label>
    </div>
    
    <!-- 密码输入 -->
    <div class="section">
        <h3>2. 输入支付密码</h3>
        <div class="password-display">
            <span class="password-dot" data-index="0"></span>
            <span class="password-dot" data-index="1"></span>
            <span class="password-dot" data-index="2"></span>
            <span class="password-dot" data-index="3"></span>
            <span class="password-dot" data-index="4"></span>
            <span class="password-dot" data-index="5"></span>
        </div>
        
        <div class="keyboard">
            <button class="num-btn" data-num="1">1</button>
            <button class="num-btn" data-num="2">2</button>
            <button class="num-btn" data-num="3">3</button>
            <button class="num-btn" data-num="4">4</button>
            <button class="num-btn" data-num="5">5</button>
            <button class="num-btn" data-num="6">6</button>
            <button class="num-btn" data-num="7">7</button>
            <button class="num-btn" data-num="8">8</button>
            <button class="num-btn" data-num="9">9</button>
            <button class="num-btn" onclick="clearPassword()">清除</button>
            <button class="num-btn" data-num="0">0</button>
            <button class="num-btn" onclick="deleteLastDigit()">删除</button>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn btn-primary" onclick="testPassword()">验证密码</button>
            <button class="btn btn-success" onclick="processPayment()" id="payBtn" disabled>确认支付</button>
        </div>
    </div>
    
    <!-- 结果显示 -->
    <div class="section">
        <h3>3. 测试结果</h3>
        <div class="result info" id="result">
            请选择支付方式并输入6位密码<br>
            默认密码：123456, 888888, 666666, 000000
        </div>
    </div>

    {% csrf_token %}
    
    <script>
        let currentPassword = '';
        let selectedPayment = '';
        const passwordDots = document.querySelectorAll('.password-dot');
        const numBtns = document.querySelectorAll('.num-btn[data-num]');
        const paymentRadios = document.querySelectorAll('input[name="payment"]');
        
        // 支付方式选择
        paymentRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                selectedPayment = this.value;
                console.log('选择支付方式:', selectedPayment);
                updatePayButton();
            });
        });
        
        // 数字按钮事件
        numBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const num = this.dataset.num;
                if (currentPassword.length < 6) {
                    currentPassword += num;
                    updatePasswordDisplay();
                    updatePayButton();
                }
            });
        });
        
        function updatePasswordDisplay() {
            passwordDots.forEach((dot, index) => {
                if (index < currentPassword.length) {
                    dot.classList.add('filled');
                } else {
                    dot.classList.remove('filled');
                }
            });
        }
        
        function updatePayButton() {
            const payBtn = document.getElementById('payBtn');
            payBtn.disabled = !(selectedPayment && currentPassword.length === 6);
        }
        
        function clearPassword() {
            currentPassword = '';
            updatePasswordDisplay();
            updatePayButton();
            document.getElementById('result').innerHTML = '密码已清除';
            document.getElementById('result').className = 'result info';
        }
        
        function deleteLastDigit() {
            if (currentPassword.length > 0) {
                currentPassword = currentPassword.slice(0, -1);
                updatePasswordDisplay();
                updatePayButton();
            }
        }
        
        async function testPassword() {
            if (currentPassword.length !== 6) {
                showResult('请输入6位密码', 'error');
                return;
            }
            
            showResult('正在验证密码...', 'info');
            
            try {
                const response = await fetch('/payment/verify-password/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        password: currentPassword,
                        order_number: '{{ order.order_number|default:"TEST123456" }}'
                    })
                });
                
                console.log('验证响应状态:', response.status);
                const data = await response.json();
                console.log('验证响应数据:', data);
                
                if (data.success) {
                    showResult(`✓ 密码验证成功！密码: ${currentPassword}`, 'success');
                } else {
                    showResult(`✗ 密码验证失败: ${data.msg}`, 'error');
                }
            } catch (error) {
                console.error('验证错误:', error);
                showResult(`✗ 网络错误: ${error.message}`, 'error');
            }
        }
        
        async function processPayment() {
            if (!selectedPayment) {
                showResult('请选择支付方式', 'error');
                return;
            }
            
            if (currentPassword.length !== 6) {
                showResult('请输入6位密码', 'error');
                return;
            }
            
            showResult('正在处理支付...', 'info');
            
            // 先验证密码
            try {
                const response = await fetch('/payment/verify-password/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        password: currentPassword,
                        order_number: '{{ order.order_number|default:"TEST123456" }}'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showResult('密码验证成功，正在跳转...', 'success');
                    
                    // 根据支付方式跳转
                    setTimeout(() => {
                        switch(selectedPayment) {
                            case 'alipay':
                                window.location.href = '/payment/alipay/pay/{{ order.order_number|default:"TEST123456" }}/';
                                break;
                            case 'wechat':
                            case 'bank':
                                window.location.href = '/payment/simulate-success/{{ order.order_number|default:"TEST123456" }}/';
                                break;
                            default:
                                showResult('支付方式错误', 'error');
                        }
                    }, 1000);
                } else {
                    showResult(`密码验证失败: ${data.msg}`, 'error');
                }
            } catch (error) {
                console.error('支付处理错误:', error);
                showResult(`支付处理失败: ${error.message}`, 'error');
            }
        }
        
        function showResult(message, type) {
            const result = document.getElementById('result');
            result.innerHTML = message;
            result.className = `result ${type}`;
        }
    </script>
</body>
</html>
