{% extends "base.html" %}
{% load static %}

{% block title %}我的收藏{% endblock %}

{% block extra_css %}
<style>
    /* 页面背景 */
    body {
        background-color: #f9f9f9;
    }
    
    /* 主标题样式 */
    .page-title {
        color: #e41c3c;
        font-weight: 700;
        position: relative;
        padding-left: 15px;
        margin-bottom: 1.5rem;
    }
    
    .page-title::before {
        content: '';
        position: absolute;
        left: 0;
        top: 5px;
        height: 80%;
        width: 5px;
        background: linear-gradient(to bottom, #e41c3c, #ff6b6b);
        border-radius: 10px;
    }
    
    /* 收藏卡片样式 */
    .favorite-card {
        transition: all 0.3s ease;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        height: 100%;
        border: 1px solid #f0f0f0;
        position: relative;
    }
    
    .favorite-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(228, 28, 60, 0.15);
        border-color: rgba(228, 28, 60, 0.2);
        border-left: 3px solid #e41c3c;
    }
    
    .favorite-card .card-img-top {
        height: 200px;
        object-fit: cover;
        transition: all 0.5s ease;
    }
    
    .favorite-card:hover .card-img-top {
        transform: scale(1.1);
    }
    
    .favorite-card .card-body {
        padding: 1.25rem;
        background: linear-gradient(to bottom, #fff, #fff9f9);
    }
    
    .favorite-card .card-title {
        font-weight: 600;
        margin-bottom: 0.75rem;
        font-size: 1.1rem;
        line-height: 1.4;
        height: 3.1rem;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    
    .favorite-item-price {
        color: #e41c3c;
        font-weight: bold;
        font-size: 1.25rem;
        display: flex;
        align-items: center;
    }
    
    .favorite-item-price::before {
        content: '¥';
        font-size: 0.85rem;
        margin-right: 2px;
        position: relative;
        top: -2px;
    }
    
    /* 空收藏提示 */
    .favorite-empty {
        text-align: center;
        padding: 60px 0;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
        margin-top: 1rem;
        border: 1px solid #f8e0e0;
        border-left: 5px solid #e41c3c;
    }
    
    .favorite-empty i {
        font-size: 5rem;
        color: #ffccd5;
        margin-bottom: 20px;
        display: block;
        animation: heartBeat 1.5s infinite ease-in-out;
    }
    
    @keyframes heartBeat {
        0% { transform: scale(1); }
        14% { transform: scale(1.3); }
        28% { transform: scale(1); }
        42% { transform: scale(1.3); }
        70% { transform: scale(1); }
    }
    
    .favorite-empty h3 {
        color: #e41c3c;
        margin-bottom: 20px;
        font-weight: 600;
    }
    
    .favorite-empty p {
        color: #666;
        max-width: 80%;
        margin: 0 auto 25px;
    }
    
    /* 移除收藏按钮 */
    .remove-favorite-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #e41c3c;
        border: none;
        transition: all 0.3s ease;
        z-index: 2;
        opacity: 0.8;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    
    .favorite-card:hover .remove-favorite-btn {
        opacity: 1;
        transform: scale(1.1);
    }
    
    .remove-favorite-btn:hover {
        background-color: #e41c3c;
        color: white;
        transform: rotate(90deg) scale(1.1);
        box-shadow: 0 2px 8px rgba(228, 28, 60, 0.3);
    }
    
    /* 收藏时间 */
    .favorite-time {
        color: #999;
        font-size: 0.8rem;
        display: flex;
        align-items: center;
    }
    
    .favorite-time i {
        margin-right: 4px;
        font-size: 0.75rem;
    }
    
    /* 商品标签 */
    .favorite-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 2;
    }
    
    .favorite-badge .badge {
        margin-right: 4px;
        padding: 5px 8px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .bg-danger {
        background-color: #e41c3c !important;
    }
    
    /* 面包屑导航 */
    .breadcrumb {
        background-color: rgba(255, 255, 255, 0.9);
        padding: 12px 18px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
        margin-bottom: 1.5rem;
        border-left: 3px solid #e41c3c;
    }
    
    .breadcrumb-item a {
        color: #e41c3c;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .breadcrumb-item a:hover {
        color: #ff6b6b;
        text-decoration: none;
    }
    
    .breadcrumb-item.active {
        color: #666;
    }
    
    /* 统计卡片 */
    .stats-card {
        background: linear-gradient(135deg, #fff, #fff5f5);
        border-radius: 10px;
        border-left: 4px solid #e41c3c;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }
    
    .stats-card .card-title {
        font-weight: 600;
        color: #333;
    }
    
    .stats-card .text-danger {
        color: #e41c3c !important;
    }
    
    .stats-card .btn-primary {
        background-color: #e41c3c;
        border-color: #e41c3c;
        transition: all 0.3s ease;
        border-radius: 8px;
        font-weight: 500;
        padding: 8px 20px;
        box-shadow: 0 4px 10px rgba(228, 28, 60, 0.2);
    }
    
    .stats-card .btn-primary:hover {
        background-color: #d01533;
        border-color: #d01533;
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(228, 28, 60, 0.3);
    }
    
    /* 分页样式 */
    .pagination .page-item .page-link {
        color: #e41c3c;
        border-color: #eee;
        border-radius: 6px;
        margin: 0 3px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0,0,0,0.03);
    }
    
    .pagination .page-item.active .page-link {
        background-color: #e41c3c;
        border-color: #e41c3c;
        color: white;
        box-shadow: 0 2px 8px rgba(228, 28, 60, 0.2);
    }
    
    .pagination .page-item .page-link:hover {
        background-color: #fff5f5;
        color: #e41c3c;
    }
    
    .pagination .page-item.disabled .page-link {
        color: #ccc;
        background-color: #f9f9f9;
    }
    
    /* 查看详情按钮 */
    .btn-outline-primary {
        color: #e41c3c;
        border-color: #e41c3c;
        border-radius: 6px;
        transition: all 0.3s ease;
        font-weight: 500;
    }
    
    .btn-outline-primary:hover {
        background-color: #e41c3c;
        border-color: #e41c3c;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(228, 28, 60, 0.15);
    }

    /* 添加消息提示样式 */
    .toast-message {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 10px;
        color: white;
        font-weight: 500;
        z-index: 9999;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        animation: slideIn 0.3s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .toast-message.success {
        background-color: #28a745;
    }

    .toast-message.error {
        background-color: #dc3545;
    }

    .toast-message.info {
        background-color: #17a2b8;
    }

    .toast-message.fade-out {
        animation: fadeOut 0.3s ease forwards;
    }

    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }

    @keyframes fadeOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-3">
        <div class="col">
            <h2 class="page-title">我的收藏</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'home:index' %}"><i class="fas fa-home"></i> 首页</a></li>
                    <li class="breadcrumb-item active"><i class="fas fa-heart text-danger"></i> 我的收藏</li>
                </ol>
            </nav>
        </div>
    </div>
    
    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="row mb-4">
        <div class="col">
            <div class="card stats-card">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="card-title mb-0">
                            <i class="fas fa-heart text-danger me-2"></i>
                            收藏商品总数：<span class="text-danger">{{ total_favorites }}</span>
                        </h5>
                    </div>
                    <div>
                        <a href="{% url 'goods:list' %}" class="btn btn-primary">
                            <i class="fas fa-shopping-basket me-2"></i>
                            继续购物
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    {% if favorites %}
    <div class="row">
        {% for favorite in favorites %}
        <div class="col-md-3 mb-4">
            <div class="card favorite-card">
                <div class="position-relative">
                    {% if favorite.product.main_image %}
                    <a href="{% url 'goods:detail' favorite.product.id %}">
                        <img src="{{ favorite.product.main_image.url }}" class="card-img-top" alt="{{ favorite.product.name }}">
                    </a>
                    {% else %}
                    <a href="{% url 'goods:detail' favorite.product.id %}">
                        <img src="{% static 'images/no-image.png' %}" class="card-img-top" alt="{{ favorite.product.name }}">
                    </a>
                    {% endif %}
                    
                    <!-- 移除收藏按钮 -->
                    <form method="post" action="{% url 'account:remove_from_favorite' favorite.product.id %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="remove-favorite-btn" title="取消收藏">
                            <i class="fas fa-heart-broken"></i>
                        </button>
                    </form>
                    
                    <!-- 商品标签 -->
                    <div class="favorite-badge">
                        {% if favorite.product.is_hot %}
                        <span class="badge bg-danger">热门</span>
                        {% endif %}
                        {% if favorite.product.is_new %}
                        <span class="badge bg-success">新品</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="card-body">
                    <h5 class="card-title">
                        <a href="{% url 'goods:detail' favorite.product.id %}" class="text-decoration-none text-dark">
                            {{ favorite.product.name }}
                        </a>
                    </h5>
                    <p class="card-text text-truncate text-muted">{{ favorite.product.description|truncatechars:50 }}</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="favorite-item-price">{{ favorite.product.price }}</span>
                        <a href="{% url 'goods:detail' favorite.product.id %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-eye me-1"></i> 查看详情
                        </a>
                    </div>
                    <div class="mt-2">
                        <small class="favorite-time"><i class="far fa-clock"></i> 收藏于：{{ favorite.created_at|date:"Y-m-d H:i" }}</small>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <!-- 分页 -->
    {% if page_obj.has_other_pages %}
    <nav aria-label="收藏商品分页">
        <ul class="pagination justify-content-center">
            {% if page_obj.has_previous %}
            <li class="page-item">
                <a class="page-link" href="?page=1" aria-label="首页">
                    <span aria-hidden="true">&laquo;&laquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="上一页">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">&laquo;&laquo;</span>
            </li>
            <li class="page-item disabled">
                <span class="page-link">&laquo;</span>
            </li>
            {% endif %}
            
            {% for num in page_obj.paginator.page_range %}
            {% if page_obj.number == num %}
            <li class="page-item active"><span class="page-link">{{ num }}</span></li>
            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
            <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
            {% endif %}
            {% endfor %}
            
            {% if page_obj.has_next %}
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="下一页">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            <li class="page-item">
                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="末页">
                    <span aria-hidden="true">&raquo;&raquo;</span>
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">&raquo;</span>
            </li>
            <li class="page-item disabled">
                <span class="page-link">&raquo;&raquo;</span>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
    
    {% else %}
    <!-- 空收藏夹提示 -->
    <div class="favorite-empty">
        <i class="fas fa-heart-broken"></i>
        <h3>您的收藏夹还是空的</h3>
        <p class="text-muted mb-4">浏览商品并收藏您喜欢的商品，它们会显示在这里</p>
        <a href="{% url 'goods:list' %}" class="btn btn-primary btn-lg">
            <i class="fas fa-shopping-basket me-2"></i>
            去逛逛
        </a>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 添加收藏卡片动画效果
        const cards = document.querySelectorAll('.favorite-card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px)';
                this.style.boxShadow = '0 15px 30px rgba(228, 28, 60, 0.15)';
                this.style.borderColor = 'rgba(228, 28, 60, 0.2)';
                this.style.borderLeft = '3px solid #e41c3c';
            });
            
            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.08)';
                this.style.borderColor = '#f0f0f0';
                this.style.borderLeft = '1px solid #f0f0f0';
            });
        });
        
        // 使用AJAX进行收藏删除
        const removeForms = document.querySelectorAll('form[action*="remove"]');
        removeForms.forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const card = this.closest('.col-md-3');
                const url = this.getAttribute('action');
                const button = this.querySelector('.remove-favorite-btn');
                
                // 添加删除动画
                button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                button.disabled = true;
                
                fetch(url, {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRFToken': this.querySelector('input[name="csrfmiddlewaretoken"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // 添加淡出动画
                        card.style.opacity = '0';
                        card.style.transform = 'scale(0.8)';
                        card.style.transition = 'all 0.5s ease';
                        
                        // 移除元素
                        setTimeout(() => {
                            card.remove();
                            
                            // 更新收藏总数
                            const totalElement = document.querySelector('.card-title');
                            const totalSpan = totalElement.querySelector('.text-danger');
                            const currentTotal = parseInt(totalSpan.textContent);
                            totalSpan.textContent = currentTotal - 1;
                            
                            // 如果没有收藏了，显示空收藏提示
                            if (currentTotal - 1 === 0) {
                                const row = document.querySelector('.row:not(.mb-4):not(.mb-3)');
                                const container = row.parentElement;
                                
                                // 移除分页
                                const pagination = container.querySelector('nav[aria-label="收藏商品分页"]');
                                if (pagination) {
                                    pagination.remove();
                                }
                                
                                // 创建空收藏提示
                                row.innerHTML = `
                                <div class="favorite-empty">
                                    <i class="fas fa-heart-broken"></i>
                                    <h3>您的收藏夹还是空的</h3>
                                    <p class="text-muted mb-4">浏览商品并收藏您喜欢的商品，它们会显示在这里</p>
                                    <a href="{% url 'goods:list' %}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-shopping-basket me-2"></i>
                                        去逛逛
                                    </a>
                                </div>
                                `;
                            }
                            
                            // 显示成功提示
                            const toast = document.createElement('div');
                            toast.className = 'toast-message success';
                            toast.innerHTML = `<i class="fas fa-check-circle"></i> ${data.message}`;
                            document.body.appendChild(toast);
                            
                            setTimeout(() => {
                                toast.classList.add('fade-out');
                                setTimeout(() => {
                                    document.body.removeChild(toast);
                                }, 500);
                            }, 2000);
                        }, 500);
                    } else {
                        // 恢复按钮状态
                        button.innerHTML = '<i class="fas fa-heart-broken"></i>';
                        button.disabled = false;
                        
                        // 显示错误提示
                        const toast = document.createElement('div');
                        toast.className = 'toast-message error';
                        toast.innerHTML = `<i class="fas fa-exclamation-circle"></i> 删除失败，请稍后重试`;
                        document.body.appendChild(toast);
                        
                        setTimeout(() => {
                            toast.classList.add('fade-out');
                            setTimeout(() => {
                                document.body.removeChild(toast);
                            }, 500);
                        }, 2000);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    // 恢复按钮状态
                    button.innerHTML = '<i class="fas fa-heart-broken"></i>';
                    button.disabled = false;
                    
                    // 显示错误提示
                    const toast = document.createElement('div');
                    toast.className = 'toast-message error';
                    toast.innerHTML = `<i class="fas fa-exclamation-circle"></i> 网络错误，请稍后重试`;
                    document.body.appendChild(toast);
                    
                    setTimeout(() => {
                        toast.classList.add('fade-out');
                        setTimeout(() => {
                            document.body.removeChild(toast);
                        }, 500);
                    }, 2000);
                });
            });
        });
    });
</script>
{% endblock %} 