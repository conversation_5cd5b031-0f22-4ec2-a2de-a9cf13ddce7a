{% extends 'base.html' %}

{% block title %}订单 {{ order.order_no }} - 订单详情 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}订单详情{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <a href="{% url 'orders:list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
    {% if order.status == 'paid' %}
    <button type="button" class="btn btn-success update-status" 
            data-order-id="{{ order.id }}" 
            data-status="shipped">
        <i class="fas fa-shipping-fast me-1"></i>发货
    </button>
    {% elif order.status == 'shipped' %}
    <button type="button" class="btn btn-info update-status" 
            data-order-id="{{ order.id }}" 
            data-status="delivered">
        <i class="fas fa-check me-1"></i>确认送达
    </button>
    {% endif %}
    {% if order.status == 'pending' %}
    <button type="button" class="btn btn-danger update-status" 
            data-order-id="{{ order.id }}" 
            data-status="cancelled">
        <i class="fas fa-times me-1"></i>取消订单
    </button>
    {% endif %}
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- 订单基本信息 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-bag me-2"></i>订单信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td width="100"><strong>订单号：</strong></td>
                                <td>{{ order.order_no }}</td>
                            </tr>
                            <tr>
                                <td><strong>用户：</strong></td>
                                <td>
                                    <a href="{% url 'users:detail' order.user.id %}" class="text-decoration-none">
                                        {{ order.user.username }}
                                    </a>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>状态：</strong></td>
                                <td>
                                    <span class="badge bg-{{ order.get_status_display_color }}">
                                        {{ order.get_status_display }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>支付方式：</strong></td>
                                <td>
                                    {% if order.payment_method %}
                                        {{ order.get_payment_method_display }}
                                    {% else %}
                                        <span class="text-muted">未选择</span>
                                    {% endif %}
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>金额信息</h6>
                        <table class="table table-sm table-borderless">
                            <tr>
                                <td width="100"><strong>商品总额：</strong></td>
                                <td>¥{{ order.total_amount }}</td>
                            </tr>
                            <tr>
                                <td><strong>优惠金额：</strong></td>
                                <td class="text-danger">-¥{{ order.discount_amount }}</td>
                            </tr>
                            <tr>
                                <td><strong>实付金额：</strong></td>
                                <td class="h6 text-success">¥{{ order.final_amount }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h6>收货信息</h6>
                        <div class="bg-light p-3 rounded">
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>收货人：</strong> {{ order.receiver_name }}
                                </div>
                                <div class="col-md-4">
                                    <strong>联系电话：</strong> {{ order.receiver_phone }}
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-12">
                                    <strong>收货地址：</strong> {{ order.receiver_address }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if order.remark %}
                <div class="row mb-4">
                    <div class="col-12">
                        <h6>订单备注</h6>
                        <div class="bg-light p-3 rounded">
                            {{ order.remark }}
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- 订单商品 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-box me-2"></i>订单商品
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>商品</th>
                                <th>单价</th>
                                <th>数量</th>
                                <th>小计</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in order_items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        {% if item.product.main_image %}
                                            <img src="{{ item.product.main_image.url }}" alt="{{ item.product_name }}" 
                                                 class="rounded me-3" width="60" height="60" style="object-fit: cover;">
                                        {% else %}
                                            <div class="bg-light rounded d-flex align-items-center justify-content-center me-3" 
                                                 style="width: 60px; height: 60px;">
                                                <i class="fas fa-image text-muted"></i>
                                            </div>
                                        {% endif %}
                                        <div>
                                            <div class="fw-bold">{{ item.product_name }}</div>
                                            <small class="text-muted">
                                                <a href="{% url 'goods:detail' item.product.id %}" class="text-decoration-none">
                                                    查看商品
                                                </a>
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>¥{{ item.product_price }}</td>
                                <td>{{ item.quantity }}</td>
                                <td class="fw-bold">¥{{ item.subtotal }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot>
                            <tr class="table-light">
                                <td colspan="3" class="text-end"><strong>合计：</strong></td>
                                <td class="fw-bold text-success">¥{{ order.final_amount }}</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 侧边栏信息 -->
    <div class="col-md-4">
        <!-- 订单状态 -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>订单状态
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <span class="badge bg-{{ order.get_status_display_color }} fs-6">
                        {{ order.get_status_display }}
                    </span>
                </div>
                
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">订单创建</h6>
                            <p class="timeline-text">{{ order.created_time|date:"Y-m-d H:i:s" }}</p>
                        </div>
                    </div>
                    
                    {% if order.paid_time %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">支付完成</h6>
                            <p class="timeline-text">{{ order.paid_time|date:"Y-m-d H:i:s" }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if order.shipped_time %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">商品发货</h6>
                            <p class="timeline-text">{{ order.shipped_time|date:"Y-m-d H:i:s" }}</p>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if order.delivered_time %}
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">确认收货</h6>
                            <p class="timeline-text">{{ order.delivered_time|date:"Y-m-d H:i:s" }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- 快速操作 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if order.status == 'paid' %}
                    <button type="button" class="btn btn-success update-status" 
                            data-order-id="{{ order.id }}" 
                            data-status="shipped">
                        <i class="fas fa-shipping-fast me-2"></i>标记发货
                    </button>
                    {% elif order.status == 'shipped' %}
                    <button type="button" class="btn btn-info update-status" 
                            data-order-id="{{ order.id }}" 
                            data-status="delivered">
                        <i class="fas fa-check me-2"></i>确认送达
                    </button>
                    {% endif %}
                    
                    {% if order.status == 'pending' %}
                    <button type="button" class="btn btn-danger update-status" 
                            data-order-id="{{ order.id }}" 
                            data-status="cancelled">
                        <i class="fas fa-times me-2"></i>取消订单
                    </button>
                    {% endif %}
                    
                    <a href="{% url 'users:detail' order.user.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-user me-2"></i>查看用户
                    </a>
                    
                    <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>打印订单
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 订单统计 -->
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>订单统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary mb-1">{{ order_items|length }}</h6>
                            <small class="text-muted">商品种类</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success mb-1">
                            {% for item in order_items %}{{ item.quantity }}{% if not forloop.last %} + {% endif %}{% endfor %}
                        </h6>
                        <small class="text-muted">商品数量</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <h6 class="text-warning mb-1">¥{{ order.final_amount }}</h6>
                    <small class="text-muted">订单金额</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-text {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 0;
}

@media print {
    .btn, .card-header, .timeline::before {
        display: none !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 更新订单状态
    $('.update-status').click(function() {
        const orderId = $(this).data('order-id');
        const status = $(this).data('status');
        
        let confirmText = '';
        switch(status) {
            case 'shipped':
                confirmText = '确定要将此订单标记为已发货吗？';
                break;
            case 'delivered':
                confirmText = '确定要将此订单标记为已送达吗？';
                break;
            case 'cancelled':
                confirmText = '确定要取消此订单吗？';
                break;
            default:
                confirmText = '确定要更新此订单状态吗？';
        }
        
        if (confirm(confirmText)) {
            $.post('{% url "orders:update_status" 0 %}'.replace('0', orderId), {
                'status': status,
                'csrfmiddlewaretoken': '{{ csrf_token }}'
            })
            .done(function(data) {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || '更新失败，请重试');
                }
            })
            .fail(function() {
                alert('更新失败，请重试');
            });
        }
    });
});
</script>
{% endblock %}
