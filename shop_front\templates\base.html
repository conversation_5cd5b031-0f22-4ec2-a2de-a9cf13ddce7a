<!DOCTYPE html>
{% load static %}
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MARS BUY-品质生活首选{% endblock %}</title>
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    <link href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* 工具类 */
        .clearfix::after {
            content: '';
            display: block;
            clear: both;
        }
        .fl {
            float: left;
        }
        .fr {
            float: right;
        }
        /* 顶部导航条 */
        .topbar {
            height: 35px;
            line-height: 35px;
            background: #f5f5f5;
            border-bottom: 1px solid #e5e5e5;
            font-size: 12px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .topbar .container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .topbar .welcome {
            margin-right: 10px;
            color: #666;
            display: flex;
            align-items: center;
        }
        .topbar .hello {
            color: #666;
            margin-right: 15px;
            font-weight: 500;
        }
        .topbar .login, .topbar .register, .topbar .admin {
            margin-left: 10px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            padding: 0 8px;
            border-radius: 3px;
        }
        .topbar .login:hover, .topbar .register:hover {
            color: #dc3545;
            background-color: rgba(220, 53, 69, 0.1);
        }
        .topbar .admin {
            color: #ff6000;
            font-weight: bold;
        }
        .topbar-nav {
            display: flex;
            align-items: center;
        }
        .topbar-nav .list {
            display: flex;
            margin: 0;
            padding: 0;
            list-style: none;
            align-items: center;
        }
        .topbar-nav .list li {
            margin-left: 15px;
            position: relative;
            display: flex;
            align-items: center;
        }
        .topbar-nav .list li a {
            color: #666;
            text-decoration: none;
            padding: 0 8px;
            transition: all 0.3s ease;
            font-weight: 500;
            white-space: nowrap;
        }
        .topbar-nav .list li a:hover {
            color: #dc3545;
        }
        .topbar-nav .list li a i {
            margin-right: 3px;
            color: #dc3545;
        }
        .topbar-nav .user-info,
        .topbar-nav .user-actions {
            display: flex;
            align-items: center;
            margin: 0 5px;
        }
        .topbar-nav .user-avatar {
            width: 20px;
            height: 20px;
            margin-right: 5px;
        }
        .topbar-nav .user-details {
            display: flex;
            align-items: center;
        }
        .topbar-nav .user-greeting,
        .topbar-nav .username {
            margin: 0 2px;
        }
        /* 头部 */
        .header {
            height: 120px;
            padding: 10px 0 20px; /* 减少上下内边距，使logo位置更高 */
            background: #fff;
            border-bottom: 1px solid #e5e5e5;
            margin-bottom: 15px;
        }
        .header .logo {
            width: 190px;
            display: flex;
            align-items: center; /* 垂直居中对齐 */
            justify-content: center;
            margin-bottom: 0;
            padding-top: 0;
        }
        .header .logo a {
            display: block;
            text-decoration: none;
        }
        .header .logo img {
            max-height: 75px; /* 保持大尺寸 */
            border-radius: 8px;
            margin: 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        .header .logo img:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        }
        .header .search-container {
            flex: 1;
            margin: 0 30px 0 40px; /* 调整左右边距 */
            position: relative;
            display: flex;
            flex-direction: column; /* 垂直布局 */
            align-items: center; /* 水平居中 */
        }
        .header .search {
            width: 100%;
            max-width: 520px; /* 调整宽度比例 */
            height: 46px; /* 调整高度 */
            position: relative;
            margin: 12px 0 6px 0; /* 调整上下边距保持居中 */
            border-radius: 23px; /* 完全圆角 */
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: #fff; /* 确保背景为白色 */
            border: 1px solid #e0e0e0; /* 添加淡灰色边框 */
            display: flex; /* 使用flex布局 */
            align-items: center; /* 垂直居中 */
        }
        .header .search:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .header .search input {
            flex: 1; /* 占据剩余空间 */
            height: 100%;
            padding: 0 70px 0 20px; /* 调整内边距，为按钮留出空间 */
            border: none; /* 去掉边框 */
            outline: none;
            font-size: 14px; /* 调整文字大小适应新高度 */
            border-radius: 23px; /* 调整圆角匹配搜索框 */
            background: transparent; /* 透明背景 */
            transition: all 0.3s ease;
            line-height: normal; /* 使用正常行高 */
            box-sizing: border-box; /* 确保padding不会影响总宽度 */
            text-align: left; /* 文字左对齐 */
            color: #333; /* 文字颜色 */
        }
        .header .search:focus-within {
            border-color: #ff6b35;
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.15);
        }

        .header .search input:focus {
            outline: none;
        }
        .header .search input::placeholder {
            color: #999;
            font-size: 14px; /* 与输入文字大小一致 */
            font-weight: 400;
            line-height: normal;
        }
        .header .search button {
            position: absolute;
            right: 4px; /* 稍微向内缩进，让按钮小于外框 */
            top: 4px; /* 稍微向下缩进 */
            width: 60px; /* 调整按钮宽度 */
            height: 38px; /* 减小按钮高度，让它小于外框 */
            background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%); /* 橙色渐变 */
            color: #fff;
            border: none;
            cursor: pointer;
            font-size: 14px; /* 调整搜索图标大小 */
            font-weight: 600; /* 加粗文字 */
            border-radius: 19px; /* 调整圆角，让按钮更圆润 */
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3); /* 橙色阴影效果 */
        }
        .header .search button:hover {
            background: linear-gradient(135deg, #ff5722 0%, #ff6b35 100%); /* 橙色悬停效果 */
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4); /* 增强悬停时的橙色阴影 */
        }

        .header .header-right {
            width: 200px;
            text-align: right;
        }
        /* 容器通用样式 */
        .container {
            width: 1300px;
            margin: 0 auto;
        }
        /* 页脚样式 */
        .footer {
            background-color: #f5f5f5;
            padding: 30px 0 20px;
            margin-top: 30px;
            border-top: 1px solid #e5e5e5;
        }
        .top-links {
            padding-bottom: 20px;
        }
        .serve-list {
            margin-bottom: 0;
        }
        .serve-list dt {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .serve-list dd {
            line-height: 24px;
            margin-bottom: 6px;
        }
        .serve-list dd a {
            color: #666;
            text-decoration: none;
        }
        .serve-list dd a:hover {
            color: #dc3545;
        }
        .footer-line {
            height: 1px;
            background-color: #e5e5e5;
            margin: 10px 0 20px;
        }
        .cooperation-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 10px;
            padding-left: 0;
            list-style: none;
        }
        .cooperation-list li {
            margin: 0 10px;
            position: relative;
        }

        .cooperation-list li a {
            color: #666;
            text-decoration: none;
            font-size: 12px;
        }
        .cooperation-list li a:hover {
            color: #DD302D;
        }
        .bottom-links-aside {
            text-align: center;
            color: #999;
            margin-top: 10px;
            font-size: 12px;
        }

        /* 用户信息样式 */
        .user-info {
            display: inline-flex;
            align-items: center;
            margin-right: 15px;
            padding: 2px 8px;
            background: transparent;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .user-info:hover {
            background: rgba(255, 96, 0, 0.1);
        }

        .user-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 8px;
            border: 1px solid #ddd;
            transition: all 0.3s ease;
        }

        .user-avatar:hover {
            border-color: #ff6000;
            transform: scale(1.05);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-details {
            display: flex;
            align-items: center;
            line-height: 1;
        }

        .user-greeting {
            font-size: 12px;
            color: #666;
            margin-right: 3px;
        }

        .user-role {
            font-size: 12px;
            color: #666;
            margin-right: 3px;
        }

        .username {
            font-size: 12px;
            font-weight: bold;
            color: #333;
        }

        .vip-badge {
            display: inline-block;
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            font-size: 10px;
            font-weight: bold;
            padding: 1px 4px;
            border-radius: 6px;
            margin-left: 3px;
        }

        .user-actions {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .guest-actions {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .admin-link {
            color: #ff6000 !important;
            text-decoration: none;
            padding: 2px 6px;
            border-radius: 10px;
            background: rgba(255, 96, 0, 0.1);
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .admin-link:hover {
            background: rgba(255, 96, 0, 0.2);
        }

        .topbar .welcome a {
            color: #666;
            text-decoration: none;
            padding: 2px 6px;
            border-radius: 10px;
            transition: all 0.3s ease;
            font-size: 12px;
        }

        .topbar .welcome a:hover {
            color: #ff6000;
            background: rgba(255, 96, 0, 0.1);
        }

        .topbar .welcome a i {
            margin-right: 3px;
        }

        /* 搜索框响应式设计 */
        @media (max-width: 1200px) {
            .header .search-container {
                margin: 0 25px 0 30px;
            }
            .header .search {
                max-width: 480px;
            }
            .hot-keywords {
                max-width: 480px !important;
            }
        }

        @media (max-width: 992px) {
            .header .search-container {
                margin: 0 20px 0 25px;
            }
            .header .search {
                max-width: 420px;
                height: 42px;
            }
            .header .search input {
                font-size: 13px;
                padding: 0 60px 0 16px;
                border-radius: 21px;
                line-height: normal; /* 使用正常行高 */
            }
            .header .search button {
                right: 4px;
                top: 4px;
                width: 50px; /* 调整按钮宽度 */
                height: 34px; /* 调整按钮高度 */
                font-size: 12px;
                border-radius: 17px; /* 调整圆角 */
            }
            .hot-keywords {
                max-width: 420px !important;
                font-size: 11px !important;
                gap: 6px !important;
            }
        }

        @media (max-width: 768px) {
            .header .search-container {
                margin: 0 15px 0 20px;
            }
            .header .search {
                max-width: 350px;
                height: 40px;
            }
            .header .search input {
                font-size: 13px;
                padding: 0 45px 0 15px;
                border-radius: 20px;
                line-height: normal; /* 使用正常行高 */
            }
            .header .search button {
                right: 3px;
                top: 3px;
                width: 39px; /* 调整按钮宽度 */
                height: 34px; /* 调整按钮高度 */
                font-size: 14px;
                border-radius: 17px; /* 调整圆角 */
            }
            .hot-keywords {
                max-width: 350px !important;
                font-size: 11px !important;
                gap: 5px !important;
            }
            .hot-keywords a {
                padding: 1px 6px !important;
                font-size: 11px !important;
            }
        }

        @media (max-width: 576px) {
            .header .search-container {
                margin: 0 10px 0 15px;
            }
            .header .search {
                max-width: 280px;
                height: 38px;
            }
            .header .search input {
                font-size: 12px;
                padding: 0 42px 0 12px;
                border-radius: 19px;
                line-height: normal; /* 使用正常行高 */
            }
            .header .search button {
                right: 3px;
                top: 3px;
                width: 36px; /* 调整按钮宽度 */
                height: 32px; /* 调整按钮高度 */
                font-size: 13px;
                border-radius: 16px; /* 调整圆角 */
            }
            .hot-keywords {
                max-width: 280px !important;
                font-size: 10px !important;
                gap: 4px !important;
                margin-top: 5px !important;
            }
            .hot-keywords span {
                font-size: 10px !important;
            }
            .hot-keywords a {
                padding: 1px 5px !important;
                font-size: 10px !important;
            }
        }

        /* 通用头像链接样式 */
        .avatar-link {
            display: inline-block;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 50%;
            overflow: hidden;
        }

        .avatar-link:hover {
            transform: scale(1.05);
            box-shadow: 0 0 10px rgba(0,0,0,0.2);
        }

        .avatar-link img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: all 0.3s ease;
        }

        .avatar-link:hover img {
            filter: brightness(1.1);
        }

        /* 不同尺寸的头像 */
        .avatar-sm {
            width: 32px;
            height: 32px;
        }

        .avatar-md {
            width: 48px;
            height: 48px;
        }

        .avatar-lg {
            width: 64px;
            height: 64px;
        }

        .avatar-xl {
            width: 96px;
            height: 96px;
        }

        /* 登录按钮样式 */
        .guest-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .guest-actions .login {
            color: #dc3545;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid #dc3545;
            background: rgba(220, 53, 69, 0.1);
        }

        .guest-actions .register {
            color: #6c757d;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid #6c757d;
            background: rgba(108, 117, 125, 0.1);
        }

        .guest-actions .admin-link {
            color: #28a745;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-weight: 500;
            border: 1px solid #28a745;
            background: rgba(40, 167, 69, 0.1);
            font-size: 12px;
        }

        .guest-actions .login:hover {
            color: white;
            background: #dc3545;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(220, 53, 69, 0.3);
        }

        .guest-actions .register:hover {
            color: white;
            background: #6c757d;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(108, 117, 125, 0.3);
        }

        .guest-actions .admin-link:hover {
            color: white;
            background: #28a745;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(40, 167, 69, 0.3);
        }

        .guest-actions i {
            margin-right: 5px;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 顶部导航条 -->
    <div class="topbar">
        <div class="container">
            <div class="welcome fl">
                <span class="hello">欢迎来到MARS BUY！</span>
            </div>
            <div class="topbar-nav fr">
                <ul class="list">
                    <li><a href="{% url 'order:list' %}"><i class="fas fa-shopping-bag"></i> 我的订单</a></li>
                    <li><a href="{% url 'order:cart' %}"><i class="fas fa-shopping-cart"></i> 购物车</a></li>
                    <li><a href="{% url 'account:favorite_list' %}"><i class="fas fa-heart"></i> 收藏夹</a></li>
                    <li><a href="{% url 'reviews:my_reviews' %}"><i class="fas fa-star"></i> 我的评价</a></li>
                    <li><a href="{% url 'users:center' %}"><i class="fas fa-user-circle"></i> 个人中心</a></li>
                    <li><a href="#" onclick="showSystemMessage('欢迎使用在线客服功能', '系统消息', 'info'); return false;"><i class="fas fa-headset"></i> 在线客服</a></li>
                    
                    {% if user.is_authenticated %}
                    <li>
                        <div class="user-info" data-user-authenticated="true">
                            <div class="user-avatar">
                                <img src="{{ user.avatar.url }}" alt="{{ user.username }}" onerror="this.src='/media/avatars/default.png'">
                            </div>
                            <div class="user-details">
                                <span class="user-greeting">欢迎，</span>
                                <span class="username">{{ user.username }}</span>
                            </div>
                        </div>
                    </li>
                    <li>
                        {% if user.is_staff %}
                        <a href="http://127.0.0.1:8003/dashboard/" class="admin-link" target="_blank">
                            <i class="fas fa-cog"></i> 商家后台
                        </a>
                        {% endif %}
                    </li>
                    <li>
                        <a href="{% url 'users:logout' %}" class="register">
                            <i class="fas fa-sign-out-alt"></i> 退出
                        </a>
                    </li>
                    {% else %}
                    <li>
                        <div class="user-info">
                            <div class="user-avatar">
                                <img src="/media/avatars/default.png" alt="游客" onerror="this.src='/media/avatars/default.png'">
                            </div>
                            <div class="user-details">
                                <span class="user-greeting">欢迎，</span>
                                <span class="username">游客</span>
                            </div>
                        </div>
                    </li>
                    <li>
                        <a href="{% url 'users:login' %}" class="login">
                            <i class="fas fa-sign-in-alt"></i> 登录
                        </a>
                    </li>
                    <li>
                        <a href="{% url 'users:register' %}" class="register">
                            <i class="fas fa-user-plus"></i> 注册
                        </a>
                    </li>
                    <li>
                        <a href="http://127.0.0.1:8003/dashboard/" class="admin-link" target="_blank">
                            <i class="fas fa-cog"></i> 商家后台
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>

    <!-- 头部 -->
    <div class="header">
        <div class="container" style="display: flex; align-items: center;">
            <div class="logo">
                <a href="{% url 'home:index' %}">
                    <img src="/media/products/Ori/1749342499728(1).PNG" alt="MARS BUY Logo">
                </a>
            </div>
            <div class="search-container">
                <div class="search">
                    <form action="{% url 'goods:search' %}" method="get">
                        <input type="text" name="keyword" placeholder="搜索 商品/品牌/店铺">
                        <button type="submit">搜索</button>
                    </form>
                </div>
                <!-- 热门搜索词 -->
                <div class="hot-keywords" style="
                    width: 100%;
                    max-width: 520px;
                    margin-top: 6px;
                    margin-bottom: 4px;
                    font-size: 12px;
                    color: #999;
                    text-align: center;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-wrap: wrap;
                    gap: 8px;
                ">
                    <span style="color: #666; font-weight: 500; margin-right: 5px;">热门：</span>
                    <a href="{% url 'goods:search' %}?keyword=手机" style="
                        color: #dc3545;
                        text-decoration: none;
                        padding: 2px 8px;
                        border-radius: 10px;
                        transition: all 0.3s ease;
                        font-size: 12px;
                        background: rgba(220,53,69,0.08);
                    " onmouseover="this.style.backgroundColor='rgba(220,53,69,0.15)'; this.style.transform='translateY(-1px)'" onmouseout="this.style.backgroundColor='rgba(220,53,69,0.08)'; this.style.transform='translateY(0)'">手机</a>
                    <a href="{% url 'goods:search' %}?keyword=电脑" style="
                        color: #666;
                        text-decoration: none;
                        padding: 2px 8px;
                        border-radius: 10px;
                        transition: all 0.3s ease;
                        font-size: 12px;
                    " onmouseover="this.style.backgroundColor='rgba(220,53,69,0.1)'; this.style.color='#dc3545'; this.style.transform='translateY(-1px)'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#666'; this.style.transform='translateY(0)'">电脑</a>
                    <a href="{% url 'goods:search' %}?keyword=家电" style="
                        color: #666;
                        text-decoration: none;
                        padding: 2px 8px;
                        border-radius: 10px;
                        transition: all 0.3s ease;
                        font-size: 12px;
                    " onmouseover="this.style.backgroundColor='rgba(220,53,69,0.1)'; this.style.color='#dc3545'; this.style.transform='translateY(-1px)'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#666'; this.style.transform='translateY(0)'">家电</a>
                    <a href="{% url 'goods:search' %}?keyword=数码" style="
                        color: #666;
                        text-decoration: none;
                        padding: 2px 8px;
                        border-radius: 10px;
                        transition: all 0.3s ease;
                        font-size: 12px;
                    " onmouseover="this.style.backgroundColor='rgba(220,53,69,0.1)'; this.style.color='#dc3545'; this.style.transform='translateY(-1px)'" onmouseout="this.style.backgroundColor='transparent'; this.style.color='#666'; this.style.transform='translateY(0)'">数码</a>
                </div>
            </div>
            <div class="header-right">
                {% if user.is_authenticated %}
                <div style="text-align: center; font-size: 12px; color: #666;">
                    <div class="user-avatar" style="margin: 0 auto 5px; width: 40px; height: 40px;">
                        <img src="{{ user.avatar.url }}" alt="{{ user.username }}" onerror="this.src='/media/avatars/default.png'" style="border-radius: 50%; width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div>{{ user.username }}</div>
                </div>
                {% else %}
                <div style="text-align: center; font-size: 12px;">
                    <div class="user-avatar" style="margin: 0 auto 5px; width: 40px; height: 40px;">
                        <img src="/media/avatars/default.png" alt="未登录" style="border-radius: 50%; width: 100%; height: 100%; object-fit: cover;">
                    </div>
                    <div>
                        <span style="color: #666;">游客身份</span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 主要内容区 -->
    {% block content %}{% endblock %}

    <!-- 页脚 -->
    <div class="footer">
        <div class="container">
            <div class="top-links">
                <div class="row">
                    <div class="col-md-3">
                        <dl class="serve-list">
                            <dt>购物指南</dt>
                            <dd><a href="#">购物流程</a></dd>
                            <dd><a href="#">会员介绍</a></dd>
                            <dd><a href="#">生活旅行</a></dd>
                            <dd><a href="#">常见问题</a></dd>
                            <dd><a href="#">大家致电</a></dd>
                            <dd><a href="#">联系客服</a></dd>
                        </dl>
                    </div>
                    <div class="col-md-3">
                        <dl class="serve-list">
                            <dt>配送方式</dt>
                            <dd><a href="#">上门自提</a></dd>
                            <dd><a href="#">211限时达</a></dd>
                            <dd><a href="#">配送服务查询</a></dd>
                            <dd><a href="#">配送费收取标准</a></dd>
                            <dd><a href="#">海外配送</a></dd>
                        </dl>
                    </div>
                    <div class="col-md-3">
                        <dl class="serve-list">
                            <dt>支付方式</dt>
                            <dd><a href="#">货到付款</a></dd>
                            <dd><a href="#">在线支付</a></dd>
                            <dd><a href="#">分期付款</a></dd>
                            <dd><a href="#">邮局汇款</a></dd>
                            <dd><a href="#">公司转账</a></dd>
                        </dl>
                    </div>
                    <div class="col-md-3">
                        <dl class="serve-list">
                            <dt>售后服务</dt>
                            <dd><a href="#">售后政策</a></dd>
                            <dd><a href="#">价格保护</a></dd>
                            <dd><a href="#">退款说明</a></dd>
                            <dd><a href="#">返修/退换货</a></dd>
                            <dd><a href="#">取消订单</a></dd>
                        </dl>
                    </div>
                </div>
            </div>
            <div class="footer-line"></div>
            <div class="bottom-links">
                <div class="text-center mb-3">
                    <a href="{% url 'home:index' %}">
                        <img src="/media/products/Ori/1749342499728(1).PNG" alt="MARS BUY Logo" style="max-height: 50px; border-radius: 8px; margin-bottom: 10px;">
                    </a>
                </div>
                <ul class="cooperation-list clearfix">
                    <li><a href="#">关于我们</a></li>
                    <li><a href="#">联系我们</a></li>
                    <li><a href="#">联系客服</a></li>
                    <li><a href="#">合作招商</a></li>
                    <li><a href="#">商家帮助</a></li>
                    <li><a href="#">营销中心</a></li>
                    <li><a href="#">手机商城</a></li>
                    <li><a href="#">友情链接</a></li>
                    <li><a href="#">销售联盟</a></li>
                    <li><a href="#">隐私政策</a></li>
                </ul>
                <p class="bottom-links-aside">
                    <img src="/media/products/Ori/1749342499728(1).PNG" alt="MARS BUY Logo" style="max-height: 20px; border-radius: 4px; vertical-align: middle; margin-right: 5px;">
                    © 2024-2025 版权所有
                </p>
            </div>
        </div>
    </div>

    <!-- 引入通用消息提示和确认对话框 -->
    {% include 'includes/confirm_dialog.html' %}

    <!-- 引入jQuery库 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/5.0.2/js/bootstrap.bundle.min.js"></script>

    <!-- 实时更新功能 -->
    <script src="{% static 'js/realtime_updates.js' %}"></script>

    {% block extra_js %}{% endblock %}

    <!-- 处理Django消息 -->
    {% if messages %}
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        {% for message in messages %}
            {% if message.tags %}
                var messageType = '{{ message.tags }}';
                // 将Django消息类型映射到我们的消息类型
                if (messageType === 'error') {
                    messageType = 'error';
                } else if (messageType === 'success') {
                    messageType = 'success';
                } else if (messageType === 'warning') {
                    messageType = 'warning';
                } else {
                    messageType = 'info';
                }
                
                // 使用我们的系统消息框显示
                if (typeof window.showSystemMessage === 'function') {
                    window.showSystemMessage('{{ message|escapejs }}', '系统消息', messageType);
                }
            {% else %}
                // 如果没有标签，默认使用info类型
                if (typeof window.showSystemMessage === 'function') {
                    window.showSystemMessage('{{ message|escapejs }}', '系统消息', 'info');
                }
            {% endif %}
        {% endfor %}
    });
    </script>
    {% endif %}
</body>
</html> 

