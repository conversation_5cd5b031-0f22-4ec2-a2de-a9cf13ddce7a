from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone


class SharedModel(models.Model):
    """共享模型基类"""
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('创建时间'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('更新时间'))

    class Meta:
        abstract = True


class SystemConfig(SharedModel):
    """系统配置模型 - 示例共享模型"""
    key = models.CharField(max_length=100, unique=True, verbose_name=_('配置键'))
    value = models.TextField(verbose_name=_('配置值'))
    description = models.CharField(max_length=255, blank=True, null=True, verbose_name=_('配置描述'))
    is_active = models.BooleanField(default=True, verbose_name=_('是否启用'))

    class Meta:
        verbose_name = _('系统配置')
        verbose_name_plural = verbose_name
        ordering = ['key']
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return f"{self.key}: {self.value[:30]}"


class SharedCategory(SharedModel):
    """共享商品分类"""
    name = models.CharField(max_length=100, verbose_name=_('分类名称'))
    code = models.CharField(max_length=50, unique=True, verbose_name=_('分类编码'))
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, 
                               related_name='children', verbose_name=_('父分类'))
    level = models.PositiveIntegerField(default=1, verbose_name=_('分类层级'))
    is_active = models.BooleanField(default=True, verbose_name=_('是否启用'))
    sort_order = models.PositiveIntegerField(default=0, verbose_name=_('排序'))

    class Meta:
        verbose_name = _('商品分类')
        verbose_name_plural = verbose_name
        ordering = ['sort_order', 'code']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['parent', 'is_active']),
        ]

    def __str__(self):
        return self.name 