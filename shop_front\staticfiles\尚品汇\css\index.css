/* 基础设置 */
.container {
    width: 1190px;
    margin: 0 auto;
}
/* #region顶部导航条start */
.topbar {
    height: 30px;
    background-color: #ECECEC;
}
.welcome {
    height: 30px;
    line-height: 30px;
    font-size: 0;
    color: #666666;
}
.welcome span,
.welcome a {
    font-size: 12px;
}
.welcome .hello {
    margin-right: 28px;
}
.welcome .login {
    padding-right: 10px;
    border-right: 1px solid #666666;
}
.welcome .register {
    padding-left: 10px;
}
.topbar-nav .list {
    height: 30px;
    line-height: 30px;
}
.topbar-nav .list li {
    float: left;
}
.topbar-nav .list li a {
    padding: 0 15px;
    border-right: 1px solid #666666;
}
.topbar-nav .list li:first-child a {
    padding-left: 0;
}
.topbar-nav .list li:last-child a {
    padding-right: 0;
    border: 0;
}

/* #endregion顶部导航条end */

/*#region头部start  */
.header {
    height: 120px;
}
.header .search form {
    margin-top: 42px;
    font-size: 0;
}
.header .search input {
    width: 508px;
    height: 34px;
    border: 1px solid #DD302D;
}
.header .search button {
    width: 80px;
    height: 36px;
    background-color: #DD302D;
    vertical-align: top;
    background-image: url('../images/assets/serch_icon.png');
    background-repeat: no-repeat;
    background-position: 28px 6px;
}
/*#endregion头部end  */

/* #region主导航区start */
.main-nav {
    height: 48px;
    border-bottom: 1px solid #DD302D;
}
.all-types {
    height: 48px;
    line-height: 48px;
    width: 190px;
    background-color: #DD302D;
    font-size: 16px;
    color: #FFFFFF;
    text-align: center;
}
.main-nav-list {
    height: 48px;
    line-height: 48px;
}
.main-nav-list li {
    float: left;
    margin: 0 10px;
    font-size: 16px;
    color: #333333;
}
/* #endregion主导航区end */

/* #region主要内容区start */
.main-content {
    margin-top: 10px;
}
.slide-nav {
    width: 190px;
    height: 458px;
    background-color: #F4F4F4;
}
.slide-nav > li {
    height: 28px;
    line-height: 28px;
    padding-left: 16px;
    font-size: 14px;
}
.slide-nav > li:hover {
    background-color: #DD302D;
}
.slide-nav > li:hover > a {
    color: #FFFFFF;
}
.slide-nav > li:hover .second-menu {
    display: block;
}
.second-menu {
    display: none;
    position: absolute;
    left: 190px;
    top: 0;
    width: 680px;
    min-height: 458px;
    background-color: #FFFFFF;
    border: 1px solid #DD302D;
    padding: 20px;
}
.second-menu dl {
    margin-bottom: 20px;
}
.second-menu dt {
    float: left;
    width: 70px;
    font-weight: bold;
    margin-right: 10px;
}
.second-menu dd {
    float: left;
    margin-right: 10px;
}
.banner {
    width: 690px;
    height: 458px;
    margin: 0 10px;
    position: relative;
    overflow: hidden;
}
.banner .img-box {
    width: 2760px;
    height: 458px;
    transition: transform 0.3s;
}
.banner .img-box li {
    float: left;
}
.banner .pointer {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
}
.banner .pointer a {
    float: left;
    width: 8px;
    height: 8px;
    background-color: rgba(255, 255, 255, 0.4);
    margin: 0 2px;
    border-radius: 50%;
}
.banner .pointer a.active {
    background-color: #FFFFFF;
}
.banner .arrow a {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 40px;
    line-height: 40px;
    background-color: rgba(0, 0, 0, 0.3);
    text-align: center;
    font-size: 20px;
    color: #FFFFFF;
    display: none;
}
.banner:hover .arrow a {
    display: block;
}
.banner .arrow .arrow-l {
    left: 0;
}
.banner .arrow .arrow-r {
    right: 0;
}
.slide-other {
    width: 290px;
}
.message {
    height: 156px;
    border: 1px solid #E4E4E4;
    margin-bottom: 10px;
}
.message .title {
    height: 40px;
    line-height: 40px;
    padding: 0 15px;
    border-bottom: 1px solid #E4E4E4;
}
.message .title .leftfix {
    font-size: 14px;
}
.message .title .rightfix {
    font-size: 12px;
}
.message-list {
    padding: 5px 15px 0;
}
.message-list li {
    height: 26px;
    line-height: 26px;
}
.other-nav {
    height: 290px;
    border: 1px solid #E4E4E4;
}
.other-nav-list {
    margin-top: 16px;
}
.other-nav-list li {
    width: 48px;
    height: 70px;
    float: left;
    margin: 0 11px;
    text-align: center;
    cursor: pointer;
}
.other-nav-list li:first-child {
    margin-left: 16px;
}
.other-nav-list .picture {
    width: 48px;
    height: 48px;
    background-image: url('../images/assets/精灵图-侧边功能.png');
}
.other-nav-list:nth-child(1) li:nth-child(1) .picture {
    background-position: 0 0;
}
.other-nav-list:nth-child(1) li:nth-child(2) .picture {
    background-position: -48px 0;
}
.other-nav-list:nth-child(1) li:nth-child(3) .picture {
    background-position: -96px 0;
}
.other-nav-list:nth-child(1) li:nth-child(4) .picture {
    background-position: -144px 0;
}

.other-nav-list:nth-child(2) li:nth-child(1) .picture {
    background-position: 0 -48px;
}
.other-nav-list:nth-child(2) li:nth-child(2) .picture {
    background-position: -48px -48px;
}
.other-nav-list:nth-child(2) li:nth-child(3) .picture {
    background-position: -96px -48px;
}
.other-nav-list:nth-child(2) li:nth-child(4) .picture {
    background-position: -144px -48px;
}

.other-nav-list:nth-child(3) li:nth-child(1) .picture {
    background-position: 0 -96px;
}
.other-nav-list:nth-child(3) li:nth-child(2) .picture {
    background-position: -48px -96px;
}
.other-nav-list:nth-child(3) li:nth-child(3) .picture {
    background-position: -96px -96px;
}
.other-nav-list:nth-child(3) li:nth-child(4) .picture {
    background-position: -144px -96px;
}
/* #endregion主要内容区end */

/* #region秒杀start */
.seckill {
    margin-top: 10px;
}
.seckill img {
    float: left;
    margin-right: 11px;
    margin-top: 10px;
    cursor: pointer;
}
.seckill img:last-child {
    margin-right: 0;
}
/* #endregion秒杀end */

/* #region楼层start */
.floor {
    margin-top: 48px;
}
.floor-nav {
    height: 30px;
    line-height: 30px;
    padding-bottom: 4px;
    border-bottom: 2px solid #DD302D;
}
.floor-name {
    font-size: 20px;
}
.floor-nav-list li {
    float: left;
}
.floor-nav-list li a {
    padding: 0 8px;
    font-size: 14px;
}
.floor-info {
    margin-top: 10px;
}
.info-item {
    float: left;
    margin-right: 10px;
}
.item1 {
    width: 190px;
    height: 392px;
    background-color: #F4F4F4;
}
.item1-list {
    padding: 16px;
}
.item1-list li {
    width: 78px;
    height: 34px;
    border: 1px solid #D9D9D9;
    float: left;
    text-align: center;
    line-height: 34px;
    margin: 0 4px 4px 0;
    background-color: #FFFFFF;
}
.item2 {
    width: 340px;
    height: 392px;
}
.item3 {
    width: 206px;
    height: 392px;
}
.item3-top {
    width: 206px;
    height: 196px;
    border-bottom: 1px solid #E4E4E4;
}
.item4 {
    width: 206px;
    height: 392px;
}
.item5 {
    width: 206px;
    height: 392px;
    margin-right: 0;
}
.item5-top {
    width: 206px;
    height: 196px;
    border-bottom: 1px solid #E4E4E4;
}
/* #endregion楼层end */

/* #region页脚start */
.footer {
    margin-top: 48px;
    height: 440px;
    background-color: #EAEAEA;
    padding-top: 28px;
}
.top-links {
    height: 198px;
    border-bottom: 1px solid #E1E1E1;
}
.serve-list {
    width: 190px;
    float: left;
}
.serve-list li {
    height: 22px;
    line-height: 22px;
}
.serve-list li:first-child {
    height: 34px;
    line-height: 34px;
    font-size: 14px;
}
.serve-list:last-child {
    width: 242px;
}
.bottom-links {
    margin-top: 20px;
    text-align: center;
}
.cooperation-list {
    display: inline-block;
}
.cooperation-list li {
    float: left;
}
.cooperation-list li a {
    padding: 0 26px;
    border-right: 1px solid #E1E1E1;
}
.cooperation-list li:last-child a {
    border: 0;
    padding-right: 0;
}
.bottom-links-aside {
    margin-top: 10px;
}
/* #endregion页脚end */