/* 商品列表页样式 */
.goods-list-container {
    margin-top: 20px;
}

.filter-box {
    padding: 10px;
    border: 1px solid #e4e4e4;
    margin-bottom: 20px;
    background-color: #f8f8f8;
}

.filter-item {
    padding: 10px 0;
    border-bottom: 1px dashed #e4e4e4;
}

.filter-item:last-child {
    border-bottom: none;
}

.filter-item .filter-title {
    float: left;
    width: 80px;
    font-weight: bold;
    color: #333;
}

.filter-item .filter-values {
    float: left;
    width: calc(100% - 80px);
}

.filter-item .filter-values a {
    display: inline-block;
    margin-right: 15px;
    margin-bottom: 5px;
    padding: 2px 8px;
    color: #666;
}

.filter-item .filter-values a:hover,
.filter-item .filter-values a.active {
    color: #DD302D;
    background-color: #fff;
}

.sort-bar {
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    border: 1px solid #e4e4e4;
    margin-bottom: 20px;
    background-color: #f8f8f8;
}

.sort-item {
    float: left;
    margin-right: 20px;
    cursor: pointer;
}

.sort-item.active {
    color: #DD302D;
}

.sort-item i {
    margin-left: 5px;
}

.goods-list {
    margin-right: -10px;
}

.goods-item {
    float: left;
    width: 230px;
    margin: 0 10px 20px 0;
    padding: 10px;
    border: 1px solid #e4e4e4;
    background-color: #fff;
    transition: all 0.3s;
}

.goods-item:hover {
    border-color: #DD302D;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.goods-item .goods-img {
    width: 100%;
    height: 208px;
    overflow: hidden;
}

.goods-item .goods-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s;
}

.goods-item:hover .goods-img img {
    transform: scale(1.05);
}

.goods-item .goods-info {
    padding: 10px 0;
}

.goods-item .goods-title {
    height: 40px;
    line-height: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 10px;
}

.goods-item .goods-title a {
    font-size: 14px;
    color: #333;
}

.goods-item .goods-title a:hover {
    color: #DD302D;
}

.goods-item .goods-price {
    color: #DD302D;
    font-size: 18px;
    font-weight: bold;
}

.goods-item .goods-price .original-price {
    color: #999;
    font-size: 12px;
    text-decoration: line-through;
    margin-left: 5px;
    font-weight: normal;
}

.goods-item .goods-sale {
    margin-top: 5px;
    font-size: 12px;
    color: #999;
}

.goods-item .goods-actions {
    margin-top: 10px;
}

.goods-item .goods-actions .btn {
    width: 100%;
    height: 32px;
    line-height: 32px;
    padding: 0;
    font-size: 14px;
}

/* 商品详情页样式 */
.goods-detail {
    margin-top: 20px;
}

.goods-preview {
    width: 400px;
    float: left;
}

.goods-preview .goods-img {
    width: 400px;
    height: 400px;
    border: 1px solid #e4e4e4;
    overflow: hidden;
}

.goods-preview .goods-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.goods-preview .goods-thumbs {
    margin-top: 10px;
}

.goods-preview .goods-thumbs li {
    float: left;
    width: 76px;
    height: 76px;
    margin-right: 5px;
    border: 1px solid #e4e4e4;
    cursor: pointer;
}

.goods-preview .goods-thumbs li.active {
    border-color: #DD302D;
}

.goods-preview .goods-thumbs li img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.goods-info-box {
    width: 780px;
    float: right;
}

.goods-info-box .goods-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
    line-height: 1.5;
}

.goods-info-box .goods-subtitle {
    font-size: 14px;
    color: #DD302D;
    margin-bottom: 20px;
}

.goods-info-box .goods-price-info {
    padding: 10px;
    background-color: #f8f8f8;
    margin-bottom: 20px;
}

.goods-info-box .goods-price {
    font-size: 24px;
    color: #DD302D;
    font-weight: bold;
}

.goods-info-box .original-price {
    color: #999;
    font-size: 14px;
    text-decoration: line-through;
    margin-left: 10px;
}

.goods-info-box .goods-sales {
    margin-top: 10px;
    font-size: 14px;
    color: #666;
}

.goods-info-box .goods-attr {
    margin-bottom: 20px;
}

.goods-info-box .goods-attr-item {
    margin-bottom: 15px;
}

.goods-info-box .goods-attr-title {
    float: left;
    width: 60px;
    font-size: 14px;
    color: #666;
}

.goods-info-box .goods-attr-value {
    float: left;
    width: calc(100% - 60px);
}

.goods-info-box .goods-attr-value a {
    display: inline-block;
    padding: 5px 15px;
    border: 1px solid #ddd;
    margin-right: 10px;
    margin-bottom: 5px;
    color: #333;
    cursor: pointer;
}

.goods-info-box .goods-attr-value a:hover,
.goods-info-box .goods-attr-value a.active {
    border-color: #DD302D;
    color: #DD302D;
}

.goods-info-box .goods-number {
    margin-bottom: 20px;
}

.goods-info-box .goods-number .goods-attr-title {
    line-height: 32px;
}

.goods-info-box .goods-number .number-input {
    width: 120px;
    height: 32px;
    border: 1px solid #ddd;
    float: left;
}

.goods-info-box .goods-number .number-input button {
    width: 32px;
    height: 32px;
    float: left;
    background-color: #f8f8f8;
    border: none;
    font-size: 16px;
    cursor: pointer;
}

.goods-info-box .goods-number .number-input input {
    width: 56px;
    height: 32px;
    float: left;
    border: none;
    border-left: 1px solid #ddd;
    border-right: 1px solid #ddd;
    text-align: center;
    font-size: 14px;
}

.goods-info-box .goods-actions {
    margin-top: 30px;
}

.goods-info-box .goods-actions .btn {
    height: 40px;
    line-height: 40px;
    padding: 0 30px;
    font-size: 16px;
    margin-right: 10px;
}

.goods-info-box .goods-actions .btn-cart {
    background-color: #fff;
    color: #DD302D;
    border: 1px solid #DD302D;
}

.goods-info-box .goods-actions .btn-cart:hover {
    background-color: #fef0f0;
}

.goods-info-box .goods-actions .btn-buy {
    background-color: #DD302D;
    color: #fff;
    border: 1px solid #DD302D;
}

.goods-info-box .goods-actions .btn-buy:hover {
    background-color: #c82333;
}

.goods-detail-tabs {
    margin-top: 30px;
    border: 1px solid #e4e4e4;
}

.goods-detail-tabs .tabs-header {
    height: 40px;
    background-color: #f8f8f8;
    border-bottom: 1px solid #e4e4e4;
}

.goods-detail-tabs .tabs-header li {
    float: left;
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
    font-size: 14px;
    cursor: pointer;
}

.goods-detail-tabs .tabs-header li.active {
    background-color: #fff;
    border-top: 2px solid #DD302D;
    border-right: 1px solid #e4e4e4;
    border-left: 1px solid #e4e4e4;
    margin-bottom: -1px;
    color: #DD302D;
}

.goods-detail-tabs .tabs-content {
    padding: 20px;
}

.goods-detail-tabs .tabs-panel {
    display: none;
}

.goods-detail-tabs .tabs-panel.active {
    display: block;
}

.goods-detail-tabs .goods-attrs table {
    width: 100%;
    border-collapse: collapse;
}

.goods-detail-tabs .goods-attrs table th,
.goods-detail-tabs .goods-attrs table td {
    padding: 10px;
    border: 1px solid #e4e4e4;
    text-align: left;
}

.goods-detail-tabs .goods-attrs table th {
    background-color: #f8f8f8;
    width: 120px;
}

/* 商品评价样式 */
.goods-comments .comment-item {
    padding: 20px 0;
    border-bottom: 1px solid #e4e4e4;
}

.goods-comments .comment-user {
    float: left;
    width: 120px;
    text-align: center;
}

.goods-comments .comment-user .avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 10px;
}

.goods-comments .comment-user .avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.goods-comments .comment-user .username {
    font-size: 14px;
    color: #333;
}

.goods-comments .comment-content {
    float: right;
    width: calc(100% - 120px);
}

.goods-comments .comment-stars {
    margin-bottom: 10px;
}

.goods-comments .comment-stars i {
    color: #DD302D;
    margin-right: 2px;
}

.goods-comments .comment-text {
    line-height: 1.6;
    color: #333;
    margin-bottom: 10px;
}

.goods-comments .comment-images {
    margin-bottom: 10px;
}

.goods-comments .comment-images img {
    width: 80px;
    height: 80px;
    margin-right: 10px;
    object-fit: cover;
}

.goods-comments .comment-time {
    color: #999;
    font-size: 12px;
}

/* 商家后台样式 */
.seller-container {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.seller-header {
    margin-bottom: 30px;
}

.seller-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.seller-subtitle {
    font-size: 14px;
    color: #666;
}

.seller-stats {
    display: flex;
    margin-bottom: 30px;
}

.stat-item {
    flex: 1;
    text-align: center;
    padding: 20px;
    background-color: #f8f9fa;
    margin-right: 20px;
    border-radius: 4px;
}

.stat-item:last-child {
    margin-right: 0;
}

.stat-value {
    font-size: 24px;
    color: #DD302D;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

.seller-table {
    width: 100%;
    border-collapse: collapse;
}

.seller-table th,
.seller-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.seller-table th {
    font-weight: normal;
    color: #999;
}

.seller-table td {
    color: #333;
}

.table-actions a {
    color: #DD302D;
    margin-right: 15px;
}

/* 企业采购页面样式 */
.business-container {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.business-header {
    text-align: center;
    margin-bottom: 30px;
}

.business-title {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
}

.business-subtitle {
    font-size: 16px;
    color: #666;
}

.business-features {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.feature-item {
    width: calc(25% - 20px);
    text-align: center;
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 15px;
}

.feature-title {
    font-size: 18px;
    color: #333;
    margin-bottom: 10px;
}

.feature-desc {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
} 