from django.db import models
from django.utils.translation import gettext_lazy as _

# Create your models here.

class Banner(models.Model):
    """首页轮播图模型"""
    title = models.CharField(_('标题'), max_length=200)
    image = models.ImageField(_('轮播图'), upload_to='banners/')
    url = models.URLField(_('链接地址'), max_length=500)
    sort_order = models.IntegerField(_('排序'), default=0)
    is_active = models.BooleanField(_('是否启用'), default=True)
    created_time = models.DateTimeField(_('创建时间'), auto_now_add=True)
    updated_time = models.DateTimeField(_('更新时间'), auto_now=True)

    class Meta:
        verbose_name = _('轮播图')
        verbose_name_plural = _('轮播图')
        ordering = ['sort_order', 'id']

    def __str__(self):
        return self.title
