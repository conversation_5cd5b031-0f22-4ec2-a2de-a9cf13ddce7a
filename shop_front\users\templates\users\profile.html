{% extends 'base.html' %}
{% load static %}

{% block title %}个人资料 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 修改为红色主题样式 */
    .profile-container {
        max-width: 1200px;
        margin: 40px auto;
        display: flex;
        gap: 20px;
    }

    .sidebar {
        width: 250px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.1);
        padding: 20px;
        border: 1px solid rgba(220, 53, 69, 0.1);
    }

    .user-avatar {
        text-align: center;
        margin-bottom: 20px;
    }

    .user-avatar img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #dc3545;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
    }

    .user-name {
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 10px;
    }

    .sidebar-menu a {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #666;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        transform: translateY(-2px);
    }

    .sidebar-menu a.active {
        background: #dc3545;
        color: white;
    }

    .sidebar-menu i {
        margin-right: 10px;
        width: 20px;
        font-size: 16px;
        text-align: center;
    }

    .main-content {
        flex: 1;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 30px;
        position: relative;
        overflow: hidden;
    }

    .main-content:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }

    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .profile-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .profile-header h2 {
        font-size: 24px;
        color: #dc3545;
        margin-bottom: 0;
        font-weight: 700;
        display: flex;
        align-items: center;
    }

    .profile-header h2 i {
        margin-right: 10px;
        color: #dc3545;
        font-size: 24px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #555;
        font-weight: 500;
    }

    .form-control {
        width: 100%;
        height: 45px;
        padding: 10px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s;
    }

    .form-control:focus {
        border-color: #dc3545;
        outline: none;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    }

    .avatar-upload {
        margin-bottom: 30px;
        text-align: center;
    }

    .avatar-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto 15px;
        border: 3px solid #dc3545;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
    }

    .avatar-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .btn-save {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
    }

    .btn-save:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
    }

    .btn-upload {
        background: linear-gradient(135deg, #28a745 0%, #5cb85c 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-size: 15px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3);
        margin-right: 10px;
    }

    .btn-upload:hover {
        background: linear-gradient(135deg, #218838 0%, #28a745 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(40, 167, 69, 0.4);
    }

    .upload-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.5);
    }

    .upload-modal-content {
        background-color: #fff;
        margin: 5% auto;
        padding: 30px;
        border-radius: 15px;
        width: 90%;
        max-width: 600px;
        position: relative;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .upload-close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        position: absolute;
        right: 20px;
        top: 15px;
        transition: all 0.3s;
    }

    .upload-close:hover {
        color: #dc3545;
    }

    .upload-area {
        border: 2px dashed #ddd;
        border-radius: 15px;
        padding: 40px;
        text-align: center;
        margin: 20px 0;
        transition: all 0.3s;
    }

    .upload-area:hover {
        border-color: #dc3545;
        background: #fff5f5;
    }

    .upload-area.dragover {
        border-color: #dc3545;
        background: #fff5f5;
    }

    .upload-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 10px;
        margin: 20px auto;
        display: none;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .upload-info {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        display: none;
    }

    .upload-buttons {
        text-align: center;
        margin-top: 20px;
    }

    .upload-result {
        margin: 15px 0;
        padding: 12px;
        border-radius: 8px;
        display: none;
    }

    .upload-result.success {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .upload-result.error {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .error-message {
        color: #dc3545;
        margin-bottom: 15px;
        padding: 12px;
        background: #fdecea;
        border-radius: 8px;
    }

    .success-message {
        color: #28a745;
        margin-bottom: 15px;
        padding: 12px;
        background: #d4edda;
        border-radius: 8px;
    }

    /* 响应式设计 */
    @media (max-width: 992px) {
        .profile-container {
            flex-direction: column;
        }

        .sidebar {
            width: 100%;
            margin-bottom: 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="profile-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-avatar">
                <img src="{{ user.avatar.url }}" alt="{{ user.username }}" onerror="this.src='/media/avatars/default.png'">
            </div>
            <div class="user-name">{{ user.username }}</div>
            <ul class="sidebar-menu">
                <li>
                    <a href="{% url 'users:center' %}">
                        <i class="fas fa-home"></i> 个人中心
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:profile' %}" class="active">
                        <i class="fas fa-user"></i> 个人资料
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:orders' %}">
                        <i class="fas fa-shopping-bag"></i> 我的订单
                    </a>
                </li>
                <li>
                    <a href="{% url 'reviews:my_reviews' %}">
                        <i class="fas fa-star"></i> 我的评价
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:address' %}">
                        <i class="fas fa-map-marker-alt"></i> 收货地址
                    </a>
                </li>
                <li>
                    <a href="javascript:void(0)" onclick="confirmLogout()" class="text-danger">
                        <i class="fas fa-sign-out-alt"></i> 注销账户
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:password' %}">
                        <i class="fas fa-lock"></i> 修改密码
                    </a>
                </li>
            </ul>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="profile-header">
                <h2><i class="fas fa-user-edit"></i> 个人资料</h2>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="{% if message.tags == 'success' %}success-message{% else %}error-message{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <div class="avatar-upload">
                    <div class="avatar-preview">
                        <img src="{{ user.avatar.url }}" alt="头像" id="avatar-preview" onerror="this.src='/media/avatars/default.png'">
                    </div>
                    <button type="button" class="btn-upload" id="open-upload">更换头像</button>
                </div>

                <div class="form-group">
                    <label for="{{ form.username.id_for_label }}">用户名</label>
                    {{ form.username }}
                    {% if form.username.errors %}
                        <div class="error-message">{{ form.username.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.email.id_for_label }}">电子邮箱</label>
                    {{ form.email }}
                    {% if form.email.errors %}
                        <div class="error-message">{{ form.email.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.phone.id_for_label }}">手机号码</label>
                    {{ form.phone }}
                    {% if form.phone.errors %}
                        <div class="error-message">{{ form.phone.errors.0 }}</div>
                    {% endif %}
                </div>

                <div class="form-group">
                    <label for="{{ form.bio.id_for_label }}">个人简介</label>
                    {{ form.bio }}
                    {% if form.bio.errors %}
                        <div class="error-message">{{ form.bio.errors.0 }}</div>
                    {% endif %}
                </div>

                <button type="submit" class="btn-save">保存修改</button>
            </form>
        </div>
    </div>
</div>

<!-- 头像上传模态框 -->
<div id="upload-modal" class="upload-modal">
    <div class="upload-modal-content">
        <span class="upload-close">&times;</span>
        <h3 style="color: #dc3545; text-align: center; margin-bottom: 20px;">上传新头像</h3>
        
        <div class="upload-area" id="drop-area">
            <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #dc3545; margin-bottom: 15px;"></i>
            <p>点击选择图片或拖拽图片到此处</p>
            <input type="file" id="file-input" style="display: none;" accept="image/*">
        </div>
        
        <img id="upload-preview" class="upload-preview">
        
        <div id="upload-info" class="upload-info">
            <p><strong>文件名：</strong> <span id="file-name"></span></p>
            <p><strong>文件大小：</strong> <span id="file-size"></span></p>
        </div>
        
        <div id="upload-result" class="upload-result"></div>
        
        <div class="upload-buttons">
            <button id="select-file" class="btn-upload">选择文件</button>
            <button id="upload-file" class="btn-save" disabled>上传头像</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 头像上传相关脚本
    document.addEventListener('DOMContentLoaded', function() {
        const modal = document.getElementById('upload-modal');
        const openBtn = document.getElementById('open-upload');
        const closeBtn = document.querySelector('.upload-close');
        const dropArea = document.getElementById('drop-area');
        const fileInput = document.getElementById('file-input');
        const selectBtn = document.getElementById('select-file');
        const uploadBtn = document.getElementById('upload-file');
        const preview = document.getElementById('upload-preview');
        const info = document.getElementById('upload-info');
        const fileName = document.getElementById('file-name');
        const fileSize = document.getElementById('file-size');
        const result = document.getElementById('upload-result');
        let file = null;

        // 打开模态框
        openBtn.addEventListener('click', function() {
            modal.style.display = 'block';
        });

        // 关闭模态框
        closeBtn.addEventListener('click', function() {
            modal.style.display = 'none';
            resetUpload();
        });

        // 点击外部关闭模态框
        window.addEventListener('click', function(event) {
            if (event.target == modal) {
                modal.style.display = 'none';
                resetUpload();
            }
        });

        // 选择文件按钮
        selectBtn.addEventListener('click', function() {
            fileInput.click();
        });

        // 文件输入变化
        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });

        // 拖拽事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropArea.classList.add('dragover');
        }

        function unhighlight() {
            dropArea.classList.remove('dragover');
        }

        // 处理拖放
        dropArea.addEventListener('drop', function(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        });

        // 处理文件
        function handleFiles(files) {
            if (files.length > 0) {
                file = files[0];
                
                // 验证文件类型
                if (!file.type.match('image.*')) {
                    showResult('请选择图片文件（JPEG, PNG, GIF等）', false);
                    return;
                }
                
                // 验证文件大小
                if (file.size > 5 * 1024 * 1024) {
                    showResult('文件大小不能超过5MB', false);
                    return;
                }
                
                // 显示预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(file);
                
                // 显示文件信息
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                info.style.display = 'block';
                
                // 启用上传按钮
                uploadBtn.disabled = false;
                
                // 隐藏结果
                result.style.display = 'none';
            }
        }

        // 上传文件
        uploadBtn.addEventListener('click', function() {
            if (!file) {
                showResult('请先选择文件', false);
                return;
            }
            
            const formData = new FormData();
            formData.append('avatar', file);
            
            // 获取CSRF令牌
            const csrftoken = getCookie('csrftoken');
            
            // 显示上传中状态
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传中...';
            
            fetch('/users/upload-avatar/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrftoken
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(data.msg, true);
                    // 更新页面上的头像
                    document.getElementById('avatar-preview').src = data.avatar_url;
                    // 3秒后关闭模态框
                    setTimeout(function() {
                        modal.style.display = 'none';
                        resetUpload();
                    }, 3000);
                } else {
                    showResult(data.msg, false);
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = '上传头像';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showResult('上传失败，请重试', false);
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传头像';
            });
        });

        // 显示结果消息
        function showResult(message, success) {
            result.textContent = message;
            result.className = 'upload-result ' + (success ? 'success' : 'error');
            result.style.display = 'block';
        }

        // 重置上传状态
        function resetUpload() {
            file = null;
            fileInput.value = '';
            preview.style.display = 'none';
            info.style.display = 'none';
            result.style.display = 'none';
            uploadBtn.disabled = true;
            uploadBtn.textContent = '上传头像';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(2) + ' KB';
            else return (bytes / 1048576).toFixed(2) + ' MB';
        }

        // 获取Cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // 确认注销账户
        window.confirmLogout = function() {
            if (confirm('确定要注销当前账户吗？注销后需要重新登录。')) {
                // 发送注销请求
                fetch('{% url "account:logout" %}', {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': getCookie('csrftoken'),
                        'Content-Type': 'application/json',
                    },
                })
                .then(response => {
                    if (response.ok) {
                        // 注销成功，跳转到首页
                        alert('注销成功！');
                        window.location.href = '{% url "goods:index" %}';
                    } else {
                        alert('注销失败，请重试');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('注销失败，请重试');
                });
            }
        };
    });
</script>
{% endblock %}