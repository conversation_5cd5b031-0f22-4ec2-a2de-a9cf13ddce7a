

/* 基础设置 */
.container {
    width: 1190px;
    margin: 0 auto;
}
/* #region顶部导航条start */
.topbar {
    height: 30px;
    background-color: #ECECEC;
}
.welcome {
    height: 30px;
    line-height: 30px;
    font-size: 0;
    color: #666666;
}
.welcome span,
.welcome a {
    font-size: 12px;
}
.welcome .hello {
    margin-right: 28px;
}
.welcome .login {
    padding-right: 10px;

    border-right: 1px solid #666666;
}
.welcome .register {
    padding-left: 10px;
}
.topbar-nav .list {
    height: 30px;
    line-height: 30px;
}
.topbar-nav .list li  {
    float: left;
    
    
}
.topbar-nav .list li a {
    padding: 0 15px;
    border-right:1px solid #666666 ;
}
.topbar-nav .list li:first-child a {
    padding-left: 0;
}
.topbar-nav .list li:last-child a {
    padding-right: 0;
    border: 0;
}



/* #endregion顶部导航条end */

/*#region头部start  */
.header {
    height: 120px;
}
.header .search form {
    margin-top: 42px;
    font-size: 0;
}
.header .search input {
    width: 508px;
    height: 34px;
    border: 1px solid #DD302D ;

}
.header .search button {
    width: 80px;
    height: 36px;
    background-color: #DD302D;
    vertical-align: top;
    background-image: url('../images/assets/serch_icon.png');
    background-repeat: no-repeat;
    background-position: 28px 6px;
}
/*#endregion头部end  */

/* #region主导航区start */
.main-nav {
    height: 48px;
    border-bottom: 1px solid #DD302D;
}
.all-types {
    height: 48px;
    line-height: 48px;
    width: 190px;
    background-color: #DD302D;
    font-size: 16px;
    
    color: #FFFFFF;
    text-align: center;
}
.main-nav-list {
    height: 48px;
    line-height: 48px;
}
.main-nav-list li {
    float: left;
    margin: 0 10px;
    font-size: 16px;
    color: #333333;

}
/* #endregion主导航区end */

/* #region主要内容区start */
.main-content {
    margin-top: 10px;
}
.slide-nav {
    width: 190px;
    height: 458px;
    background-color: #F4F4F4;
    position: relative;
}
.main-content .slide-nav li {
    
    color: #333333;
    font-size: 14px;
    /* font-weight: bold; */
    height: 28px;
    padding-left: 16px;
    
}
.main-content .slide-nav li:hover {
    background-color: #DD302D;
    color: white;
}
.main-content .slide-nav li:hover>a {
    color: white;
}
.second-menu {
    width: 680px;
    height: 458px;
    background-color:white;
    position: absolute;
    top: 0;
    left: 190px;padding-left: 20px;
    display: none;
}
.main-content .slide-nav li:hover .second-menu {
    display: block;
}
.second-menu dl {
    height: 36px;
    line-height: 36px;
}
.second-menu dl:first-child {
    margin-top: 10px;
}
.second-menu dt {
    float: left;
    width: 70px;
    font-weight: bold;
    margin-right: 10px;
}
.second-menu dd {
    float: left;
    
}.second-menu dd a {
    padding: 0 10px;
    border-left: 1px solid #666666;
}
.banner {
    width: 690px;
    height: 458px;
    margin: 0 10px;

}

.main-content .slide-other {
    width: 290px;
    height: 458px;
}
.slide-other .message {
    width: 260px;
    height: 156px;
    border: 1px solid #D9D9D9;
    padding: 0 14px;
}

.slide-other .message .title {
    height: 38px;
    line-height: 38px;
    border-bottom: 1px solid #D9D9D9;
}
.slide-other .message .title span {
    font-size: 14px;
}
.slide-other .message .title a {
    font-size: 12px;
    color: #999999 ;
}
.slide-other .message .message-list li {
    height: 26px;
    line-height: 26px;
    font-size: 12px;
    color: #666666;
}




.slide-other .other-nav {
    width: 290px;
    height: 290px;
    margin-top: 10px;
    overflow: hidden;
}
.other-nav-list:first-child {
    margin-top: 16px;
}
.other-nav-list:nth-child(2) {
    margin: 17px 0;
}
.other-nav-list li {
    width: 48px;
    height: 70px;
    float: left;
    margin: 0 11px;
    text-align: center;
    cursor: pointer;
}
.other-nav-list li:first-child {
    margin-left: 16px;
}
.other-nav-list .picture {
    width: 48px;
    height: 48px;
    background-image: url('../images/assets/精灵图-侧边功能.png');
}
.other-nav-list:nth-child(1) li:nth-child(1) .picture {
    background-position: 0 0;
}
.other-nav-list:nth-child(1) li:nth-child(2) .picture {
    background-position: -48px 0;
}
.other-nav-list:nth-child(1) li:nth-child(3) .picture {
    background-position: -96px 0;
}
.other-nav-list:nth-child(1) li:nth-child(4) .picture {
    background-position: -144px 0;
}


.other-nav-list:nth-child(2) li:nth-child(1) .picture {
    background-position: 0 -48px;
}
.other-nav-list:nth-child(2) li:nth-child(2) .picture {
    background-position: -48px -48px;
}
.other-nav-list:nth-child(2) li:nth-child(3) .picture {
    background-position: -96px -48px;
}
.other-nav-list:nth-child(2) li:nth-child(4) .picture {
    background-position: -144px -48px;
}


.other-nav-list:nth-child(3) li:nth-child(1) .picture {
    background-position: 0 -96px;
}
.other-nav-list:nth-child(3) li:nth-child(2) .picture {
    background-position: -48px -96px;
}
.other-nav-list:nth-child(3) li:nth-child(3) .picture {
    background-position: -96px -96px;
}
.other-nav-list:nth-child(3) li:nth-child(4) .picture {
    background-position: -144px -96px;
}
/* #endregion主要内容区end */

/* #region秒杀start */
.seckill img {
    float: left;
    margin-right: 11px;
    margin-top: 10px;
    cursor: pointer;
}
.seckill img:last-child {
    margin-right: 0;
}


/* #endregion秒杀end */


/* #region楼层start */
.floor {
    margin-top: 48px;
}
.floor-nav {
    height: 30px;
    line-height: 30px;
    padding-bottom: 4px;
    border-bottom: 2px solid #DD302D;
}
.floor-nav .floor-name {
    font-size: 20px;
    color: #000000;
}
.floor-nav .floor-nav-list li {
    float: left;
    color: #666666;
}
.floor-nav .floor-nav-list li a {
    font-size: 16px;
    padding: 0 10px;
    border-right: 1px solid #666666 ;
}
.floor-nav .floor-nav-list li:first-child a {
    padding-left: 0;
}
.floor-nav .floor-nav-list li:last-child a {
    padding-right: 0;
    border: 0;
}
.floor-info .info-item {
    float: left;
}
.floor-info .item1 {
    width: 190px;
    height: 392px;
    background-color: #F4F4F4 ;
    padding: 20px;
}
.item1-list {
    margin-bottom: 29px;
}
.item1-list li {
    font-size: 16px;
    width: 90px;
    height: 22px;
    padding-bottom: 3.5px;
    border-bottom: 1px solid #D9D9D9;
    text-align: center;
}
.item1-list li:nth-child(2n-1) {
    float: left;
}
.item1-list li:nth-child(2n) {
    float: right;
}
.item1-list li:nth-child(3),
.item1-list li:nth-child(4) {
    margin: 14.4px 0;
}


.floor-info .item2 {
    width: 340px;
    height: 432px;
    cursor: pointer;
}
.floor-info .item3 {
    width: 206px;
    height: 432px;
    border-right: 1px solid #E2E2E2;
}
.floor-info .item3 .item3-top {
    border-bottom: 1px solid #E2E2E2;
    cursor: pointer;
}
.floor-info .item3 .item3-bottom {
    cursor: pointer;
    border-bottom: 1px solid #E2E2E2;
    
}
.floor-info .item4 {
    width: 206px;
    height: 431px;
    border-right: 1px solid #E2E2E2;
    border-bottom: 1px solid #E2E2E2;
    cursor: pointer;
}
.floor-info .item5 {
    width: 206px;
    height: 432px;
}
.floor-info .item5 .item5-top {
    border-bottom: 1px solid #E2E2E2;
    cursor: pointer;
}
.floor-info .item5 .item5-bottom {
    border-bottom: 1px solid #E2E2E2;
    cursor: pointer;
}

/* #endregion楼层end */

/* #region页脚start */
.footer {
    height: 440px;
    background-color: #483E3E;
    margin-top: 48px;
}
.serve-list {
    margin-top: 48px;
    margin-right: 10px;
}
.top-links ul:last-child {
    margin-right: 0;
}
.top-links li a {
    font-size: 14px;
    color: #FFFFFF;
    
}

.serve-list {
    float: left;
}
.top-links ul:nth-child(1),
.top-links ul:nth-child(5) {
    width: 190px;
    height: 176px;
} 
.top-links ul:nth-child(2),
.top-links ul:nth-child(3) {
    width: 190px;
    height: 128px;
}
.top-links ul:nth-child(4),
.top-links ul:nth-child(6) {
    width: 190px;
    height: 152px;
}
.line {
    width: 1190px;
    height: 1px;
    background-color: #584D4D;
    margin-top: 22px;
}

.bottom-links-list {
    width: 957px;
    margin: 0 auto;
}
.cooperation-list li {
    float: left;
    text-align: center;
    margin-top: 47px;
}
.cooperation-list li a {
    font-size: 12px;
    color: #FFFFFF;
    padding: 0 26px;
    border-right: 1px solid #D9D9D9;
}
.cooperation-list li:first-child a {
    padding-left: 0;
}
.cooperation-list li:last-child a {
    padding-right: 0;
    border: 0;
}
.bottom-links-aside {
    width: 190px;
    height: 18px;
    margin: 10px auto;
    text-align: center;
    line-height: 18px;
}
.bottom-links-aside span {
    font-size: 12px;
    color: #FFFFFF;
    
    
}
/* #endregion页脚end */