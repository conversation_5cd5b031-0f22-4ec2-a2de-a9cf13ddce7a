# Generated by Django 4.2.22 on 2025-06-09 12:27

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AdminLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.Char<PERSON>ield(max_length=50, verbose_name='操作类型')),
                ('content_type', models.CharField(blank=True, max_length=50, verbose_name='操作对象类型')),
                ('object_id', models.CharField(blank=True, max_length=50, verbose_name='操作对象ID')),
                ('object_repr', models.CharField(blank=True, max_length=200, verbose_name='操作对象描述')),
                ('change_message', models.TextField(blank=True, verbose_name='变更信息')),
                ('ip_address', models.GenericIPAddress<PERSON>ield(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='操作时间')),
            ],
            options={
                'verbose_name': '管理员日志',
                'verbose_name_plural': '管理员日志',
                'ordering': ['-created_time'],
            },
        ),
    ]
