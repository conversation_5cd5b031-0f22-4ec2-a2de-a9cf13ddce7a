from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from goods.models import Product
from order.models import Order, OrderItem

# Create your models here.

class Review(models.Model):
    """商品评价模型"""
    SCORE_CHOICES = (
        (1, _('很差')),
        (2, _('差')),
        (3, _('一般')),
        (4, _('好')),
        (5, _('很好')),
    )

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name=_('用户'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='reviews', verbose_name=_('商品'))
    order_item = models.OneToOneField(OrderItem, on_delete=models.SET_NULL, null=True, blank=True, 
                                    related_name='review', verbose_name=_('订单项'))
    content = models.TextField(verbose_name=_('评价内容'))
    score = models.IntegerField(choices=SCORE_CHOICES, default=5, verbose_name=_('评分'))
    is_anonymous = models.BooleanField(default=False, verbose_name=_('是否匿名'))
    is_verified = models.BooleanField(default=False, verbose_name=_('是否已验证'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('创建时间'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('更新时间'))

    class Meta:
        verbose_name = _('商品评价')
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'product']),
            models.Index(fields=['product', 'score']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_verified']),
        ]

    def __str__(self):
        return f"{self.user.username}对{self.product.name}的评价"

    @property
    def images_list(self):
        """获取评价图片列表"""
        return self.review_images.all()

class ReviewImage(models.Model):
    """评价图片模型"""
    review = models.ForeignKey(Review, on_delete=models.CASCADE, related_name='review_images', verbose_name=_('评价'))
    image = models.ImageField(upload_to='review_images/%Y/%m/', verbose_name=_('图片'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('创建时间'))

    class Meta:
        verbose_name = _('评价图片')
        verbose_name_plural = verbose_name
        ordering = ['created_at']
        indexes = [
            models.Index(fields=['review']),
        ]

    def __str__(self):
        return f"{self.review.user.username}的评价图片"
