{% extends 'base.html' %}
{% load static %}

{% block title %}评价商品 - {{ product.name }} - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    .review-container {
        max-width: 800px;
        margin: 40px auto;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 30px;
        position: relative;
        overflow: hidden;
    }

    .review-container:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }

    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .review-header {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .review-header h2 {
        font-size: 24px;
        color: #dc3545;
        margin-bottom: 0;
        font-weight: 700;
        display: flex;
        align-items: center;
    }

    .review-header h2 i {
        margin-right: 10px;
        color: #dc3545;
        font-size: 24px;
    }

    .product-info {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-bottom: 30px;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 8px;
    }

    .product-image {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 8px;
        border: 1px solid #eee;
    }

    .product-details h3 {
        font-size: 18px;
        margin-bottom: 10px;
    }

    .product-price {
        color: #dc3545;
        font-weight: 600;
        font-size: 16px;
    }

    .form-group {
        margin-bottom: 25px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #555;
        font-weight: 500;
    }

    .rating-input {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .rating-input input {
        display: none;
    }

    .rating-input label {
        cursor: pointer;
        font-size: 30px;
        color: #ddd;
        margin-right: 10px;
        transition: color 0.3s;
    }

    .rating-input label:hover,
    .rating-input label:hover ~ label,
    .rating-input input:checked ~ label {
        color: #ffc107;
    }

    .rating-text {
        margin-top: 5px;
        color: #666;
        font-size: 14px;
    }

    textarea {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s;
        resize: vertical;
        min-height: 120px;
    }

    textarea:focus {
        border-color: #dc3545;
        outline: none;
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
    }

    .image-upload {
        margin-bottom: 20px;
    }

    .image-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 10px;
    }

    .preview-item {
        position: relative;
        width: 100px;
        height: 100px;
    }

    .preview-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        border: 1px solid #eee;
    }

    .remove-image {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #dc3545;
        color: white;
        border: none;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }

    .checkbox-group {
        display: flex;
        align-items: center;
    }

    .checkbox-group input {
        margin-right: 10px;
    }

    .form-actions {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-top: 30px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
    }

    .btn-secondary {
        background: #f8f9fa;
        color: #6c757d;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-secondary:hover {
        background: #e9ecef;
        transform: translateY(-2px);
    }

    .upload-tip {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="review-container">
        <div class="review-header">
            <h2><i class="fas fa-star"></i> 评价商品</h2>
        </div>

        <div class="product-info">
            <img src="{{ product.main_image.url }}" alt="{{ product.name }}" class="product-image" onerror="this.src='/static/images/default-product.jpg'">
            <div class="product-details">
                <h3>{{ product.name }}</h3>
                <div class="product-price">¥{{ product.price }}</div>
            </div>
        </div>

        <form id="reviewForm" method="post" enctype="multipart/form-data" action="{% url 'reviews:add' product.id %}">
            {% csrf_token %}
            
            <div class="form-group">
                <label>商品评分</label>
                <div class="rating-input">
                    <input type="radio" name="score" value="5" id="star5" required>
                    <label for="star5"><i class="fas fa-star"></i></label>
                    <input type="radio" name="score" value="4" id="star4">
                    <label for="star4"><i class="fas fa-star"></i></label>
                    <input type="radio" name="score" value="3" id="star3">
                    <label for="star3"><i class="fas fa-star"></i></label>
                    <input type="radio" name="score" value="2" id="star2">
                    <label for="star2"><i class="fas fa-star"></i></label>
                    <input type="radio" name="score" value="1" id="star1">
                    <label for="star1"><i class="fas fa-star"></i></label>
                </div>
                <div class="rating-text">
                    <span id="ratingText">请选择评分</span>
                </div>
            </div>

            <div class="form-group">
                <label for="content">评价内容</label>
                <textarea id="content" name="content" rows="5" placeholder="请分享您的使用体验..." required></textarea>
            </div>

            <div class="form-group">
                <label for="images">上传图片（可选）</label>
                <input type="file" id="images" name="images" accept="image/*" multiple>
                <div class="upload-tip">最多可上传5张图片</div>
                <div class="image-preview" id="imagePreview"></div>
            </div>

            <div class="form-group checkbox-group">
                <input type="checkbox" id="is_anonymous" name="is_anonymous">
                <label for="is_anonymous">匿名评价</label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-paper-plane"></i> 提交评价
                </button>
                <a href="{% url 'users:orders' %}" class="btn-secondary">
                    <i class="fas fa-times"></i> 取消
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 评分星级交互
        const ratingInputs = document.querySelectorAll('.rating-input input[type="radio"]');
        const ratingText = document.getElementById('ratingText');

        const ratingTexts = {
            '1': '很差',
            '2': '差',
            '3': '一般',
            '4': '好',
            '5': '很好'
        };

        ratingInputs.forEach(input => {
            input.addEventListener('change', function() {
                updateRatingText();
            });
        });

        function updateRatingText() {
            const checkedInput = document.querySelector('.rating-input input[type="radio"]:checked');
            if (checkedInput && ratingText) {
                ratingText.textContent = ratingTexts[checkedInput.value];
            } else if (ratingText) {
                ratingText.textContent = '请选择评分';
            }
        }

        // 图片预览
        const imageInput = document.getElementById('images');
        const imagePreview = document.getElementById('imagePreview');

        imageInput.addEventListener('change', function() {
            imagePreview.innerHTML = '';
            const files = this.files;
            
            // 限制最多上传5张图片
            if (files.length > 5) {
                alert('最多只能上传5张图片');
                this.value = '';
                return;
            }

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                if (!file.type.match('image.*')) {
                    continue;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewItem = document.createElement('div');
                    previewItem.className = 'preview-item';
                    
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.alt = '预览图片';
                    
                    previewItem.appendChild(img);
                    imagePreview.appendChild(previewItem);
                };
                
                reader.readAsDataURL(file);
            }
        });
    });
</script>
{% endblock %} 