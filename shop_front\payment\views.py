from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import HttpResponse, JsonResponse
from django.conf import settings
from django.contrib import messages
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.db import transaction
import json
from order.models import Order
from goods.models import Product
from .alipay import AliPayAPI

def update_product_stock_and_sales(order):
    """
    支付成功后更新商品库存和销量
    """
    try:
        with transaction.atomic():
            for item in order.items.all():
                product = item.product
                # 减少库存
                if product.stock >= item.quantity:
                    product.stock -= item.quantity
                    # 增加销量
                    product.sales += item.quantity
                    product.save()
                    print(f"商品 {product.name} 库存更新: -{item.quantity}, 销量更新: +{item.quantity}")
                else:
                    print(f"警告: 商品 {product.name} 库存不足，当前库存: {product.stock}, 需要: {item.quantity}")
                    # 即使库存不足也要更新销量，但库存设为0
                    product.sales += item.quantity
                    product.stock = 0
                    product.save()
    except Exception as e:
        print(f"更新商品库存和销量失败: {e}")

@login_required
def payment_page(request, order_number):
    """支付页面"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)

    # 检查订单状态
    if order.status != 'pending':
        messages.error(request, '该订单不是待支付状态')
        return redirect('order:detail', order_number=order.order_number)

    return render(request, 'payment/pay.html', {'order': order})

@login_required
def payment_page_simple(request, order_number):
    """简化支付页面"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)

    # 检查订单状态
    if order.status != 'pending':
        messages.error(request, '该订单不是待支付状态')
        return redirect('order:detail', order_number=order.order_number)

    return render(request, 'payment/pay_simple.html', {'order': order})

@login_required
def payment_success(request, order_number):
    """支付成功页面"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)

    # 检查订单是否已支付
    if order.status != 'paid':
        messages.error(request, '订单尚未支付成功')
        return redirect('order:detail', order_number=order.order_number)

    return render(request, 'payment/success.html', {'order': order})

def test_password_page(request):
    """测试支付密码页面"""
    return render(request, 'payment/test_password.html')

def simple_test_page(request):
    """简化测试页面"""
    # 获取一个测试订单
    try:
        order = Order.objects.filter(user=request.user).first() if request.user.is_authenticated else None
    except:
        order = None
    return render(request, 'payment/simple_test.html', {'order': order})

def debug_test_page(request):
    """调试测试页面"""
    return render(request, 'payment/debug_test.html')

def get_alipay():
    """获取支付宝接口"""
    try:
        alipay = AliPayAPI(
            appid=getattr(settings, 'ALIPAY_APPID', 'your-app-id'),
            app_notify_url=None,
            app_private_key_string=getattr(settings, 'APP_PRIVATE_KEY', ''),
            alipay_public_key_string=getattr(settings, 'ALIPAY_PUBLIC_KEY', ''),
            sign_type="RSA2",
            debug=getattr(settings, 'DEBUG', True)
        )
        return alipay
    except Exception as e:
        print(f"支付宝配置错误: {e}")
        return None

@login_required
def alipay_pay(request, order_number):
    """支付宝支付"""
    order = get_object_or_404(Order, order_number=order_number, user=request.user)

    # 检查订单状态
    if order.status != 'pending':
        messages.error(request, '该订单不是待支付状态')
        return redirect('order:detail', order_number=order.order_number)

    # 获取支付宝接口
    alipay = get_alipay()
    if not alipay:
        messages.error(request, '支付服务暂时不可用，请稍后再试')
        return redirect('payment:pay', order_number=order.order_number)

    try:
        # 生成支付链接
        order_string = alipay.api_alipay_trade_page_pay(
            out_trade_no=order.order_number,
            total_amount=str(order.pay_amount),
            subject=f"MARS BUY订单-{order.order_number}",
            return_url=getattr(settings, 'ALIPAY_RETURN_URL', 'http://localhost:8001/payment/alipay/return/'),
            notify_url=getattr(settings, 'ALIPAY_NOTIFY_URL', 'http://localhost:8001/payment/alipay/notify/')
        )

        # 跳转到支付宝支付页面
        alipay_url = getattr(settings, 'ALIPAY_GATEWAY', 'https://openapi.alipaydev.com/gateway.do')
        pay_url = f"{alipay_url}?{order_string}"
        return redirect(pay_url)

    except Exception as e:
        print(f"支付宝支付错误: {e}")
        messages.error(request, '支付链接生成失败，请重试')
        return redirect('payment:pay', order_number=order.order_number)

def alipay_return(request):
    """支付宝同步回调"""
    try:
        data = request.GET.dict()
        if not data or 'sign' not in data:
            messages.error(request, '支付验证失败')
            return redirect('home:index')

        signature = data.pop("sign")

        alipay = get_alipay()
        if not alipay:
            messages.error(request, '支付验证服务不可用')
            return redirect('home:index')

        success = alipay.verify(data, signature)

        if success and data.get("trade_status") in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            order_number = data.get("out_trade_no")
            if order_number:
                try:
                    order = Order.objects.get(order_number=order_number)
                    if order.status == 'pending':
                        order.status = 'paid'
                        order.paid_time = timezone.now()
                        order.save()
                        # 更新商品库存和销量
                        update_product_stock_and_sales(order)
                        messages.success(request, '支付成功！')
                    return redirect('payment:success', order_number=order.order_number)
                except Order.DoesNotExist:
                    messages.error(request, '订单不存在')
                    return redirect('home:index')

        messages.error(request, '支付验证失败')
        return redirect('home:index')

    except Exception as e:
        print(f"支付宝回调处理错误: {e}")
        messages.error(request, '支付处理异常')
        return redirect('home:index')

def alipay_notify(request):
    """支付宝异步通知"""
    try:
        data = request.POST.dict()
        if not data or 'sign' not in data:
            return HttpResponse("fail")

        signature = data.pop("sign")

        alipay = get_alipay()
        if not alipay:
            return HttpResponse("fail")

        success = alipay.verify(data, signature)

        if success and data.get("trade_status") in ("TRADE_SUCCESS", "TRADE_FINISHED"):
            order_number = data.get("out_trade_no")
            if order_number:
                try:
                    order = Order.objects.get(order_number=order_number)
                    if order.status == 'pending':
                        order.status = 'paid'
                        order.paid_time = timezone.now()
                        order.save()
                        # 更新商品库存和销量
                        update_product_stock_and_sales(order)
                        print(f"订单 {order_number} 支付成功，库存和销量已更新")
                    return HttpResponse("success")
                except Order.DoesNotExist:
                    print(f"订单 {order_number} 不存在")
                    return HttpResponse("fail")

        return HttpResponse("fail")

    except Exception as e:
        print(f"支付宝异步通知处理错误: {e}")
        return HttpResponse("fail")

@csrf_exempt
def verify_payment_password(request):
    """验证支付密码"""
    print(f"=== 密码验证请求 ===")
    print(f"请求方法: {request.method}")
    print(f"请求体: {request.body}")
    print(f"用户: {request.user}")

    if request.method != 'POST':
        print("错误: 请求方法不是POST")
        return JsonResponse({'success': False, 'msg': '请求方法错误'})

    try:
        data = json.loads(request.body)
        password = data.get('password', '')
        order_number = data.get('order_number', '')

        print(f"解析的密码: {password}")
        print(f"解析的订单号: {order_number}")

        # 验证订单（如果不是测试订单且用户已登录）
        if order_number != 'TEST123456' and request.user.is_authenticated:
            try:
                order = Order.objects.get(order_number=order_number, user=request.user)
                print(f"找到订单: {order.id}")
            except Order.DoesNotExist:
                print("错误: 订单不存在")
                return JsonResponse({'success': False, 'msg': '订单不存在'})

        # 验证支付密码（这里使用简单验证，实际应该从用户模型中获取）
        valid_passwords = ['123456', '888888', '666666', '000000']

        if password in valid_passwords:
            print("密码验证成功!")
            return JsonResponse({
                'success': True,
                'msg': '密码验证成功'
            })
        else:
            print(f"密码验证失败: {password} 不在有效密码列表中")
            return JsonResponse({
                'success': False,
                'msg': '支付密码错误'
            })

    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return JsonResponse({'success': False, 'msg': '数据格式错误'})
    except Exception as e:
        print(f"支付密码验证异常: {e}")
        import traceback
        traceback.print_exc()
        return JsonResponse({'success': False, 'msg': '验证失败'})

def simulate_payment_success(request, order_number):
    """模拟支付成功（用于测试）"""
    try:
        print(f"=== 模拟支付成功 ===")
        print(f"订单号: {order_number}")
        print(f"用户: {request.user}")

        # 如果是测试订单，直接跳转到成功页面
        if order_number == 'TEST123456':
            print("测试订单，直接显示成功页面")
            # 创建一个虚拟订单对象用于显示
            class MockOrder:
                order_number = 'TEST123456'
                pay_amount = '899.00'
                paid_time = timezone.now()
                status = 'paid'

            mock_order = MockOrder()
            return render(request, 'payment/success.html', {'order': mock_order})

        # 真实订单处理
        if request.user.is_authenticated:
            try:
                order = Order.objects.get(order_number=order_number, user=request.user)
                print(f"找到订单: {order.id}")

                if order.status == 'pending':
                    order.status = 'paid'
                    order.paid_time = timezone.now()
                    order.save()
                    # 更新商品库存和销量
                    update_product_stock_and_sales(order)
                    print("订单状态已更新为已支付，库存和销量已更新")
                    messages.success(request, '支付成功！')

                return redirect('payment:success', order_number=order.order_number)

            except Order.DoesNotExist:
                print("订单不存在")
                messages.error(request, '订单不存在')
                return redirect('home:index')
        else:
            print("用户未登录")
            messages.error(request, '请先登录')
            return redirect('users:login')

    except Exception as e:
        print(f"模拟支付成功错误: {e}")
        import traceback
        traceback.print_exc()
        messages.error(request, '支付处理失败')
        return redirect('home:index')
