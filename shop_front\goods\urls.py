from django.urls import path
from . import views

app_name = 'goods'

urlpatterns = [
    path('', views.index, name='index'),
    path('products/', views.product_list, name='list'),
    path('product/<int:pk>/', views.product_detail, name='detail'),
    path('category/<int:category_id>/', views.category_products, name='category'),
    path('hot/', views.hot_products, name='hot'),
    path('new/', views.new_products, name='new'),
    path('search/', views.search_products, name='search'),
    path('product/add/', views.add_product, name='add_product'),
    path('product/<int:product_id>/edit/', views.edit_product, name='edit_product'),
    path('api/products/<int:product_id>/', views.get_product, name='get_product'),
    path('api/products/<int:product_id>/update/', views.update_product, name='update_product'),
    path('api/products/<int:product_id>/delete/', views.delete_product, name='delete_product'),
    path('api/product-images/<int:image_id>/delete/', views.delete_product_image, name='delete_product_image'),

    # 实时更新API
    path('api/check_updates/', views.check_product_updates, name='check_updates'),
    path('api/latest_products/', views.get_latest_products, name='latest_products'),
]
