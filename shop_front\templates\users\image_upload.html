{% extends 'base.html' %}

{% block title %}图片上传 - MARS BUY{% endblock %}

{% load static %}

{% block extra_css %}
<style>
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .upload-container {
        max-width: 800px;
        margin: 40px auto;
        padding: 0 20px;
    }

    .upload-card {
        background: rgba(255,255,255,0.95);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        backdrop-filter: blur(10px);
    }

    .upload-header {
        text-align: center;
        margin-bottom: 40px;
        padding-bottom: 20px;
        border-bottom: 2px solid #f0f0f0;
    }

    .upload-title {
        font-size: 32px;
        font-weight: 700;
        color: #333;
        margin-bottom: 10px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .upload-section {
        margin: 30px 0;
        padding: 25px;
        border: 2px dashed #ddd;
        border-radius: 15px;
        background: #fafafa;
        transition: all 0.3s ease;
    }

    .upload-section:hover {
        border-color: #dc3545;
        background: #fff;
    }

    .upload-section.dragover {
        border-color: #dc3545;
        background: #fff5f5;
    }

    .upload-input {
        width: 100%;
        padding: 15px;
        border: none;
        background: transparent;
        font-size: 16px;
        cursor: pointer;
    }

    .upload-input::-webkit-file-upload-button {
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 25px;
        cursor: pointer;
        margin-right: 15px;
        font-weight: 600;
    }

    .upload-input::-webkit-file-upload-button:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
    }

    .upload-types {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin: 30px 0;
    }

    .upload-type {
        background: white;
        border: 2px solid #e0e0e0;
        border-radius: 15px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .upload-type:hover {
        border-color: #dc3545;
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(220,53,69,0.15);
    }

    .upload-type.selected {
        border-color: #dc3545;
        background: linear-gradient(135deg, rgba(220,53,69,0.05) 0%, rgba(231,76,60,0.05) 100%);
    }

    .upload-type-icon {
        font-size: 48px;
        margin-bottom: 15px;
        color: #dc3545;
    }

    .preview-section {
        margin: 30px 0;
        text-align: center;
        display: none;
    }

    .preview-image {
        max-width: 400px;
        max-height: 400px;
        border: 2px solid #ddd;
        border-radius: 15px;
        padding: 10px;
        background: white;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    }

    .file-info {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 15px;
        margin: 20px 0;
        text-align: left;
    }

    .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 10px;
        margin: 10px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
        color: white;
        text-decoration: none;
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
        box-shadow: 0 8px 25px rgba(108,117,125,0.3);
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-3px);
        color: white;
        text-decoration: none;
    }

    .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .result {
        margin: 20px 0;
        padding: 15px;
        border-radius: 15px;
        border-left: 5px solid #dc3545;
    }

    .success {
        background: #d4edda;
        color: #155724;
        border-left-color: #28a745;
    }

    .error {
        background: #f8d7da;
        color: #721c24;
        border-left-color: #dc3545;
    }

    .info {
        background: #d1ecf1;
        color: #0c5460;
        border-left-color: #17a2b8;
    }

    .instructions {
        background: #e7f3ff;
        padding: 25px;
        border-radius: 15px;
        margin: 30px 0;
        border-left: 5px solid #007bff;
    }

    .instructions h4 {
        margin-top: 0;
        color: #007bff;
    }

    .instructions ul {
        margin: 15px 0;
        padding-left: 25px;
    }

    .instructions li {
        margin: 8px 0;
    }

    @media (max-width: 768px) {
        .upload-card {
            padding: 20px;
            margin: 20px 10px;
        }
        
        .upload-types {
            grid-template-columns: 1fr;
        }
        
        .preview-image {
            max-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="upload-container">
    <div class="upload-card">
        <div class="upload-header">
            <h1 class="upload-title">
                <i class="fas fa-cloud-upload-alt"></i>
                图片上传中心
            </h1>
            <p>支持从电脑任意位置上传图片文件，系统会自动处理和优化</p>
        </div>

        <!-- 上传类型选择 -->
        <div class="upload-types">
            <div class="upload-type selected" data-type="avatar">
                <div class="upload-type-icon">
                    <i class="fas fa-user-circle"></i>
                </div>
                <div>头像上传</div>
                <small>用于个人资料头像</small>
            </div>
            <div class="upload-type" data-type="general">
                <div class="upload-type-icon">
                    <i class="fas fa-images"></i>
                </div>
                <div>通用图片</div>
                <small>用于一般用途</small>
            </div>
            <div class="upload-type" data-type="review">
                <div class="upload-type-icon">
                    <i class="fas fa-star"></i>
                </div>
                <div>评价图片</div>
                <small>用于商品评价</small>
            </div>
            <div class="upload-type" data-type="product">
                <div class="upload-type-icon">
                    <i class="fas fa-box"></i>
                </div>
                <div>商品图片</div>
                <small>用于商品展示</small>
            </div>
        </div>

        <!-- 文件上传区域 -->
        <div class="upload-section" id="uploadSection">
            <div style="text-align: center;">
                <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #dc3545; margin-bottom: 20px;"></i>
                <h4>选择图片文件或拖拽到此处</h4>
                <p>支持 JPG, PNG, GIF, BMP, WEBP 格式，最大 5MB</p>
                <input type="file" id="imageFile" class="upload-input" accept="image/*" multiple>
            </div>
        </div>

        <!-- 图片预览 -->
        <div id="previewSection" class="preview-section">
            <h4>图片预览</h4>
            <img id="previewImage" class="preview-image" src="" alt="预览图片">
            <div id="fileInfo" class="file-info"></div>
        </div>

        <!-- 操作按钮 -->
        <div style="text-align: center; margin: 30px 0;">
            <button id="uploadBtn" class="btn btn-primary" disabled>
                <i class="fas fa-upload"></i>
                上传图片
            </button>
            <button id="clearBtn" class="btn btn-secondary">
                <i class="fas fa-trash"></i>
                清除选择
            </button>
        </div>

        <!-- 结果显示 -->
        <div id="result" class="result info">
            请选择上传类型和图片文件
        </div>

        <!-- 使用说明 -->
        <div class="instructions">
            <h4><i class="fas fa-info-circle"></i> 使用说明</h4>
            <ul>
                <li>✅ 支持从电脑任意位置选择图片文件</li>
                <li>✅ 支持拖拽上传，直接将图片拖到上传区域</li>
                <li>✅ 支持多种图片格式：JPG, PNG, GIF, BMP, WEBP</li>
                <li>✅ 自动压缩大图片，节省存储空间</li>
                <li>✅ 自动生成唯一文件名，避免冲突</li>
                <li>✅ 实时预览，上传前可查看图片效果</li>
                <li>⚠️ 单个文件大小限制：5MB</li>
                <li>⚠️ 请确保图片内容符合网站规范</li>
            </ul>
        </div>
    </div>
</div>

{% csrf_token %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadTypes = document.querySelectorAll('.upload-type');
    const uploadSection = document.getElementById('uploadSection');
    const imageFile = document.getElementById('imageFile');
    const previewSection = document.getElementById('previewSection');
    const previewImage = document.getElementById('previewImage');
    const fileInfo = document.getElementById('fileInfo');
    const uploadBtn = document.getElementById('uploadBtn');
    const clearBtn = document.getElementById('clearBtn');
    const result = document.getElementById('result');
    
    let selectedType = 'avatar';
    let selectedFile = null;

    // 上传类型选择
    uploadTypes.forEach(type => {
        type.addEventListener('click', function() {
            uploadTypes.forEach(t => t.classList.remove('selected'));
            this.classList.add('selected');
            selectedType = this.dataset.type;
            showResult(`已选择：${this.textContent.trim()}`, 'info');
        });
    });

    // 文件选择
    imageFile.addEventListener('change', function() {
        if (this.files && this.files[0]) {
            handleFileSelect(this.files[0]);
        }
    });

    // 拖拽上传
    uploadSection.addEventListener('dragover', function(e) {
        e.preventDefault();
        this.classList.add('dragover');
    });

    uploadSection.addEventListener('dragleave', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
    });

    uploadSection.addEventListener('drop', function(e) {
        e.preventDefault();
        this.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });

    // 处理文件选择
    function handleFileSelect(file) {
        selectedFile = file;
        
        // 验证文件
        if (!validateFile(file)) {
            return;
        }
        
        // 显示预览
        showPreview(file);
        
        // 启用上传按钮
        uploadBtn.disabled = false;
    }

    // 验证文件
    function validateFile(file) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        if (!allowedTypes.includes(file.type)) {
            showResult('❌ 不支持的文件格式！请选择图片文件。', 'error');
            return false;
        }
        
        if (file.size > maxSize) {
            showResult('❌ 文件太大！请选择小于5MB的图片文件。', 'error');
            return false;
        }
        
        return true;
    }

    // 显示预览
    function showPreview(file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImage.src = e.target.result;
            previewSection.style.display = 'block';
            
            // 显示文件信息
            const sizeInMB = (file.size / (1024 * 1024)).toFixed(2);
            fileInfo.innerHTML = `
                <h5><i class="fas fa-info-circle"></i> 文件信息</h5>
                <p><strong>文件名：</strong>${file.name}</p>
                <p><strong>文件大小：</strong>${sizeInMB} MB</p>
                <p><strong>文件类型：</strong>${file.type}</p>
                <p><strong>上传类型：</strong>${getTypeText(selectedType)}</p>
            `;
            
            showResult('✅ 图片预览成功！可以点击"上传图片"按钮进行上传。', 'success');
        };
        reader.readAsDataURL(file);
    }

    // 获取类型文本
    function getTypeText(type) {
        const typeMap = {
            'avatar': '头像上传',
            'general': '通用图片',
            'review': '评价图片',
            'product': '商品图片'
        };
        return typeMap[type] || '未知类型';
    }

    // 上传按钮
    uploadBtn.addEventListener('click', function() {
        if (!selectedFile) {
            showResult('❌ 请先选择图片文件。', 'error');
            return;
        }
        
        uploadImage();
    });

    // 清除按钮
    clearBtn.addEventListener('click', function() {
        clearSelection();
    });

    // 上传图片
    async function uploadImage() {
        const formData = new FormData();
        formData.append('image', selectedFile);
        formData.append('type', selectedType);
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        
        uploadBtn.disabled = true;
        uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 上传中...';
        
        try {
            const response = await fetch('/users/upload-image/', {
                method: 'POST',
                body: formData
            });
            
            const data = await response.json();
            
            if (data.success) {
                showResult(`✅ ${data.msg}<br>📁 图片地址：${data.image_url}`, 'success');
                
                // 如果是头像上传，更新页面头像
                if (selectedType === 'avatar') {
                    updateAvatarDisplay(data.image_url);
                }
            } else {
                showResult(`❌ ${data.msg}`, 'error');
            }
            
        } catch (error) {
            showResult(`❌ 上传失败：${error.message}`, 'error');
        } finally {
            uploadBtn.disabled = false;
            uploadBtn.innerHTML = '<i class="fas fa-upload"></i> 上传图片';
        }
    }

    // 清除选择
    function clearSelection() {
        selectedFile = null;
        imageFile.value = '';
        previewSection.style.display = 'none';
        uploadBtn.disabled = true;
        showResult('🗑️ 已清除选择，请重新选择图片文件。', 'info');
    }

    // 显示结果
    function showResult(message, type) {
        result.innerHTML = message;
        result.className = `result ${type}`;
    }

    // 更新头像显示
    function updateAvatarDisplay(imageUrl) {
        // 更新页面中的头像元素
        const avatarElements = document.querySelectorAll('.user-avatar, .avatar-img');
        avatarElements.forEach(element => {
            if (element.tagName === 'IMG') {
                element.src = imageUrl;
            } else {
                element.style.backgroundImage = `url(${imageUrl})`;
            }
        });
    }
});
</script>
{% endblock %}
