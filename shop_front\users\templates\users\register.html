{% extends 'base.html' %}
{% load static %}

{% block title %}注册 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 页面背景 */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
    }

    /* 注册容器 */
    .register-container {
        max-width: 520px;
        margin: 40px auto;
        padding: 40px;
        background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
        border-radius: 20px;
        box-shadow: 0 15px 50px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(10px);
    }

    /* 顶部装饰条 */
    .register-container:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: shimmer 3s infinite;
    }

    @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
    }

    /* 头部样式 */
    .register-header {
        text-align: center;
        margin-bottom: 35px;
        position: relative;
    }

    .register-header h2 {
        font-size: 32px;
        font-weight: 700;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(220,53,69,0.1);
    }

    .register-header p {
        color: #666;
        font-size: 16px;
        margin: 0;
        opacity: 0.8;
    }

    /* 表单组样式 */
    .form-group {
        margin-bottom: 25px;
        position: relative;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #555;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    /* 输入框样式 */
    .form-control {
        width: 100%;
        height: 50px;
        padding: 12px 20px;
        border: 2px solid rgba(220,53,69,0.1);
        border-radius: 12px;
        font-size: 15px;
        background: rgba(255,255,255,0.8);
        transition: all 0.3s ease;
        backdrop-filter: blur(5px);
    }

    .form-control:focus {
        border-color: #dc3545;
        outline: none;
        box-shadow: 0 0 0 4px rgba(220,53,69,0.1);
        background: white;
        transform: translateY(-2px);
    }

    .form-control::placeholder {
        color: #999;
        font-size: 14px;
    }

    /* 头像上传区域 */
    .avatar-upload {
        margin-bottom: 30px;
        text-align: center;
    }

    .avatar-upload label {
        color: #555;
        font-weight: 600;
        margin-bottom: 15px;
        display: block;
    }

    .avatar-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        overflow: hidden;
        margin: 15px auto 20px;
        border: 4px solid rgba(220,53,69,0.2);
        position: relative;
        transition: all 0.3s ease;
        cursor: pointer;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .avatar-preview:hover {
        border-color: #dc3545;
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(220,53,69,0.2);
    }

    .avatar-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: all 0.3s ease;
    }

    .avatar-preview:hover img {
        transform: scale(1.1);
    }

    /* 文件上传按钮美化 */
    #id_avatar {
        display: none;
    }

    .avatar-upload-btn {
        display: inline-block;
        padding: 10px 20px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border-radius: 25px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(220,53,69,0.3);
        margin-top: 10px;
    }

    .avatar-upload-btn:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(220,53,69,0.4);
    }

    /* 注册按钮 */
    .btn-register {
        width: 100%;
        height: 55px;
        background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%);
        color: white;
        border: none;
        border-radius: 15px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(220,53,69,0.3);
        position: relative;
        overflow: hidden;
        margin-top: 10px;
    }

    .btn-register:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-register:hover:before {
        left: 100%;
    }

    .btn-register:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(220,53,69,0.4);
    }

    .btn-register:active {
        transform: translateY(-1px);
    }

    /* 页脚样式 */
    .register-footer {
        text-align: center;
        margin-top: 30px;
        padding-top: 25px;
        border-top: 2px solid rgba(220,53,69,0.1);
    }

    .register-footer p {
        color: #666;
        margin-bottom: 10px;
        font-size: 15px;
    }

    .register-footer a {
        color: #dc3545;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        position: relative;
    }

    .register-footer a:hover {
        color: #c82333;
        text-decoration: none;
    }

    .register-footer a:after {
        content: '';
        position: absolute;
        width: 0;
        height: 2px;
        bottom: -2px;
        left: 50%;
        background: #dc3545;
        transition: all 0.3s ease;
    }

    .register-footer a:hover:after {
        width: 100%;
        left: 0;
    }

    /* 错误信息样式 */
    .error-message {
        color: #dc3545;
        font-size: 13px;
        margin-top: 8px;
        padding: 10px 15px;
        background: rgba(220,53,69,0.1);
        border-radius: 10px;
        border-left: 4px solid #dc3545;
        animation: slideIn 0.3s ease;
        backdrop-filter: blur(5px);
    }

    @keyframes slideIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* 帮助文本样式 */
    .help-text {
        font-size: 12px;
        color: #888;
        margin-top: 6px;
        padding-left: 5px;
        opacity: 0.8;
        line-height: 1.4;
    }

    /* 输入框图标 */
    .form-group.has-icon {
        position: relative;
    }

    .form-group.has-icon .form-control {
        padding-left: 50px;
    }

    .form-group.has-icon .input-icon {
        position: absolute;
        left: 18px;
        top: 50%;
        transform: translateY(-50%);
        color: #dc3545;
        font-size: 16px;
        z-index: 2;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .register-container {
            margin: 20px;
            padding: 30px 25px;
        }

        .register-header h2 {
            font-size: 28px;
        }

        .form-control {
            height: 45px;
            font-size: 14px;
        }

        .btn-register {
            height: 50px;
            font-size: 15px;
        }

        .avatar-preview {
            width: 100px;
            height: 100px;
        }
    }

    /* 动画效果 */
    .register-container {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .form-group {
        animation: fadeInUp 0.6s ease-out;
        animation-fill-mode: both;
    }

    .form-group:nth-child(1) { animation-delay: 0.1s; }
    .form-group:nth-child(2) { animation-delay: 0.2s; }
    .form-group:nth-child(3) { animation-delay: 0.3s; }
    .form-group:nth-child(4) { animation-delay: 0.4s; }
    .form-group:nth-child(5) { animation-delay: 0.5s; }

    /* 社交登录按钮 */
    .social-button:hover {
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }

    /* 加载动画 */
    .btn-register.loading {
        pointer-events: none;
        opacity: 0.8;
    }

    .btn-register.loading:after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        margin: auto;
        border: 2px solid transparent;
        border-top-color: #ffffff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* 成功提示样式 */
    .success-message {
        color: #28a745;
        font-size: 13px;
        margin-top: 8px;
        padding: 10px 15px;
        background: rgba(40,167,69,0.1);
        border-radius: 10px;
        border-left: 4px solid #28a745;
        animation: slideIn 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="register-container">
        <div class="register-header">
            <h2>用户注册</h2>
            <p>创建您的MARS BUY账号</p>
        </div>
        
        {% if messages %}
        {% for message in messages %}
        <div class="error-message">
            {{ message }}
        </div>
        {% endfor %}
        {% endif %}
        
        <form method="post" enctype="multipart/form-data">
            {% csrf_token %}
            
            <div class="avatar-upload">
                <label><i class="fas fa-user-circle"></i> 选择头像</label>
                <div class="avatar-preview" onclick="document.getElementById('id_avatar').click()">
                    <img src="/media/avatars/default.png" alt="默认头像" id="avatar-preview-img">
                </div>
                {{ form.avatar }}
                <label for="id_avatar" class="avatar-upload-btn">
                    <i class="fas fa-camera"></i> 选择图片
                </label>
            </div>
            
            <div class="form-group has-icon">
                <label for="id_username"><i class="fas fa-user"></i> 用户名</label>
                <i class="input-icon fas fa-user"></i>
                {{ form.username }}
                {% if form.username.errors %}
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i> {{ form.username.errors.0 }}
                </div>
                {% endif %}
                <div class="help-text">{{ form.username.help_text }}</div>
            </div>

            <div class="form-group has-icon">
                <label for="id_email"><i class="fas fa-envelope"></i> 邮箱地址</label>
                <i class="input-icon fas fa-envelope"></i>
                {{ form.email }}
                {% if form.email.errors %}
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i> {{ form.email.errors.0 }}
                </div>
                {% endif %}
            </div>

            <div class="form-group has-icon">
                <label for="id_password1"><i class="fas fa-lock"></i> 设置密码</label>
                <i class="input-icon fas fa-lock"></i>
                {{ form.password1 }}
                {% if form.password1.errors %}
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i> {{ form.password1.errors.0 }}
                </div>
                {% endif %}
                <div class="help-text">{{ form.password1.help_text }}</div>
            </div>

            <div class="form-group has-icon">
                <label for="id_password2"><i class="fas fa-lock"></i> 确认密码</label>
                <i class="input-icon fas fa-lock"></i>
                {{ form.password2 }}
                {% if form.password2.errors %}
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i> {{ form.password2.errors.0 }}
                </div>
                {% endif %}
            </div>
            
            <button type="submit" class="btn-register">
                <i class="fas fa-user-plus"></i> 立即注册
            </button>
        </form>

        <div class="register-footer">
            <p>已有账号？<a href="{% url 'users:login' %}"><i class="fas fa-sign-in-alt"></i> 立即登录</a></p>

            <div class="social-login" style="margin-top: 20px;">
                <p style="color: #888; font-size: 14px; margin-bottom: 15px;">其他注册方式</p>
                <div class="social-buttons" style="display: flex; justify-content: center; gap: 15px;">
                    <a href="#" class="social-button" style="width: 40px; height: 40px; border-radius: 50%; background: #1aad19; color: white; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease;" title="微信注册">
                        <i class="fab fa-weixin"></i>
                    </a>
                    <a href="#" class="social-button" style="width: 40px; height: 40px; border-radius: 50%; background: #12b7f5; color: white; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease;" title="QQ注册">
                        <i class="fab fa-qq"></i>
                    </a>
                    <a href="#" class="social-button" style="width: 40px; height: 40px; border-radius: 50%; background: #e6162d; color: white; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease;" title="微博注册">
                        <i class="fab fa-weibo"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 头像预览
        const avatarInput = document.getElementById('id_avatar');
        const avatarPreview = document.getElementById('avatar-preview-img');

        avatarInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    alert('请选择图片文件！');
                    return;
                }

                // 检查文件大小（限制5MB）
                if (file.size > 5 * 1024 * 1024) {
                    alert('图片大小不能超过5MB！');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                    // 添加上传成功的视觉反馈
                    avatarPreview.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        avatarPreview.style.transform = 'scale(1)';
                    }, 300);
                }
                reader.readAsDataURL(file);
            }
        });

        // 表单提交动画
        const form = document.querySelector('form');
        const submitBtn = document.querySelector('.btn-register');

        form.addEventListener('submit', function(e) {
            submitBtn.classList.add('loading');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 注册中...';
        });

        // 输入框焦点效果
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });

        // 密码强度检测
        const password1 = document.getElementById('id_password1');
        if (password1) {
            password1.addEventListener('input', function() {
                const password = this.value;
                const strength = checkPasswordStrength(password);
                showPasswordStrength(strength);
            });
        }

        function checkPasswordStrength(password) {
            let score = 0;
            if (password.length >= 8) score++;
            if (/[a-z]/.test(password)) score++;
            if (/[A-Z]/.test(password)) score++;
            if (/[0-9]/.test(password)) score++;
            if (/[^A-Za-z0-9]/.test(password)) score++;

            if (score < 2) return 'weak';
            if (score < 4) return 'medium';
            return 'strong';
        }

        function showPasswordStrength(strength) {
            let existingIndicator = document.querySelector('.password-strength');
            if (existingIndicator) {
                existingIndicator.remove();
            }

            const indicator = document.createElement('div');
            indicator.className = 'password-strength';
            indicator.style.cssText = `
                margin-top: 5px;
                padding: 5px 10px;
                border-radius: 5px;
                font-size: 12px;
                font-weight: 600;
            `;

            switch(strength) {
                case 'weak':
                    indicator.textContent = '密码强度：弱';
                    indicator.style.background = 'rgba(220,53,69,0.1)';
                    indicator.style.color = '#dc3545';
                    break;
                case 'medium':
                    indicator.textContent = '密码强度：中等';
                    indicator.style.background = 'rgba(255,193,7,0.1)';
                    indicator.style.color = '#ffc107';
                    break;
                case 'strong':
                    indicator.textContent = '密码强度：强';
                    indicator.style.background = 'rgba(40,167,69,0.1)';
                    indicator.style.color = '#28a745';
                    break;
            }

            password1.parentElement.appendChild(indicator);
        }
    });
</script>
{% endblock %} 