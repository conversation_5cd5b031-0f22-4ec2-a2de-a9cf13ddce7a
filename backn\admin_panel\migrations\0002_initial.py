# Generated by Django 4.2.22 on 2025-06-09 12:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('admin_panel', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='adminlog',
            name='admin',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admin_logs', to=settings.AUTH_USER_MODEL, verbose_name='管理员'),
        ),
        migrations.AddIndex(
            model_name='adminlog',
            index=models.Index(fields=['admin', 'created_time'], name='admin_panel_admin_i_157ce0_idx'),
        ),
        migrations.AddIndex(
            model_name='adminlog',
            index=models.Index(fields=['action', 'created_time'], name='admin_panel_action_5ab308_idx'),
        ),
    ]
