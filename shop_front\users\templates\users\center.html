{% extends 'base.html' %}
{% load static %}

{% block title %}个人中心 - MARS BUY{% endblock %}

{% block extra_css %}
<style>
    /* 修改为红色主题样式 */
    .profile-container {
        max-width: 1200px;
        margin: 40px auto;
        display: flex;
        gap: 20px;
    }

    .sidebar {
        width: 250px;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.1);
        padding: 20px;
        border: 1px solid rgba(220, 53, 69, 0.1);
    }

    .user-avatar {
        text-align: center;
        margin-bottom: 20px;
    }

    .user-avatar img {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #dc3545;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.2);
    }

    .user-name {
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
    }

    .sidebar-menu {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .sidebar-menu li {
        margin-bottom: 10px;
    }

    .sidebar-menu a {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        color: #666;
        text-decoration: none;
        border-radius: 8px;
        transition: all 0.3s;
    }

    .sidebar-menu a:hover,
    .sidebar-menu a.active {
        background: rgba(220, 53, 69, 0.1);
        color: #dc3545;
        transform: translateY(-2px);
    }

    .sidebar-menu a.active {
        background: #dc3545;
        color: white;
    }

    .sidebar-menu i {
        margin-right: 10px;
        width: 20px;
        font-size: 16px;
        text-align: center;
    }

    .main-content {
        flex: 1;
        background: #fff;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        padding: 30px;
        position: relative;
        overflow: hidden;
    }

    .main-content:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #dc3545, #ff6b6b, #dc3545);
        background-size: 200% 100%;
        animation: gradientMove 3s ease infinite;
    }

    @keyframes gradientMove {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .welcome-card {
        background: linear-gradient(45deg, #dc3545, #ff6b6b);
        color: white;
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 10px 20px rgba(220, 53, 69, 0.15);
        position: relative;
        overflow: hidden;
    }

    .welcome-card:before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 150px;
        height: 150px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    .welcome-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
        font-weight: 700;
    }

    .welcome-text {
        opacity: 0.9;
        margin-bottom: 0;
        font-size: 1rem;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stats-card {
        background: #fff;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        transition: transform 0.3s, box-shadow 0.3s;
        border: 1px solid #f0f0f0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .stats-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
    }

    .stats-icon i {
        font-size: 24px;
        color: white;
    }

    .stats-title {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
    }

    .stats-value {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 0;
        color: #333;
    }

    .section-title {
        font-size: 20px;
        color: #333;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 10px;
        color: #dc3545;
    }

    .order-card {
        background: #fff;
        border-radius: 10px;
        margin-bottom: 20px;
        overflow: hidden;
        transition: transform 0.3s, box-shadow 0.3s;
        border: 1px solid #f0f0f0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .order-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .order-header {
        background-color: #f8f9fa;
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #f0f0f0;
    }

    .order-number {
        font-weight: 600;
        color: #333;
    }

    .order-time {
        color: #666;
        font-size: 14px;
    }

    .order-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 600;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-paid {
        background-color: #d4edda;
        color: #155724;
    }

    .status-shipped {
        background-color: #cce5ff;
        color: #004085;
    }

    .status-completed {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .order-body {
        padding: 15px;
    }

    .order-items {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
        overflow-x: auto;
        padding-bottom: 10px;
    }

    .order-item {
        flex: 0 0 80px;
        text-align: center;
    }

    .order-item img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 5px;
        border: 1px solid #f0f0f0;
    }

    .order-footer {
        padding: 15px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #f0f0f0;
    }

    .order-total {
        color: #dc3545;
        font-weight: 700;
        font-size: 16px;
    }

    .order-actions {
        display: flex;
        gap: 10px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #dc3545 0%, #ff6b6b 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.3);
    }

    .btn-primary:hover {
        background: linear-gradient(135deg, #c82333 0%, #dc3545 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(220, 53, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #5cb85c 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3);
    }

    .btn-success:hover {
        background: linear-gradient(135deg, #218838 0%, #28a745 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(40, 167, 69, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-info {
        background: linear-gradient(135deg, #17a2b8 0%, #5bc0de 100%);
        color: #fff;
        border: none;
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 4px 10px rgba(23, 162, 184, 0.3);
    }

    .btn-info:hover {
        background: linear-gradient(135deg, #138496 0%, #17a2b8 100%);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(23, 162, 184, 0.4);
        color: white;
        text-decoration: none;
    }

    .btn-outline-secondary {
        background: transparent;
        color: #6c757d;
        border: 1px solid #6c757d;
        border-radius: 8px;
        padding: 8px 15px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s;
    }

    .btn-outline-secondary:hover {
        background: #6c757d;
        color: white;
        text-decoration: none;
        transform: translateY(-2px);
    }

    .empty-orders {
        text-align: center;
        padding: 50px 0;
    }

    .empty-orders i {
        font-size: 60px;
        color: #dc3545;
        opacity: 0.5;
        margin-bottom: 20px;
    }

    .empty-orders p {
        color: #666;
        margin-bottom: 20px;
    }

    /* 响应式设计 */
    @media (max-width: 992px) {
        .profile-container {
            flex-direction: column;
        }

        .sidebar {
            width: 100%;
            margin-bottom: 20px;
        }

        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 576px) {
        .stats-grid {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="profile-container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="user-avatar">
                <img src="{{ user.avatar.url }}" alt="{{ user.username }}" onerror="this.src='/media/avatars/default.png'">
            </div>
            <div class="user-name">{{ user.username }}</div>
            <ul class="sidebar-menu">
                <li>
                    <a href="{% url 'users:center' %}" class="active">
                        <i class="fas fa-home"></i> 个人中心
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:profile' %}">
                        <i class="fas fa-user"></i> 个人资料
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:orders' %}">
                        <i class="fas fa-shopping-bag"></i> 我的订单
                    </a>
                </li>
                <li>
                    <a href="{% url 'reviews:my_reviews' %}">
                        <i class="fas fa-star"></i> 我的评价
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:address' %}">
                        <i class="fas fa-map-marker-alt"></i> 收货地址
                    </a>
                </li>
                <li>
                    <a href="javascript:void(0)" onclick="confirmLogout()" class="text-danger">
                        <i class="fas fa-sign-out-alt"></i> 注销账户
                    </a>
                </li>
                <li>
                    <a href="{% url 'users:password' %}">
                        <i class="fas fa-lock"></i> 修改密码
                    </a>
                </li>
            </ul>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 欢迎卡片 -->
            <div class="welcome-card">
                <h4 class="welcome-title">欢迎回来，{{ user.username }}！</h4>
                <p class="welcome-text">这里是您的个人中心，您可以查看订单、管理收货地址等。</p>
            </div>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stats-card">
                    <div class="stats-icon" style="background-color: #dc3545;">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h6 class="stats-title">待付款订单</h6>
                    <p class="stats-value">{{ pending_orders_count|default:"0" }}</p>
                </div>
                <div class="stats-card">
                    <div class="stats-icon" style="background-color: #007bff;">
                        <i class="fas fa-shipping-fast"></i>
                    </div>
                    <h6 class="stats-title">待收货订单</h6>
                    <p class="stats-value">{{ shipping_orders_count|default:"0" }}</p>
                </div>
                <div class="stats-card">
                    <div class="stats-icon" style="background-color: #ffc107;">
                        <i class="fas fa-star"></i>
                    </div>
                    <h6 class="stats-title">待评价订单</h6>
                    <p class="stats-value">{{ review_orders_count|default:"0" }}</p>
                </div>
                <div class="stats-card">
                    <div class="stats-icon" style="background-color: #6f42c1;">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h6 class="stats-title">收货地址</h6>
                    <p class="stats-value">{{ address_count|default:"0" }}</p>
                </div>
            </div>

            <!-- 最近订单 -->
            <h5 class="section-title"><i class="fas fa-clipboard-list"></i> 最近订单</h5>
            {% if recent_orders %}
                {% for order in recent_orders %}
                <div class="order-card">
                    <div class="order-header">
                        <span class="order-number">订单号：{{ order.order_number }}</span>
                        <span class="order-time">{{ order.created_time|date:"Y-m-d H:i" }}</span>
                        <span class="order-status {% if order.status == 'pending' %}status-pending{% elif order.status == 'paid' %}status-paid{% elif order.status == 'shipped' %}status-shipped{% elif order.status == 'received' %}status-completed{% endif %}">
                            {{ order.status_display }}
                        </span>
                    </div>
                    <div class="order-body">
                        <div class="order-items">
                            {% for item in order.items.all %}
                            <div class="order-item">
                                <img src="{{ item.product.image.url }}" alt="{{ item.product.name }}" onerror="this.src='/media/products/default.jpg'">
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="order-footer">
                        <div class="order-total">
                            总计：¥{{ order.total_amount }}
                        </div>
                        <div class="order-actions">
                            {% if order.status == 'pending' %}
                            <a href="{% url 'payment:pay_simple' order_number=order.order_number %}" class="btn-primary">去付款</a>
                            {% elif order.status == 'shipped' %}
                            <button class="btn-success" onclick="confirmReceive('{{ order.order_number }}')">确认收货</button>
                            {% elif order.status == 'received' %}
                            {% with first_item=order.items.first %}
                                {% if first_item %}
                                <a href="{% url 'reviews:add' first_item.product.id %}" class="btn-info">评价</a>
                                {% endif %}
                            {% endwith %}
                            {% endif %}
                            <a href="{% url 'users:orders' %}" class="btn-outline-secondary">查看详情</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-orders">
                    <i class="fas fa-shopping-bag"></i>
                    <p>暂无订单记录</p>
                    <a href="{% url 'home:index' %}" class="btn-primary">去购物</a>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmReceive(orderNumber) {
    if (confirm('确认已收到商品吗？')) {
        fetch(`/api/orders/${orderNumber}/receive/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示确认成功消息
                alert('确认收货成功！即将跳转到商品评价页面');
                // 跳转到评价页面
                if (data.review_url) {
                    window.location.href = data.review_url;
                } else {
                    location.reload();
                }
            } else {
                alert(data.message || '操作失败，请重试');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('操作失败，请重试');
        });
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 确认注销账户
function confirmLogout() {
    if (confirm('确定要注销当前账户吗？注销后需要重新登录。')) {
        // 发送注销请求
        fetch('{% url "account:logout" %}', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        })
        .then(response => {
            if (response.ok) {
                // 注销成功，跳转到首页
                alert('注销成功！');
                window.location.href = '{% url "goods:index" %}';
            } else {
                alert('注销失败，请重试');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('注销失败，请重试');
        });
    }
}
</script>
{% endblock %} 