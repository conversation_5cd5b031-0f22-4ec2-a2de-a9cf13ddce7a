#!/usr/bin/env python
"""
简单测试商品图片创建
"""
import os
import sys
import django

# 设置Django环境
sys.path.append('D:/python/shop/shop_front')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from goods.models import Product, ProductImage

def test_simple_creation():
    """简单测试"""
    print("🔍 测试商品图片创建...")
    
    # 获取一个商品
    try:
        product = Product.objects.first()
        if not product:
            print("❌ 没有找到商品")
            return
        
        print(f"📦 使用商品: {product.name}")
        
        # 测试创建商品图片（提供所有必需字段）
        try:
            product_image = ProductImage(
                product=product,
                alt_text="测试图片",
                sort_order=1
            )
            # 不保存，只是测试模型创建
            print(f"✅ 商品图片模型创建成功")
            print(f"   Alt Text: '{product_image.alt_text}'")
            print(f"   Sort Order: {product_image.sort_order}")
            
        except Exception as e:
            print(f"❌ 商品图片模型创建失败: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")

if __name__ == '__main__':
    test_simple_creation()
