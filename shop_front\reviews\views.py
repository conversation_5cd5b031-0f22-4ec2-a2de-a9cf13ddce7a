from django.shortcuts import render, redirect, get_object_or_404
from django.views import View
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator
from django.urls import reverse
from .models import Review, ReviewImage
from order.models import OrderItem
from goods.models import Product
from .forms import ReviewForm

# Create your views here.

class ReviewCreateView(LoginRequiredMixin, View):
    def get(self, request, order_item_id):
        order_item = OrderItem.objects.get(id=order_item_id, order__user=request.user)
        return render(request, 'reviews/create.html', {'order_item': order_item})

    def post(self, request, order_item_id):
        order_item = OrderItem.objects.get(id=order_item_id, order__user=request.user)
        
        review = Review.objects.create(
            user=request.user,
            product=order_item.product,
            order_item=order_item,
            rating=request.POST.get('rating'),
            content=request.POST.get('content')
        )
        
        # 处理评价图片
        for image in request.FILES.getlist('images'):
            ReviewImage.objects.create(review=review, image=image)
            
        return redirect('order:detail', order_id=order_item.order.id)

@login_required
def add_review(request, product_id):
    """添加商品评价"""
    from django.http import JsonResponse

    product = get_object_or_404(Product, id=product_id)

    # 检查用户是否为管理员
    if request.user.is_staff:
        return JsonResponse({'success': False, 'error': '管理员不能评价商品'})

    # 检查用户是否已经评价过该商品
    existing_review = Review.objects.filter(user=request.user, product=product).first()
    if existing_review:
        return JsonResponse({'success': False, 'error': '您已经评价过该商品'})

    if request.method == 'POST':
        try:
            # 获取表单数据
            content = request.POST.get('content', '').strip()
            score = request.POST.get('score')
            is_anonymous = request.POST.get('is_anonymous') == 'on'

            # 验证数据
            if not content:
                return JsonResponse({'success': False, 'error': '评价内容不能为空'})

            if not score or int(score) not in [1, 2, 3, 4, 5]:
                return JsonResponse({'success': False, 'error': '请选择有效的评分'})

            # 创建评价
            review = Review.objects.create(
                user=request.user,
                product=product,
                content=content,
                score=int(score),
                is_anonymous=is_anonymous
            )

            # 处理评价图片
            images = request.FILES.getlist('images')
            for image in images[:5]:  # 最多5张图片
                ReviewImage.objects.create(
                    review=review,
                    image=image
                )

            return JsonResponse({'success': True, 'message': '评价提交成功'})

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    # GET请求返回表单页面
    form = ReviewForm()
    return render(request, 'reviews/add.html', {
        'form': form,
        'product': product
    })

def review_list(request, product_id):
    """商品评价列表"""
    product = get_object_or_404(Product, id=product_id)
    reviews = Review.objects.filter(product=product)
    
    # 分页
    paginator = Paginator(reviews, 10)
    page = request.GET.get('page')
    reviews = paginator.get_page(page)
    
    return render(request, 'reviews/list.html', {
        'product': product,
        'reviews': reviews
    })

@login_required
def my_reviews(request):
    """我的评价"""
    reviews = Review.objects.filter(user=request.user)

    # 分页
    paginator = Paginator(reviews, 10)
    page = request.GET.get('page')
    reviews = paginator.get_page(page)

    return render(request, 'reviews/my_reviews.html', {
        'reviews': reviews
    })

@login_required
def add_review_by_order(request):
    """基于订单添加评价"""
    from order.models import Order

    order_id = request.GET.get('order_id')
    if not order_id:
        return redirect('order:list')

    # 获取订单并验证权限
    order = get_object_or_404(Order, id=order_id, user=request.user)

    # 检查订单状态（只有已支付或已完成的订单才能评价）
    if order.status not in ['paid', 'completed']:
        return redirect('order:list')

    # 获取订单中的商品
    order_items = order.items.all()

    if request.method == 'POST':
        from django.http import JsonResponse

        try:
            # 处理每个商品的评价
            for item in order_items:
                product_id = item.product.id
                content = request.POST.get(f'content_{product_id}', '').strip()
                score = request.POST.get(f'score_{product_id}')
                is_anonymous = request.POST.get(f'anonymous_{product_id}') == 'on'

                # 检查是否已经评价过
                existing_review = Review.objects.filter(
                    user=request.user,
                    product=item.product
                ).first()

                if existing_review:
                    continue  # 跳过已评价的商品

                # 验证数据
                if content and score and int(score) in [1, 2, 3, 4, 5]:
                    # 创建评价
                    review = Review.objects.create(
                        user=request.user,
                        product=item.product,
                        content=content,
                        score=int(score),
                        is_anonymous=is_anonymous
                    )

                    # 处理评价图片
                    images = request.FILES.getlist(f'images_{product_id}')
                    for image in images[:5]:  # 最多5张图片
                        ReviewImage.objects.create(
                            review=review,
                            image=image
                        )

                    # 标记订单项为已评价
                    item.is_reviewed = True
                    item.save()

            # 检查订单是否所有商品都已评价
            all_reviewed = all(item.is_reviewed for item in order.items.all())
            if all_reviewed:
                # 如果所有商品都已评价，可以更新订单状态为已完成
                if order.status == 'received':
                    order.status = 'completed'
                    order.save()

            return JsonResponse({
                'success': True,
                'message': '评价提交成功',
                'redirect_url': reverse('reviews:my_reviews')
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    # GET请求显示评价表单
    return render(request, 'reviews/add_by_order.html', {
        'order': order,
        'order_items': order_items
    })
