#!/usr/bin/env python3
"""
创建简单测试用户
"""

import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'shop_front.settings')
django.setup()

from django.contrib.auth import get_user_model

User = get_user_model()

def create_simple_user():
    print("🔧 创建简单测试用户...")
    
    # 删除现有的testuser
    User.objects.filter(username='testuser').delete()
    
    # 创建新的测试用户
    user = User.objects.create_user(
        username='testuser',
        email='<EMAIL>',
        password='123456'
    )
    
    print(f"✅ 创建用户: {user.username}")
    print(f"✅ 密码验证: {user.check_password('123456')}")
    print("\n📝 登录信息:")
    print("用户名: testuser")
    print("密码: 123456")
    print("登录地址: http://127.0.0.1:8001/users/login/")

if __name__ == "__main__":
    create_simple_user()
