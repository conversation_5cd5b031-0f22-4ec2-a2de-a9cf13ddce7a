{% extends "base.html" %}
{% load static %}

{% block title %}添加商品{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/admin_product.css' %}">
{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="product-form-container">
        <div class="row mb-4">
            <div class="col">
                <h2 class="product-form-title">添加新商品</h2>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'goods:index' %}"><i class="fas fa-home"></i> 首页</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'account:admin_dashboard' %}"><i class="fas fa-tachometer-alt"></i> 管理员仪表板</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'account:product_list' %}"><i class="fas fa-list"></i> 商品管理</a></li>
                        <li class="breadcrumb-item active"><i class="fas fa-plus"></i> 添加商品</li>
                    </ol>
                </nav>
            </div>
        </div>

        {% if messages %}
        <div class="messages mb-4">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card form-card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>添加新商品</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="{% url 'account:product_add' %}" enctype="multipart/form-data" id="productForm" novalidate>
                            {% csrf_token %}
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">商品名称</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-tag"></i></span>
                                        <input type="text" class="form-control" id="name" name="name" required placeholder="请输入商品名称">
                                    </div>
                                    <div class="invalid-feedback" id="name-feedback">
                                        请输入有效的商品名称
                                    </div>
                                    <small class="form-text">商品名称将显示在商品列表和详情页</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="category" class="form-label">商品分类</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-folder"></i></span>
                                        <select class="form-select" id="category" name="category" required>
                                            <option value="" selected disabled>请选择分类</option>
                                            {% for category in categories %}
                                            <option value="{{ category.id }}">{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="invalid-feedback" id="category-feedback">
                                        请选择一个商品分类
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <label for="price" class="form-label">价格</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-yen-sign"></i></span>
                                        <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" required placeholder="0.00">
                                    </div>
                                    <div class="invalid-feedback" id="price-feedback">
                                        请输入有效的价格（大于0）
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="stock" class="form-label">库存</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-cubes"></i></span>
                                        <input type="number" class="form-control" id="stock" name="stock" min="0" required placeholder="0">
                                    </div>
                                    <div class="invalid-feedback" id="stock-feedback">
                                        库存不能为负数
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="description" class="form-label">商品描述</label>
                                <textarea class="form-control" id="description" name="description" rows="5" required placeholder="请详细描述商品特点、规格等信息"></textarea>
                                <div class="invalid-feedback" id="description-feedback">
                                    请输入商品描述
                                </div>
                                <small class="form-text">详细的商品描述有助于提高销售转化率</small>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_hot" name="is_hot">
                                        <label class="form-check-label" for="is_hot">
                                            <i class="fas fa-fire text-danger me-1"></i> 热门商品
                                        </label>
                                    </div>
                                    <small class="form-text">热门商品将显示在首页推荐区域</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_new" name="is_new" checked>
                                        <label class="form-check-label" for="is_new">
                                            <i class="fas fa-star text-warning me-1"></i> 新品上市
                                        </label>
                                    </div>
                                    <small class="form-text">新品将显示在新品上市区域</small>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                        <label class="form-check-label" for="is_active">
                                            <i class="fas fa-check-circle text-success me-1"></i> 上架状态
                                        </label>
                                    </div>
                                    <small class="form-text">只有上架的商品才会在商城展示</small>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="main_image" class="form-label">商品主图</label>
                                <div class="file-upload-container">
                                    <label for="main_image">
                                        <div class="icon"><i class="fas fa-cloud-upload-alt"></i></div>
                                        <div class="text">点击或拖拽图片到此处上传</div>
                                    </label>
                                    <input class="form-control" type="file" id="main_image" name="main_image" accept="image/*" required>
                                </div>
                                <div class="invalid-feedback" id="image-feedback">
                                    请上传商品主图
                                </div>
                                <img id="imagePreview" class="image-preview" src="#" alt="图片预览">
                                <small class="form-text">推荐上传尺寸比例为1:1的图片，最大2MB</small>
                            </div>
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="{% url 'account:product_list' %}" class="btn btn-outline-secondary me-md-2">
                                    <i class="fas fa-times me-1"></i> 取消
                                </a>
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="fas fa-save me-1"></i> 添加商品
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 图片预览功能
        const mainImage = document.getElementById('main_image');
        const imagePreview = document.getElementById('imagePreview');
        
        mainImage.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    imagePreview.src = e.target.result;
                    imagePreview.style.display = 'block';
                    validateImageSize(mainImage.files[0]);
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
        
        // 验证图片大小
        function validateImageSize(file) {
            const maxSize = 2 * 1024 * 1024; // 2MB
            
            if (file.size > maxSize) {
                mainImage.classList.add('is-invalid');
                document.getElementById('image-feedback').textContent = '图片大小不能超过2MB';
                return false;
            } else {
                mainImage.classList.remove('is-invalid');
                return true;
            }
        }
        
        // 字段实时验证
        const nameInput = document.getElementById('name');
        const priceInput = document.getElementById('price');
        const stockInput = document.getElementById('stock');
        const descriptionInput = document.getElementById('description');
        const categorySelect = document.getElementById('category');
        
        // 姓名验证
        nameInput.addEventListener('input', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
                document.getElementById('name-feedback').textContent = '请输入商品名称';
            } else if (this.value.length > 100) {
                this.classList.add('is-invalid');
                document.getElementById('name-feedback').textContent = '商品名称不能超过100个字符';
            } else {
                this.classList.remove('is-invalid');
            }
        });
        
        // 价格验证
        priceInput.addEventListener('input', function() {
            if (this.value === '' || parseFloat(this.value) <= 0) {
                this.classList.add('is-invalid');
                document.getElementById('price-feedback').textContent = '请输入有效的价格（大于0）';
            } else {
                this.classList.remove('is-invalid');
            }
        });
        
        // 库存验证
        stockInput.addEventListener('input', function() {
            if (this.value === '' || parseInt(this.value) < 0) {
                this.classList.add('is-invalid');
                document.getElementById('stock-feedback').textContent = '库存不能为负数';
            } else {
                this.classList.remove('is-invalid');
            }
        });
        
        // 描述验证
        descriptionInput.addEventListener('input', function() {
            if (this.value.trim() === '') {
                this.classList.add('is-invalid');
                document.getElementById('description-feedback').textContent = '请输入商品描述';
            } else {
                this.classList.remove('is-invalid');
            }
        });
        
        // 分类验证
        categorySelect.addEventListener('change', function() {
            if (this.value === '' || this.value === null) {
                this.classList.add('is-invalid');
                document.getElementById('category-feedback').textContent = '请选择一个商品分类';
            } else {
                this.classList.remove('is-invalid');
            }
        });
        
        // 表单验证
        const productForm = document.getElementById('productForm');
        const submitBtn = document.getElementById('submitBtn');
        
        productForm.addEventListener('submit', function(event) {
            let isValid = true;
            
            // 验证所有字段
            if (nameInput.value.trim() === '') {
                nameInput.classList.add('is-invalid');
                isValid = false;
            }
            
            if (categorySelect.value === '' || categorySelect.value === null) {
                categorySelect.classList.add('is-invalid');
                isValid = false;
            }
            
            if (priceInput.value === '' || parseFloat(priceInput.value) <= 0) {
                priceInput.classList.add('is-invalid');
                isValid = false;
            }
            
            if (stockInput.value === '' || parseInt(stockInput.value) < 0) {
                stockInput.classList.add('is-invalid');
                isValid = false;
            }
            
            if (descriptionInput.value.trim() === '') {
                descriptionInput.classList.add('is-invalid');
                isValid = false;
            }
            
            if (!mainImage.files || mainImage.files.length === 0) {
                mainImage.classList.add('is-invalid');
                document.getElementById('image-feedback').textContent = '请上传商品主图';
                isValid = false;
            } else if (!validateImageSize(mainImage.files[0])) {
                isValid = false;
            }
            
            if (!isValid) {
                event.preventDefault();
                // 滚动到第一个错误字段
                const firstInvalidField = document.querySelector('.is-invalid');
                if (firstInvalidField) {
                    firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            } else {
                // 提交按钮加载状态
                submitBtn.classList.add('btn-loading');
                submitBtn.disabled = true;
            }
        });
        
        // 取消按钮确认
        const cancelBtn = document.querySelector('.btn-outline-secondary');
        cancelBtn.addEventListener('click', function(event) {
            if (formHasChanges()) {
                if (!confirm('您有未保存的更改，确定要离开吗？')) {
                    event.preventDefault();
                }
            }
        });
        
        // 检查表单是否有更改
        function formHasChanges() {
            return nameInput.value !== '' || 
                   priceInput.value !== '' || 
                   stockInput.value !== '' || 
                   descriptionInput.value !== '' || 
                   categorySelect.value !== '' || 
                   mainImage.files.length > 0;
        }
        
        // 文件拖放上传增强
        const fileUploadContainer = document.querySelector('.file-upload-container');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileUploadContainer.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            fileUploadContainer.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            fileUploadContainer.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            fileUploadContainer.classList.add('border-primary');
            fileUploadContainer.style.backgroundColor = '#fff5f7';
        }
        
        function unhighlight() {
            fileUploadContainer.classList.remove('border-primary');
            fileUploadContainer.style.backgroundColor = '#f8f9fa';
        }
        
        fileUploadContainer.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            
            if (files.length > 0) {
                mainImage.files = files;
                const event = new Event('change');
                mainImage.dispatchEvent(event);
            }
        }
    });
</script>
{% endblock %} 