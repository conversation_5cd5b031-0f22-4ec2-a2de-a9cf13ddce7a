from django.db import models
from django.utils.translation import gettext_lazy as _
from goods.models import Product
from django.urls import reverse
from django.conf import settings
from django.utils import timezone

class Cart(models.Model):
    """购物车模型"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name=_('用户'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name=_('商品'))
    quantity = models.PositiveIntegerField(default=1, verbose_name=_('数量'))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_('创建时间'))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_('更新时间'))
    selected = models.BooleanField(default=True, verbose_name=_('是否选中'))

    class Meta:
        verbose_name = _('购物车')
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['user', 'product']),
            models.Index(fields=['selected']),
        ]

    def __str__(self):
        return f"{self.user.username}的购物车 - {self.product.name}"

    def get_absolute_url(self):
        return reverse('order:cart_detail')

    @property
    def total_price(self):
        """计算购物车项的总价"""
        return self.quantity * self.product.price

class Coupon(models.Model):
    """优惠券模型"""
    COUPON_TYPE_CHOICES = (
        ('amount', _('固定金额')),
        ('percent', _('折扣百分比')),
    )

    code = models.CharField(max_length=50, unique=True, verbose_name=_('优惠码'))
    type = models.CharField(max_length=10, choices=COUPON_TYPE_CHOICES, default='amount', verbose_name=_('优惠类型'))
    value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name=_('优惠值'))
    min_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('最低使用金额'))
    start_time = models.DateTimeField(verbose_name=_('生效时间'))
    end_time = models.DateTimeField(verbose_name=_('失效时间'))
    is_active = models.BooleanField(default=True, verbose_name=_('是否有效'))
    created_time = models.DateTimeField(auto_now_add=True, verbose_name=_('创建时间'))

    class Meta:
        verbose_name = _('优惠券')
        verbose_name_plural = verbose_name
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['start_time', 'end_time']),
        ]

    def __str__(self):
        return self.code

class Order(models.Model):
    """订单模型"""
    ORDER_STATUS = (
        ('pending', _('待支付')),
        ('paid', _('已支付')),
        ('shipped', _('已发货')),
        ('received', _('已收货')),
        ('cancelled', _('已取消')),
        ('refunded', _('已退款')),
    )

    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name=_('用户'))
    order_number = models.CharField(max_length=32, unique=True, db_index=True, verbose_name=_('订单号'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('订单总额'))
    pay_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('实付金额'))
    coupon = models.ForeignKey(Coupon, on_delete=models.SET_NULL, null=True, blank=True, verbose_name=_('使用的优惠券'))
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('优惠金额'))
    status = models.CharField(max_length=20, choices=ORDER_STATUS, default='pending', db_index=True, verbose_name=_('订单状态'))
    shipping_address = models.TextField(verbose_name=_('配送地址'), null=True, blank=True)
    contact_phone = models.CharField(max_length=20, verbose_name=_('联系电话'), null=True, blank=True)
    receiver = models.CharField(max_length=50, verbose_name=_('收货人'), null=True, blank=True)
    receiver_mobile = models.CharField(max_length=11, verbose_name=_('收货人手机'), null=True, blank=True)
    remark = models.TextField(blank=True, null=True, verbose_name=_('订单备注'))
    created_time = models.DateTimeField(default=timezone.now, db_index=True, verbose_name=_('创建时间'))
    updated_time = models.DateTimeField(auto_now=True, verbose_name=_('更新时间'))
    paid_time = models.DateTimeField(null=True, blank=True, verbose_name=_('支付时间'))
    shipped_time = models.DateTimeField(null=True, blank=True, verbose_name=_('发货时间'))
    received_time = models.DateTimeField(null=True, blank=True, verbose_name=_('收货时间'))
    cancelled_time = models.DateTimeField(null=True, blank=True, verbose_name=_('取消时间'))
    refund_time = models.DateTimeField(null=True, blank=True, verbose_name=_('退款时间'))

    class Meta:
        verbose_name = _('订单')
        verbose_name_plural = verbose_name
        ordering = ['-created_time']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['order_number']),
            models.Index(fields=['created_time']),
        ]

    def __str__(self):
        return self.order_number

    def get_absolute_url(self):
        return reverse('order:detail', args=[self.order_number])
    
    @property
    def status_display(self):
        """获取状态的显示文本"""
        return dict(self.ORDER_STATUS).get(self.status, '')
    
    @property
    def is_paid(self):
        """判断订单是否已支付"""
        return self.status in ['paid', 'shipped', 'received']

    def calculate_discount(self):
        """计算优惠金额"""
        if not self.coupon:
            return 0

        if self.coupon.type == 'amount':
            return min(self.coupon.value, self.total_amount)
        else:  # percent
            return self.total_amount * (self.coupon.value / 100)

    @property
    def is_fully_reviewed(self):
        """检查订单是否已完全评价"""
        if not self.items.exists():
            return False
        return all(item.is_reviewed for item in self.items.all())

    @property
    def can_review(self):
        """检查订单是否可以评价"""
        return self.status in ['paid', 'received', 'completed'] and not self.is_fully_reviewed

class OrderItem(models.Model):
    """订单项模型"""
    order = models.ForeignKey(Order, related_name='items', on_delete=models.CASCADE, verbose_name=_('订单'))
    product = models.ForeignKey(Product, on_delete=models.CASCADE, verbose_name=_('商品'))
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('价格'))
    quantity = models.PositiveIntegerField(default=1, verbose_name=_('数量'))
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name=_('总金额'))
    is_reviewed = models.BooleanField(default=False, verbose_name=_('是否已评价'))

    class Meta:
        verbose_name = _('订单项')
        verbose_name_plural = verbose_name
        indexes = [
            models.Index(fields=['order', 'product']),
            models.Index(fields=['is_reviewed']),
        ]

    def __str__(self):
        return f"{self.order.order_number} - {self.product.name}"

    def save(self, *args, **kwargs):
        """重写保存方法，自动计算总金额"""
        self.total_amount = self.price * self.quantity
        super().save(*args, **kwargs)

    @property
    def total_price(self):
        """计算订单项的总价"""
        return self.quantity * self.price
