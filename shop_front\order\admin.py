from django.contrib import admin
from .models import Cart, Order, OrderItem

class OrderItemInline(admin.TabularInline):
    model = OrderItem
    extra = 0
    readonly_fields = ['total_price']

@admin.register(Order)
class OrderAdmin(admin.ModelAdmin):
    list_display = ['order_number', 'user', 'total_amount', 'pay_amount', 'status', 'created_time']
    list_filter = ['status', 'created_time']
    search_fields = ['order_number', 'user__username', 'receiver']
    readonly_fields = ['order_number', 'created_time', 'updated_time']
    inlines = [OrderItemInline]
    date_hierarchy = 'created_time'
    fieldsets = (
        ('基本信息', {
            'fields': ('order_number', 'user', 'status', 'total_amount', 'pay_amount')
        }),
        ('收货信息', {
            'fields': ('receiver', 'receiver_mobile', 'address', 'shipping_address', 'contact_phone', 'remark')
        }),
        ('时间信息', {
            'fields': ('created_time', 'updated_time', 'paid_time', 'shipped_time', 'received_time')
        }),
    )

@admin.register(Cart)
class CartAdmin(admin.ModelAdmin):
    list_display = ['user', 'product', 'quantity', 'total_price', 'created_at']
    list_filter = ['created_at']
    search_fields = ['user__username', 'product__name']
    readonly_fields = ['total_price']
