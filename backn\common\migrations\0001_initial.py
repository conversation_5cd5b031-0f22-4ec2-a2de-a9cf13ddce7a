# Generated by Django 4.2.22 on 2025-06-09 12:27

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Settings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('site_name', models.CharField(default='MARS BUY商城', max_length=100, verbose_name='网站名称')),
                ('site_description', models.TextField(blank=True, verbose_name='网站描述')),
                ('contact_email', models.EmailField(blank=True, max_length=254, verbose_name='联系邮箱')),
                ('contact_phone', models.CharField(blank=True, max_length=20, verbose_name='联系电话')),
                ('smtp_host', models.CharField(blank=True, max_length=100, verbose_name='SMTP服务器')),
                ('smtp_port', models.IntegerField(default=25, verbose_name='SMTP端口')),
                ('smtp_user', models.CharField(blank=True, max_length=100, verbose_name='SMTP用户名')),
                ('smtp_password', models.CharField(blank=True, max_length=100, verbose_name='SMTP密码')),
                ('smtp_ssl', models.BooleanField(default=True, verbose_name='使用SSL')),
                ('alipay_app_id', models.CharField(blank=True, max_length=100, verbose_name='支付宝AppID')),
                ('alipay_private_key', models.TextField(blank=True, verbose_name='支付宝私钥')),
                ('alipay_public_key', models.TextField(blank=True, verbose_name='支付宝公钥')),
                ('wechat_app_id', models.CharField(blank=True, max_length=100, verbose_name='微信支付AppID')),
                ('wechat_mch_id', models.CharField(blank=True, max_length=100, verbose_name='微信支付商户号')),
                ('wechat_key', models.CharField(blank=True, max_length=100, verbose_name='微信支付密钥')),
                ('order_timeout', models.IntegerField(default=30, verbose_name='订单超时时间(分钟)')),
                ('maintenance_mode', models.BooleanField(default=False, verbose_name='维护模式')),
                ('maintenance_message', models.TextField(blank=True, verbose_name='维护提示信息')),
            ],
            options={
                'verbose_name': '系统设置',
                'verbose_name_plural': '系统设置',
            },
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True, verbose_name='配置键')),
                ('value', models.TextField(verbose_name='配置值')),
                ('description', models.CharField(blank=True, max_length=255, null=True, verbose_name='配置描述')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
                'ordering': ['key'],
                'indexes': [models.Index(fields=['key'], name='common_syst_key_733e35_idx'), models.Index(fields=['is_active'], name='common_syst_is_acti_498ecb_idx')],
            },
        ),
    ]
