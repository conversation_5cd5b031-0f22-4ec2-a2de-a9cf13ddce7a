{% extends 'base.html' %}

{% block title %}编辑商品 - MARS BUY 后台管理系统{% endblock %}

{% block page_title %}编辑商品{% endblock %}

{% block page_actions %}
<div class="btn-group">
    <a href="{% url 'goods:list' %}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-1"></i>返回列表
    </a>
    <a href="{% url 'goods:detail' product.id %}" class="btn btn-outline-info">
        <i class="fas fa-eye me-1"></i>查看详情
    </a>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>编辑商品信息
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data">
                    {% csrf_token %}
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label for="name" class="form-label">商品名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ product.name }}" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="category" class="form-label">商品分类 <span class="text-danger">*</span></label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">请选择分类</option>
                                    {% for category in categories %}
                                    <option value="{{ category.id }}" 
                                            {% if category.id == product.category.id %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">商品描述 <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="description" name="description" rows="4" required>{{ product.description }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="price" class="form-label">商品价格 <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <span class="input-group-text">¥</span>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           step="0.01" min="0" value="{{ product.price }}" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="stock" class="form-label">库存数量</label>
                                <input type="number" class="form-control" id="stock" name="stock" 
                                       min="0" value="{{ product.stock }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="main_image" class="form-label">商品主图</label>
                        {% if product.main_image %}
                        <div class="mb-2">
                            <img src="{{ product.main_image.url }}" alt="{{ product.name }}" 
                                 class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                            <div class="small text-muted">当前图片</div>
                        </div>
                        {% endif %}
                        <input type="file" class="form-control" id="main_image" name="main_image" 
                               accept="image/*">
                        <div class="form-text">支持 JPG、PNG、GIF 格式，建议尺寸 800x800 像素。留空则保持原图片。</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                       {% if product.is_active %}checked{% endif %}>
                                <label class="form-check-label" for="is_active">
                                    商品上架
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_hot" name="is_hot"
                                       {% if product.is_hot %}checked{% endif %}>
                                <label class="form-check-label" for="is_hot">
                                    设为热门
                                </label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is_new" name="is_new"
                                       {% if product.is_new %}checked{% endif %}>
                                <label class="form-check-label" for="is_new">
                                    标记为新品
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'goods:detail' product.id %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>取消
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>商品信息
                </h6>
            </div>
            <div class="card-body">
                <table class="table table-sm table-borderless">
                    <tr>
                        <td><strong>商品ID：</strong></td>
                        <td>{{ product.id }}</td>
                    </tr>
                    <tr>
                        <td><strong>当前分类：</strong></td>
                        <td>{{ product.category.name }}</td>
                    </tr>
                    <tr>
                        <td><strong>当前价格：</strong></td>
                        <td class="text-success">¥{{ product.price }}</td>
                    </tr>
                    <tr>
                        <td><strong>当前库存：</strong></td>
                        <td>{{ product.stock }}</td>
                    </tr>
                    <tr>
                        <td><strong>销售数量：</strong></td>
                        <td>{{ product.sales }}</td>
                    </tr>
                    <tr>
                        <td><strong>创建时间：</strong></td>
                        <td>{{ product.created_time|date:"Y-m-d H:i" }}</td>
                    </tr>
                    <tr>
                        <td><strong>更新时间：</strong></td>
                        <td>{{ product.updated_time|date:"Y-m-d H:i" }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>编辑提示
                </h6>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb me-2"></i>编辑建议</h6>
                    <ul class="mb-0 small">
                        <li>修改价格时请考虑市场竞争</li>
                        <li>库存调整会影响商品可购买性</li>
                        <li>商品描述要准确反映商品特点</li>
                        <li>图片更新后会立即在前台显示</li>
                        <li>下架商品不会在前台显示</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>注意事项</h6>
                    <ul class="mb-0 small">
                        <li>带 <span class="text-danger">*</span> 的字段为必填项</li>
                        <li>价格修改会影响正在进行的订单</li>
                        <li>分类修改可能影响商品展示位置</li>
                        <li>修改后的信息会立即生效</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>商品统计
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h6 class="text-primary mb-1">{{ product.sales }}</h6>
                            <small class="text-muted">总销量</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h6 class="text-success mb-1">{{ product.stock }}</h6>
                        <small class="text-muted">当前库存</small>
                    </div>
                </div>
                <hr>
                <div class="text-center">
                    <h6 class="text-warning mb-1">¥{% widthratio product.sales 1 product.price %}</h6>
                    <small class="text-muted">总销售额</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 图片预览
    $('#main_image').change(function() {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                // 如果已有预览图，则更新，否则创建新的
                let preview = $('#image-preview');
                if (preview.length === 0) {
                    $('#main_image').after(`
                        <div id="image-preview" class="mt-2">
                            <img src="" alt="新图片预览" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                            <div class="small text-muted">新图片预览</div>
                        </div>
                    `);
                    preview = $('#image-preview');
                }
                preview.find('img').attr('src', e.target.result);
            };
            reader.readAsDataURL(file);
        }
    });
    
    // 表单验证
    $('form').submit(function(e) {
        const name = $('#name').val().trim();
        const price = $('#price').val();
        const category = $('#category').val();
        const description = $('#description').val().trim();
        
        if (!name) {
            alert('请输入商品名称');
            $('#name').focus();
            e.preventDefault();
            return false;
        }
        
        if (!category) {
            alert('请选择商品分类');
            $('#category').focus();
            e.preventDefault();
            return false;
        }
        
        if (!description) {
            alert('请输入商品描述');
            $('#description').focus();
            e.preventDefault();
            return false;
        }
        
        if (!price || parseFloat(price) <= 0) {
            alert('请输入有效的商品价格');
            $('#price').focus();
            e.preventDefault();
            return false;
        }
        
        return true;
    });
});
</script>
{% endblock %}
