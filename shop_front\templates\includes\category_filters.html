<!-- 商品分类筛选器 -->
<div class="category-filters-container">
  <div class="filter-section">
    <div class="filter-header">
      <i class="fas fa-filter"></i>
      <span>商品筛选</span>
    </div>
    
    <div class="filter-item">
      <div class="filter-label">分类：</div>
      <div class="filter-options">
        <a href="{% url 'goods:list' %}" class="option-item active">全部</a>
        <a href="{% url 'goods:list' %}?keyword=充电宝" class="option-item">充电宝</a>
        <a href="{% url 'goods:list' %}?keyword=华为" class="option-item">华为</a>
        <a href="{% url 'goods:list' %}?keyword=卧室" class="option-item">卧室</a>
        <a href="{% url 'goods:list' %}?keyword=厨房" class="option-item">厨房</a>
        <a href="{% url 'goods:list' %}?keyword=台式机" class="option-item">台式机</a>
        <a href="{% url 'goods:list' %}?keyword=后台编码测试" class="option-item">后台编码测试</a>
        <a href="{% url 'goods:list' %}?keyword=咖啡" class="option-item">咖啡</a>
        <a href="{% url 'goods:list' %}?keyword=坚果" class="option-item">坚果</a>
        <a href="{% url 'goods:list' %}?keyword=实时更新测试" class="option-item">实时更新测试</a>
        <a href="{% url 'goods:list' %}?keyword=客厅" class="option-item">客厅</a>
        <a href="{% url 'goods:list' %}?keyword=家居" class="option-item">家居</a>
        <a href="{% url 'goods:list' %}?keyword=家居生活" class="option-item">家居生活</a>
        <a href="{% url 'goods:list' %}?keyword=小米" class="option-item">小米</a>
        <a href="{% url 'goods:list' %}?keyword=平板" class="option-item">平板</a>
        <a href="{% url 'goods:list' %}?keyword=床品" class="option-item">床品</a>
        <a href="{% url 'goods:list' %}?keyword=手机" class="option-item">手机</a>
        <a href="{% url 'goods:list' %}?keyword=手机数码" class="option-item">手机数码</a>
        <a href="{% url 'goods:list' %}?keyword=抱枕" class="option-item">抱枕</a>
        <a href="{% url 'goods:list' %}?keyword=挂毯" class="option-item">挂毯</a>
        <a href="{% url 'goods:list' %}?keyword=数码" class="option-item">数码</a>
        <a href="{% url 'goods:list' %}?keyword=时尚服装" class="option-item">时尚服装</a>
      </div>
    </div>
    
    <div class="filter-item">
      <div class="filter-label">排序：</div>
      <div class="filter-options">
        <a href="{% url 'goods:list' %}?sort=price_asc" class="option-item">价格升序</a>
        <a href="{% url 'goods:list' %}?sort=price_desc" class="option-item">价格降序</a>
        <a href="{% url 'goods:list' %}?sort=name" class="option-item">名称排序</a>
        <a href="{% url 'goods:list' %}?sort=newest" class="option-item">最新上架</a>
      </div>
    </div>
    
    <div class="filter-item">
      <div class="filter-label">特色：</div>
      <div class="filter-options">
        <a href="{% url 'goods:list' %}?is_hot=true" class="option-item">热门商品</a>
        <a href="{% url 'goods:list' %}?is_new=true" class="option-item">新品上市</a>
      </div>
    </div>
  </div>
</div>

<style>
.category-filters-container {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 15px 20px;
  margin-bottom: 20px;
}

.filter-section {
  width: 100%;
}

.filter-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: #dc3545;
  font-weight: bold;
}

.filter-header i {
  margin-right: 8px;
}

.filter-item {
  display: flex;
  margin-bottom: 15px;
  border-bottom: 1px dashed #eee;
  padding-bottom: 15px;
}

.filter-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.filter-label {
  flex-shrink: 0;
  width: 80px;
  font-weight: bold;
  color: #666;
  padding-top: 6px;
}

.filter-options {
  flex: 1;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.option-item {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  color: #666;
  text-decoration: none;
  background-color: #f8f9fa;
  border: 1px solid transparent;
  font-size: 14px;
  transition: all 0.3s;
}

.option-item:hover {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.08);
  transform: translateY(-2px);
}

.option-item.active {
  background-color: #dc3545;
  color: white;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .filter-item {
    flex-direction: column;
  }
  
  .filter-label {
    width: 100%;
    margin-bottom: 10px;
  }
}
</style> 