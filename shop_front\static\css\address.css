/* 收货地址管理样式 */
.address-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.address-card {
    position: relative;
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e6e6e6;
}

.address-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.address-card.default {
    border: 2px solid #dc3545;
}

.default-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #dc3545;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
}

.address-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.recipient-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.recipient-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.recipient-phone {
    color: #666;
}

.address-content {
    margin-bottom: 20px;
}

.address-detail {
    color: #666;
    line-height: 1.6;
}

.address-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

.btn-edit, .btn-delete, .btn-set-default {
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-edit {
    background: rgba(0, 123, 255, 0.1);
    color: #007bff;
    border: 1px solid #007bff;
}

.btn-delete {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid #dc3545;
}

.btn-set-default {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid #28a745;
}

.btn-edit:hover, .btn-delete:hover, .btn-set-default:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.add-address {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 200px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.add-address:hover {
    border-color: #dc3545;
    background: #fff;
}

.add-icon {
    font-size: 40px;
    color: #dc3545;
    margin-bottom: 10px;
}

.add-text {
    color: #666;
    font-size: 14px;
}

/* 地址表单样式 */
.address-form {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    outline: none;
}

.address-region {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.form-check {
    margin-top: 15px;
}

.form-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

.btn-submit, .btn-cancel {
    padding: 10px 25px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-submit {
    background: #dc3545;
    color: white;
    border: none;
}

.btn-cancel {
    background: white;
    color: #666;
    border: 1px solid #dee2e6;
}

.btn-submit:hover, .btn-cancel:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* 确认删除弹窗样式 */
.modal-confirm {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    max-width: 400px;
    margin: 20px auto;
}

.confirm-icon {
    font-size: 50px;
    color: #dc3545;
    margin-bottom: 20px;
}

.confirm-title {
    font-size: 20px;
    color: #333;
    margin-bottom: 15px;
}

.confirm-message {
    color: #666;
    margin-bottom: 25px;
}

.confirm-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .address-list {
        grid-template-columns: 1fr;
    }
    
    .address-region {
        grid-template-columns: 1fr;
    }
    
    .form-buttons {
        flex-direction: column;
    }
    
    .btn-submit, .btn-cancel {
        width: 100%;
    }
} 